# Watermelon App - Architecture Links

## Core

### Models

*   [User Model](link_to_user_dart_file)
*   [Event Model](link_to_event_dart_file)
*   ...

### Services

*   [Supabase Service](link_to_supabase_service_dart_file)
*   ...



# Commits
- 1,2: Project setup, core files
- 3: getOneOnOneChatRoom method added to Chat section SupabaseService class
- 4: Updated to use UserVisibility
- 5: Updating SupabaseService class
- 6: Updating data models
- 7: Adding all repository classes and services folder






# Steps to building WM Complete Architecture

### 1. Project setup, folder structure, core files
- Create project folder structure
- Import project dependencies and dev-dependencies
  - `pubspec.yaml`
  - In terminal, run: `flutter pub get`
- Populate code in core files:
  - `main.dart`
  - `lib/core/errors.dart`
  - `lib/core/providers.dart`
  - `lib/core/routes.dart`
  - `lib/core/ui/states/loading_state.dart`

### 2. Data models
- Create files and populate code for all data models in `lib/core/models/` folder  
- In terminal, run: `flutter pub run build_runner build --delete-conflicting-outputs`
- This will generate the `.freezed.dart` and `.g.dart` files for each data model
- Other useful commands:
  - `flutter clean`
  - `flutter pub get`

### 3. SupabaseService class
- File: `lib/core/services/supabase_service.dart`
- The SupabaseService class contains methods that handle direct interaction with Supabase database. These methods catch Supabase errors and rethrow those errors to Repositories
- Methods are organized by sections (based on features):
  - Authentication
  - User
  - Friendships
  - Blocked Users
  - Hidden Users
  - Events
  - Wishlists
  - InterestedUser
  - Shared Activities
  - Categories
  - Chat
  - Notifications
  - Keywords
  - Saved User
  - Discover (Search)

### 4. Repository classes, LocationService class
- Path to repositories: `lib/core/services/...`
- Custom error classes: `lib/core/errors.dart`
- Providers: `lib/core/providers.dart`
- List of repository classes:
  - `auth_repository.dart`
  - `chat_repository.dart`
  - `event_repository.dart`
  - `keywords_repository.dart`
  - `notification_repository.dart`
  - `shared_activities_repository.dart`
  - `user_repository.dart`
  - `wishlist_repository.dart`
- LocationService class: `location_service.dart`


