
# 1. Introduction & Goals

## 1.1. Introduction

Watermelon is a mobile application designed to foster genuine human connections by connecting users based on shared interests, desired real-world activities, and geographic proximity. In an era often characterized by superficial online interactions and increasing social isolation despite digital connectivity, Watermelon aims to act as an antidote. It leverages technology not to replace real-world interaction, but to actively facilitate it, helping users bridge the gap between online discovery and offline engagement.

## 1.2. Problem Statement

Modern society faces challenges with loneliness and a lack of deep, meaningful connections, partly fueled by the nature of existing social platforms and the fast pace of digital life. People often struggle to find others who not only share their interests but also share their intent to participate in related activities. Existing platforms may focus solely on online interaction, large-scale public events, or dating with limited focus on shared activities as a foundation. This leaves a gap for individuals seeking platonic friendships, activity partners, or even dates specifically through the lens of shared real-world experiences.

## 1.3. Project Vision

The vision for Watermelon is to create a platform where users can easily discover compatible people nearby who are genuinely interested in doing the same activities. It aims to combat the passive scrolling and superficiality prevalent on other platforms by emphasizing:

* **Shared Intent:** Highlighting "Activities to Share" helps users find others with compatible goals for interaction.
* **Authentic Profiles:** Encouraging richer profiles through interests, activities to share, and a questionnaire provides deeper insight than basic demographics.
* **Facilitating Action:** Features like the user's activities and interests highlight on their profile card, Events and Wishlists are designed to directly enable users to plan and participate in activities together, acting as an "implicit call to action" and "meeting users halfway."
* **Meaningful Connections:** Ultimately fostering friendships and relationships grounded in shared experiences and genuine compatibility.

## 1.4. Goals (MVP Focus)

The primary goals for the Minimum Viable Product (MVP) are:

* **Establish Core User Base:** Implement robust user authentication, onboarding, and profile management.
* **Enable Discovery:** Provide effective location-based and interest-based search mechanisms to allow users to find relevant profiles nearby.
* **Facilitate Initial Connection:** Implement basic friendship request/management and one-on-one chat functionalities.
* **Introduce Activity Focus:** Implement the full Events and Wishlist features as the primary mechanism for shared activity engagement in the MVP.
* **Provide Essential Feedback:** Implement a core in-app notification system for key social interactions (waves, friend requests, non-friend messages, event/wishlist activity).
* **Ensure Foundational Stability:** Build upon a solid technical architecture (MVVM, Riverpod, Supabase) with clear error handling and basic security (RLS).

## 1.5. Target Audience (Implied)

Adults seeking to build new platonic friendships, find activity partners, or potentially find romantic connections based on shared interests and a desire for real-world interaction within their local or regional area.


# 2. MVP (Minimum Viable Product) Scope & Features

This section defines the features and functionalities **designed** for the Watermelon application. The goal for the initial implementation phase (MVP launch) is to build a functional application delivering the core value proposition. **All features listed within Section 2 are considered part of the complete application design to be documented here.** Implementation priority (e.g., Events before Wishlists) will be managed separately in the Task List.

## 2.1. Core Foundation

### 2.1.1. Authentication

Handles user account creation, login, and session management using Supabase Auth.

* **Email/Password Signup:**
    * Allows new users to register using an email address and password.
    * Requires email format validation and minimum password length (6 characters).
    * Initiates Supabase Auth `signUp` process.
    * **Requires Email Verification:** Users must click a confirmation link sent via email (using Supabase's default confirmation UI/webpage for now). Login is blocked until email is verified.
    * **Minimal User Record Creation:** Upon successful *auth* signup (before email verification is complete), a corresponding record is created in the `public."Users"` table containing only the `user_id` (matching `auth.users.id`), the `email`, an empty `username`, and `onboarding_complete` set to `false`.
    * Requires passing `emailRedirectTo` (matching configured allowed URLs) during the `signUp` call (implementation detail for AuthRepository).
* **Email/Password Login:**
    * Allows registered and verified users to log in.
    * Requires email and password input.
    * Uses Supabase Auth `signInWithPassword`.
    * On success, determines whether to navigate to the Onboarding flow or the Home screen based on the user's `onboarding_complete` status (logic handled by navigation guards later).
* **Password Reset:**
    * Allows users to request a password reset link via email.
    * Requires email input.
    * Uses Supabase Auth `resetPasswordForEmail`.
    * Requires passing `redirectTo` (matching configured allowed URLs) for the password reset email link.
    * Uses Supabase's default web page for handling the password update (deep linking implementation deferred).
* **Logout:**
    * Allows logged-in users to sign out.
    * Clears the Supabase Auth session (`AuthRepository.signOut`).
    * Clears relevant local user state/cache.
    * Navigates the user back to the initial Welcome/Login screen.

### 2.1.2. Onboarding

A mandatory multi-step process triggered after a user's first successful login (following signup and email verification) if their `onboarding_complete` flag is false. Managed by `OnboardingScreen` (using `PageView`) and `OnboardingViewModel`.

* **Step 1: Welcome:** Brief introductory screen explaining the app's purpose.
* **Step 2: Name & Username (`_buildNameUsernamePage`):**
    * Collects `fullName` (Required, validated for non-empty).
    * Collects `username` (Required, validated for format: min 3 chars, max 20 chars, no spaces, allowed characters `a-zA-Z0-9_`).
    * Performs **Username Availability Check** against the database via `OnboardingViewModel` (using debouncing). UI provides feedback (checking/available/taken/error). User cannot proceed if username fails validation or is unavailable.
* **Step 3: Date of Birth & Gender (`_buildDobGenderPage`):**
    * Collects `birthdate` (Required, using standard date picker). User's `age` is calculated and stored later.
    * Collects `gender` (Required, using `RadioListTile` options: 'Male', 'Female', 'Other', 'Prefer not to say').
* **Step 4: Profile Picture (`_buildProfilePicturePage`):**
    * Requires the user to set an initial avatar. Options provided via the `ProfileImagePicker` widget:
        * Upload from Gallery/Camera (with 1:1 cropping and compression).
        * Select a single Default Asset (`assets/images/defaults/default_avatar.png`).
        * Use Generated Initial Avatar (based on `fullName`, allows background color regeneration/selection).
    * Stores the selection type (`avatarType`: 'uploaded', 'default', 'generated') and relevant data (`XFile? profileImage`, `String? defaultAvatar`, `Color? generatedAvatarColor`) in the `OnboardingState`.
* **Step 5: Location (`_buildLocationPage`):**
    * Requires the user to provide location information using the `CitySelectorWidget`:
        * Option 1: Grant "While Using App" location permission. Fetches current `GeoPoint` using `LocationService` (`geolocator`). Derives `city` and `country` via reverse geocoding (`geocoding`).
        * Option 2: Manually enter `city`. Fetches suggestions via `LocationService` (`flutter_osm_plugin`). Derives `GeoPoint?` and `country` via forward geocoding (`geocoding`).
    * Stores `GeoPoint? location`, `String? city`, `String? country` in `OnboardingState`. Requires either `location` or `city` to be non-null to proceed.
* **Step 6: Shared Activities (`_buildSharedActivitiesPage`):**
    * Requires the user to select **1 to 5** predefined activities using the `SharedActivitiesSelectorWidget`.
    * Widget allows searching/Browse activities grouped by category (via bottom sheet) and displays selections as reorderable chips.
    * Stores selected `activity_id`s (`List<String>`) in `OnboardingState`.
* **Step 7: My Interests (`_buildMyInterestsPage` - Optional):**
    * Allows the user to select/add **0 to 5** interest keywords using the `InterestsSelectorWidget`.
    * Allows selection from predefined keywords (autocomplete) and addition of custom keywords (max 2, with fuzzy matching suggestions).
    * Selected keywords are displayed as reorderable chips.
    * User can skip this step.
    * Stores selected keywords (`List<String>`) in `OnboardingState`.
* **Step 8: Summary (`_buildSummaryPage`):**
    * Displays all information collected in previous steps.
    * Provides "Edit" buttons next to each section, allowing navigation back to the corresponding step using `PageController.animateToPage`.
    * Contains the "Complete Onboarding" button.
* **Completion (`OnboardingViewModel.completeOnboarding`):**
    * Triggered by "Complete Onboarding" button.
    * Performs final validation on all required state fields.
    * Handles profile picture upload to Supabase Storage (`user-images` bucket, path `userId/filename`) if an image file was selected.
    * Constructs the final `User` object with all collected data (including calculated `age`, `avatarType`, `generatedAvatarColor` hex, and `onboarding_complete = true`).
    * Calls `UserRepository.updateUser` to update the minimal user record in the Supabase `Users` table.
    * On successful database update, sets the `onboarding_complete_[userId]` flag to `true` in `SharedPreferences`.
    * Navigates the user to the main application screen (`/home` or `/discover`).
    * Handles and displays errors appropriately during the process.

### 2.1.3. Navigation Setup (Beamer)
* Uses `Beamer` for declarative, URL-based routing.
* Routes organized into `BeamLocation` classes (e.g., `AuthLocation`, `OnboardingLocation`, `HomeLocation` containing most other routes).
* `BeamerDelegate` configured with `BeamerLocationBuilder` listing all locations.
* Handles path parameters (e.g., `/profile/:userId`, `/events/:eventId`).
* **Guards:** `AuthGuard` and `OnboardingGuard` are part of the design to control access based on login state and onboarding completion. Their *implementation* is deferred to the main project build phase, but the routing structure accounts for their eventual addition.

## 2.2. Core Profile & Discovery

This section covers how users manage their own profiles, view others', handle blocking/hiding, and discover other users through location and search.

### 2.2.1. User Profile Data & Display
* **Data Foundation:** Utilizes the `User` model (Dart) and the `Users` table (Supabase) which stores all profile attributes and is linked to `auth.users`.
* **`UserProfileCard` Widget:** A reusable widget for displaying user summaries.
    * **Medium Size:** Used in lists (Discover results bottom sheet, Saved Profiles). Layout includes an Image Carousel placeholder (with functional Save/Unsave heart icon overlay), Full Name, Age, Gender, City, Shared Activities chips (up to 5), and My Interests chips (up to 5).
    * **Condensed Size:** Used as an overlay on the Discover map when a marker is tapped. Layout includes an image filling the left area, Full Name, Age, Gender, City, and limited Shared Activities chips (first 2-3). Tappable to navigate to the full profile.
* **`ProfileScreen` (Placeholder & Route):**
    * Route `/profile/:userId` is defined, accepting a `userId`.
    * A placeholder screen exists.
    * The full implementation (displaying all user details based on Figma design, including questionnaire answers if applicable, action buttons like Add Friend/Message/Wave) is part of the main project build, not the `watermelon_draft` structure.
* **Save/Unsave Users:**
    * Functionality to save/unsave user profiles is implemented in the `UserRepository` and `SupabaseService`, interacting with the `SavedUsers` table.
    * The heart icon on the medium `UserProfileCard` provides the UI interaction point.

### 2.2.2. Account Management (`AccountDashboard` Placeholder)
* **Route:** `/account` route defined, points to `AccountDashboard` placeholder screen.
* **Designed Functionality (Logic Implemented, UI Placeholder):**
    * Display current user's basic info (Avatar, Full Name, Username).
    * **Edit Profile Button:** Navigates to `/edit-profile` (placeholder screen).
    * **View Profile Button:** Navigates to the current user's own `/profile/:userId` (placeholder screen).
    * **Discoverability Toggle:** Functional `Switch` connected to `AccountViewModel` and `UserRepository.updateDiscoverability` to update the `discoverable` field in Supabase. Explanatory text included.
    * **Shared Activities Quick Edit:** Displays current selections in a `ListTile`. Tapping it opens the Shared Activities selection bottom sheet (using the logic encapsulated in `SharedActivitiesSelectorWidget`). Changes are saved via `UserRepository.updateUser` when the user leaves the `AccountDashboard` (if changes were made and discoverability is on).
    * **Blocked Users Button:** Navigates to `/blocked-users` (placeholder screen).
    * **Hidden Users Button:** Navigates to `/hidden-users` (placeholder screen).
    * **Sign Out Button:** Functional button calling `AuthRepository.signOut` and navigating to `/welcome`.

### 2.2.3. Blocking & Hiding Users
* **Backend Logic:** Full implementation in `UserRepository` and `SupabaseService` for blocking/unblocking (updates `Friendships` status) and hiding/unhiding (CRUD operations on `HiddenUsers` table). RLS policies are in place.
* **Search/List Exclusion:** Blocked and hidden users are correctly excluded from `searchUsers`, `searchUsersByLocation`, `getAttendees`, `getInterestedUsers`, and `getFriends` results via Supabase query modifications (likely using the `UserVisibility` view or `NOT EXISTS`).
* **UI Placeholders & Deferred Implementation:**
    * `BlockedUsersScreen` and `HiddenUsersScreen` routes and placeholder screens exist.
    * The UI implementation for *displaying* blocked/hidden status on `ProfileScreen` and providing the Block/Hide/Unblock/Unhide actions (likely in a three-dot menu or button) is deferred to the main project build.

### 2.2.4. Location Services & Discover Map
* **`LocationService`:** Implemented using `permission_handler`, `geolocator`, `geocoding`. Returns `Either` for fallible operations. Known iOS simulator issues are documented and workarounds exist for `watermelon_draft`.
* **`DiscoverViewModel`:** Implemented using `AsyncNotifier`. Manages `DiscoverState` (map center, zoom, radius, user list, etc.). Handles initial location fetch (GPS -> Profile City -> Throw), debounced user fetching based on map movement (`_fetchUsersForArea`), and state updates.
* **`DiscoverDashboard`:**
    * Displays `OSMFlutter` map, centered correctly based on ViewModel state. ✅
    * Displays custom avatar markers for `nearbyUsers` using `MapController.addMarker`/`removeMarker`. ✅
    * Opens modal profile sheet (`_showUserProfileSheet` displaying `ProfileScreen`) on marker tap using `onGeoPointClicked`. ✅
    * Displays persistent results bottom sheet (`DraggableScrollableSheet`) with list of medium `UserProfileCard`s. ✅
    * Collapses results bottom sheet on map background tap (using `onSingleTap` mixin method). ✅
    * FABs (Center Location, Search Users) show/hide correctly based on results sheet expansion state (managed via `_sheetController` listener and ViewModel). ✅
    * AppBar buttons navigate to respective placeholder screens (`/city-search`, `/filter`, `/notifications/discover`). ✅
    * *Deferred Refinement:* Minor bottom sheet dragging inconsistency.

### 2.2.5. Search Components
* **`CitySearchPage`:** Placeholder screen and route (`/city-search`) defined. Designed to include city input (with autocomplete via `LocationService`), recent searches, distance slider, and return selection. Implementation deferred.
* **`FilterPage`:** Placeholder screen and route (`/filter`) defined. Designed to include age range slider, gender radio buttons, reset/apply buttons, and return selection. Implementation deferred.
* **`SearchPage`:** Placeholder screen and route (`/search`) defined. Designed to integrate Name/Username search, Shared Activities selection, My Interests selection, distance filter (radio buttons), active filter display (chips with removal), and trigger interest-based search via `UserRepository.searchUsers`. Implementation deferred.

## 2.3. Core Social Features

This section details the fundamental social interactions within the Watermelon app, focusing on friendships and basic communication.

### 2.3.1. Friendships

Manages user connections, requests, and status.

* **Backend & Data:**
    * `Friendships` table stores relationships using `user_a_id`, `user_b_id`, and bidirectional statuses (`status_a_to_b`, `status_b_to_a`). Statuses include 'none', 'pending', 'received', 'accepted', 'blocked'. Includes timestamps for creation and request sending. Uses a UUID primary key.
    * RLS policies allow users to manage and view relationships they are part of.
    * `UserRepository` provides methods: `sendFriendRequest`, `acceptFriendRequest`, `declineFriendRequest`, `cancelFriendRequest`, `getFriendshipStatus`, `areUsersFriends`, `getFriendIds`, `getFriends`.
    * `getFriends` method explicitly excludes users where the friendship status involves 'blocked'.
* **UI & Interaction:**
    * **Profile Screen Action Buttons:** The action buttons displayed on another user's `ProfileScreen` dynamically change based on the friendship status obtained via `UserRepository.getFriendshipStatus`:
        * "Add Friend" (if status is 'none').
        * "Request Sent" (if status is 'pending' from current user).
        * "Accept Request" / "Decline Request" buttons (if status is 'received' by current user).
        * "Friends" indication (if status is 'accepted'), possibly enabling "Message" and "Wave" buttons.
        * Indication of blocked status (if status is 'blocked').
        * *(Note: Full implementation of these button states and actions occurs in the main project build within `ProfileScreen`)*.
    * **Friend Request Notifications:** Sending, accepting, and declining requests trigger in-app notifications (`friend_request`, `friend_accept` types - *Self-correction: Need to ensure `friend_accept` type was defined*).
    * **`FriendsDashboard`:** Route defined (e.g., `/friends`, likely a tab within `HomeScreen`). Placeholder screen exists. Designed with tabs for "Chat", "Friends", "Saved Profiles".
    * **`FriendsListScreen`:** Implementation of the "Friends" tab within `FriendsDashboard`. Designed to fetch accepted friends via `UserRepository.getFriends` and display them using condensed `UserProfileCard` widgets.

### 2.3.2. One-on-One Chat

Facilitates direct messaging between two users.

* **Backend & Data:**
    * `ChatRooms` table stores room metadata. For 1-on-1 chats, `event_id` and `wishlist_item_id` are NULL. Might store participant IDs or rely on join table.
    * `ChatRooms_Users` join table links users to rooms and tracks `last_read_at`.
    * `Messages` table stores message content, sender ID, and timestamp.
    * `ChatRepository` provides methods: `getChatRooms` (for chat list), `getMessages` (paginated), `sendMessage`, `markAsRead` (updates `last_read_at`), `getOrCreateChatRoom`, `addUserToChatRoom`, `removeUserFromChatRoom`.
    * `getOrCreateChatRoom` logic handles finding an existing 1-on-1 room or creating a new one and adding both participants to `ChatRooms_Users`.
* **Real-time:** Basic real-time subscription for new messages in *currently open* chat rooms using Supabase Realtime is part of the MVP design (ViewModel implementation). Full background real-time updates might be post-MVP.
* **UI Placeholders & Design:**
    * **`ChatListScreen`:** Implementation of the "Chat" tab within `FriendsDashboard`. Designed to display a list of recent conversations (1-on-1 and group) with avatar, name, last message snippet, timestamp, and unread indicator (derived from `last_read_at`).
    * **`ChatScreen`:** Route defined (`/chat/:chatRoomId`). Placeholder exists. Designed to display paginated messages, an input field (`TextFormField`), and a send button. UI updates handled by ViewModel.
* **Integration:**
    * Sending a message to a non-friend triggers a `new_message_nonfriend` notification.
    * Blocked users cannot send/receive messages to/from each other (checked via `Friendship` status before sending/displaying).
    * Tapping "Message" button on a `ProfileScreen` (if friends or allowed) calls `getOrCreateChatRoom` and navigates to the `ChatScreen`.

## 2.4. Events and Wishlists

These features are central to facilitating shared real-world activities and connections. Both feature sets are fully designed as documented below. For the initial MVP launch, **Events will be implemented first**, followed by Wishlists in a subsequent phase of the MVP build cycle.

### 2.4.1. Events (Initial Implementation Focus)

Provides functionality for users to create, discover, join, and manage real-world events.

* **Backend & Data:**
    * `Events` table stores event details, including `creator_id`, `event_name`, `description`, `category_id`, `event_date` (timestamp), `place_name`, `place_location` (GeoPoint), optional `capacity`, optional `image_url`, link to `chat_room_id`, and `status` ('draft', 'published', 'cancelled').
    * `Attendees` join table links `user_id` and `event_id`, storing `joined_at` and `join_count`.
    * `Categories` table provides predefined categories for events.
    * `EventRepository` provides methods for CRUD operations (`createEvent`, `updateEvent`, `deleteEvent`, `getEventById`), searching/fetching (`getEventsByLocation`, `getMyEvents`, `getHostedEvents`, `getAttendingEvents`, `getDraftEvents`, `getPastEvents`), attendance management (`joinEvent`, `leaveEvent`, `getAttendees`, `isUserAttendingEvent`), category fetching (`getCategories`, `getCategoryById`), and image handling (`uploadEventImage`).
    * Event creation automatically creates an associated `ChatRoom` and links it via `chat_room_id`.
* **Core Flow & UI Design:**
    * **Event Creation (`CreateEventScreen` - Placeholder):** Form allows users to input event details (name, description, category, date/time, place name [with autocomplete], optional capacity, optional image upload). Includes "Save as Draft" and "Publish" options.
    * **Event Browse (`EventsDashboard` "Browse" Tab - Placeholder):** Displays a list or grid of `EventCard` widgets based on location (from City Search) and date filters.
    * **Event Detail View (`EventDetailPage` - Placeholder):** Displays comprehensive event information (image banner, name, host info, date/time, location map link/directions, description, category, attendee count/list). Provides "Join Event" / "Leave Event" button (state changes based on attendance). Includes "Messages" tab linking to the event's chat. For the host, provides "Edit Event" and "Cancel Event" / "Delete Draft" options.
    * **My Events (`EventsDashboard` "My Events" Tab - Placeholder):** Displays events related to the current user, categorized under headings: "I'm Hosting", "I'm Going", "Draft Events", "Past Events". Uses `EventCard` widgets. Displays notification indicators on cards for new activity.
    * **Event Editing (`EditEventScreen` - Placeholder):** Allows the event host to modify details of their events.
    * **`EventCard` Widget:** Reusable widget displaying key event information concisely (image, name, date, location, category, attendee count).
* **Chat Integration:** Joining an event adds the user to the associated `ChatRoom` (via `ChatRepository.addUserToChatRoom`). Leaving removes them. The "Messages" tab on `EventDetailPage` navigates to the `ChatScreen` for that specific event.
* **Notifications:** Triggers in-app notifications for `new_attendee` (to host), `event_update` (to attendees), `event_message` (to attendees), `event_cancelled` (to attendees).

### 2.4.2. Wishlists (Subsequent Implementation Focus)

Enables users to share desired activities or goals and connect with friends who share those interests.

* **Backend & Data:**
    * `WishlistItems` table stores item details, including `creator_id`, `item_name`, `description`, optional `item_url`, `category_id`, mutually exclusive date fields (`event_date` [timestamp] or `general_date` [text]), `place_name`, `place_location` (GeoPoint), link to `chat_room_id`, and `status` ('draft', 'published', 'completed').
    * `InterestedUsers` join table links `user_id` and `wishlist_item_id`, storing the user's interest `status` ('interested', 'lets_go', 'going').
    * `Categories` table provides predefined categories for wishlist items.
    * `WishlistRepository` provides methods for CRUD operations (`createWishlistItem`, `updateWishlistItem`, `deleteWishlistItem`, `getWishlistItemById`), fetching (`getFeedItems` [for friends], `getMyWishlistItems`), interest management (`expressInterest`, `getInterestedUsers`, `isUserInterested`), and potentially category fetching.
    * Wishlist item creation automatically creates an associated `ChatRoom` and links it via `chat_room_id`.
* **Core Flow & UI Design:**
    * **Wishlist Creation (`CreateWishlistItemScreen` - Placeholder):** Form allows users to input item details (name, description, URL, category, specific date OR general date, place name [with autocomplete]). Includes "Save as Draft" and "Publish" options.
    * **Wishlist Feed (`WishlistsDashboard` "Feed" Tab - Placeholder):** Displays a paginated list of `WishlistCard` widgets showing items created by the user's friends.
    * **Wishlist Detail View (`WishlistItemDetailPage` - Placeholder):** Displays comprehensive item information (name, creator info, description, URL, date, location map link/directions, category, list of interested users/statuses). Provides buttons for the current user to express interest ("Interested", "Let's Go", "Going"). Includes "Messages" tab linking to the item's chat. For the creator, provides "Edit Item" and "Mark as Completed" / "Delete Draft" options. Includes a "Convert to Event" button.
    * **My Wishlists (`WishlistsDashboard` "My Wishlists" Tab - Placeholder):** Displays wishlist items related to the current user, categorized under headings: "I'm Hosting", "I'm Interested" (showing user's status), "Draft Wishlists", "Past/Completed Wishlists". Uses `WishlistCard` widgets. Displays notification indicators.
    * **Wishlist Editing (`EditWishlistItemScreen` - Placeholder):** Allows the item creator to modify details.
    * **`WishlistCard` Widget:** Reusable widget displaying key item information concisely (name, creator avatar, category, date, location snippet).
* **Chat Integration:** Expressing significant interest (e.g., 'Let's Go' or 'Going') adds the user to the associated `ChatRoom`. Removing interest removes them. The "Messages" tab on `WishlistItemDetailPage` navigates to the `ChatScreen`.
* **Notifications:** Triggers in-app notifications for `new_interest` (to creator), `wishlist_update` (to interested users), `wishlist_message` (to interested users).

## 2.5. Notifications (In-App Only for MVP Implementation)

Provides users with timely updates about relevant activity within the app. The initial MVP focuses on *in-app* notifications; the integration work for *push* notifications is deferred to the end of the MVP build cycle.

* **Backend & Data:**
    * `UserNotifications` table stores all notifications with fields: `notification_id`, `user_id` (recipient), `notification_type`, `content`, `is_read`, `related_user_id`, `event_id`, `wishlist_item_id`, `chat_room_id`, `created_at`.
    * RLS policies ensure users can only access/manage their own notifications.
    * `NotificationRepository` provides methods for creating, fetching (all/unread), counting (unread), marking as read (single/all), and deleting notifications.
* **MVP Notification Types & Triggers:**
    * **Discover/Social:**
        * `new_wave`: Created when `UserRepository.sendWave` is called.
        * `friend_request`: Created when `UserRepository.sendFriendRequest` is called.
        * `friend_accept`: Created when `UserRepository.acceptFriendRequest` is called.
        * `new_message_nonfriend`: Created when `ChatRepository.sendMessage` is called between users who are not friends.
    * **Events:**
        * `new_attendee`: Created when `EventRepository.joinEvent` is called (sent to event host).
        * `event_message`: Created when `ChatRepository.sendMessage` is called within an event's chat room (sent to other attendees/host).
        * `event_cancelled`: Created when host cancels event (sent to attendees). *(Note: Event update notifications deferred)*.
    * **Wishlists:**
        * `new_interest`: Created when `WishlistRepository.expressInterest` is called with status 'interested', 'lets_go', or 'going' (sent to item creator).
        * `wishlist_message`: Created when `ChatRepository.sendMessage` is called within a wishlist item's chat room (sent to other interested users/creator).
        * `wishlist_completed`: Created when creator marks item as completed. *(Note: Wishlist update notifications deferred)*.
* **UI Integration:**
    * **Badges:** Unread notification counts displayed as badges on relevant `BottomNavigationBar` items (Discover, Events/Wishlists, Friends/Chat) and potentially an `AppBar` icon. Logic uses `ref.watch` on providers that return counts from `NotificationRepository`.
    * **Notification Screens:**
        * `DiscoverNotificationsPage`: Displays `new_wave`, `friend_request`, `friend_accept`, `new_message_nonfriend` types. Accessed via Discover `AppBar`.
        * `EventNotificationsPage` (route `/events/:eventId/notifications`): Displays event-specific types (`new_attendee`, `event_message`, `event_cancelled`). Accessed potentially via `EventCard` badge or `EventDetailPage`.
        * `WishlistItemNotificationsPage` (route `/wishlists/:wishlistId/notifications`): Displays wishlist-specific types (`new_interest`, `wishlist_message`, `wishlist_completed`). Accessed potentially via `WishlistCard` badge or `WishlistItemDetailPage`.
    * **Functionality:** Screens list notifications (using a standard list item UI), allow tapping to navigate to the relevant context (profile, chat, event, wishlist), mark notifications as read upon tap, and allow swipe-to-delete.
* **Push Notifications (FCM/APNS):** The *infrastructure* for creating notifications exists, but the **client-side setup and integration with FCM/APNS are deferred** implementation tasks within the MVP build cycle.

## 2.6. Core Technology & Architecture Summary

* **Frontend Framework:** Flutter (targeting iOS and Android initially).
* **Backend:** Supabase Platform.
    * **Database:** PostgreSQL (via Supabase DB) with PostGIS extension enabled.
    * **Authentication:** Supabase Auth (Email/Password, Email Verification).
    * **Storage:** Supabase Storage (for user profile images, event images).
    * **Realtime:** Supabase Realtime (for basic chat functionality in MVP).
* **State Management:** Riverpod 2.x (using code generation via `@riverpod`, `AsyncNotifier` / `Notifier`).
* **Architecture:** MVVM (Model-View-ViewModel), Repository Pattern, Service Layer.
* **Routing:** Beamer (declarative routing, `BeamLocation` structure).
* **Data Modeling:** `freezed` / `freezed_annotation` (immutable models), `json_serializable` / `json_annotation` (JSON conversion).
* **Error Handling:** `fpdart` (`Either`), Custom `Failure` classes.
* **Key Packages:** `supabase_flutter`, `flutter_riverpod`, `riverpod_annotation`, `freezed_annotation`, `beamer`, `fpdart`, `permission_handler`, `geolocator`, `geocoding`, `flutter_osm_plugin` (for map display/address suggestion), `image_picker`, `image_cropper`, `cached_network_image`, `timeago`, `uuid`, `collection`, `rxdart`, `modal_bottom_sheet`.


# 3. Post-MVP / Deferred Features

This section outlines features, enhancements, and technical work planned for future iterations *beyond* the completion of the fully designed MVP scope (which includes Core Auth/Onboarding/Profile/Discovery/Social, full Event design, full Wishlist design, core Chat, core In-App Notifications, Push Notification infrastructure, and Deep Linking infrastructure).

## 3.1. Feature Enhancements & New Functionality

* **Advanced Group Chat Management:** Features beyond basic event/wishlist chat, such as manual member management, chat settings (naming, topics, muting), admin controls.
* **Advanced Real-time Features:** Typing indicators, read receipts, detailed online presence status in chat and elsewhere.
* **Recurring Events:** Functionality for creating repeating events.
* **Event/Wishlist Waitlists:** Managing sign-ups when capacity is reached.
* **Event/Wishlist Reviews & Ratings:** Allowing user feedback on completed activities.
* **Questionnaire-Based Matching:** Algorithms and UI displaying compatibility scores or insights based on questionnaire answers.
* **Advanced Discovery & Recommendations:** Proactive suggestions for users, events, or wishlists; more complex search/filter options (e.g., neighborhood filters, advanced keyword logic).
* **User & Content Reporting/Moderation:** Implementing systems for users to report content/profiles and tools for moderation.
* **Profile Verification:** Formal processes for verifying user identity.
* **Advanced Privacy Controls:** More granular user settings for data visibility.
* **Refined Friendship Levels:** Potential concepts like "Close Friends" lists or statuses.
* **Admin Panel/Tools:** Dedicated interface for app administration (user management, content management, keyword/category approval, analytics).
* **Monetization Strategy:** Design and implementation of potential revenue models (if pursued).

## 3.2. Technical & Platform Enhancements

* **Comprehensive Testing:** Significant expansion of automated testing coverage (Unit, Widget, Integration, E2E).
* **Performance Optimization:** Dedicated efforts for profiling and optimizing performance across the app (database, UI, network).
* **CI/CD Pipeline:** Setting up automated build, test, and deployment processes.
* **Offline Support:** Implementing strategies for data caching and offline functionality (if required).
* **Accessibility (a11y):** Thorough accessibility review and implementation improvements.
* **Internationalization/Localization (i18n/l10n):** Adapting the app for multiple languages and regions.
* **Platform-Specific Refinements:** Addressing known minor issues (e.g., `flutter_osm_plugin` simulator behavior) and ensuring smooth native platform integration.
* **UI Polish:** Refining complex animations and interactions (e.g., Discover bottom sheet drag behavior).

# 4. Core Architecture

This section details the architectural patterns, key libraries, and structural decisions guiding the development of the Watermelon application. The goal is to create a scalable, maintainable, testable, and robust codebase.

## 4.1 Overview

Watermelon utilizes a **Model-View-ViewModel (MVVM)** architecture pattern facilitated by the **Riverpod** state management library. Data persistence, authentication, and storage are handled by **Supabase** as the Backend-as-a-Service (BaaS). Navigation is managed declaratively using the **Beamer** package. Data modeling relies on the **freezed** package for immutable classes, complemented by **json_serializable**. Error handling follows functional programming principles using **fpdart**'s `Either` type and custom `Failure` classes.

The core layers are:

* **View:** Flutter Widgets displaying UI and capturing user input.
* **ViewModel:** Contains UI logic, manages state (via Riverpod Notifiers), and interacts with Repositories.
* **Repository:** Abstracts data operations for specific domains (User, Event, etc.), interacting only with Services.
* **Service:** Encapsulates direct interactions with external systems (Supabase client, device location services).
* **Model:** Represents the application's data structures.

## 4.2 Model-View-ViewModel (MVVM)

* **Model:** Represents the application's data state. Defined using immutable classes generated by the `freezed` package (e.g., `User`, `Event`, `WishlistItem`). These models often include `fromJson`/`toJson` methods (via `json_serializable`) for database interaction.
* **View (UI):** Composed of Flutter `StatelessWidget`s and `ConsumerWidget`s / `ConsumerStatefulWidget`s. Responsible for:
    * Displaying data exposed by the ViewModel's state.
    * Reacting to state changes (using `ref.watch` and pattern matching on `AsyncValue`).
    * Forwarding user input events (button taps, form submissions) to the ViewModel (using `ref.read().notifier.method()`).
    * Handling UI-specific side effects like navigation or showing SnackBars (often triggered via `ref.listen`).
* **ViewModel (Logic & State Holder):** Implemented using Riverpod's code-generated `Notifier` (for synchronous state) or, more commonly, `AsyncNotifier` (for asynchronous state). Responsible for:
    * Holding and managing the UI state for a specific screen or feature (typically as an `AsyncValue<SomeStateObject>`).
    * Exposing methods for the View to call in response to user actions.
    * Containing presentation logic (e.g., formatting data for display - though often delegated to the View or helper functions).
    * Interacting with one or more `Repository` classes to fetch or manipulate data.
    * Updating its own state (`state = ...`) to reflect data changes or loading/error conditions, triggering UI rebuilds via Riverpod.

## 4.3 State Management (Riverpod 2.x with Code Generation)

Riverpod is used for both state management and dependency injection.

* **Code Generation (`@riverpod`):** All providers (for Services, Repositories, ViewModels) are defined using the `@riverpod` annotation and associated code generation. This ensures type safety, simplifies provider definition, and enables features like `.notifier` access.
* **Dependency Injection (DI):** Providers `watch` or `read` other providers to get their dependencies (e.g., a ViewModel `watch`es a Repository, a Repository `watch`es `SupabaseService`).
* **State Management (`AsyncNotifier`):** ViewModels typically extend `AsyncNotifier<T>` where `T` is the primary data type for the screen (e.g., `AsyncNotifier<List<Event>>`, `AsyncNotifier<DiscoverState>`). Riverpod automatically manages the `AsyncValue` states (`AsyncLoading`, `AsyncData`, `AsyncError`).
* **Reactivity:** Widgets use `ref.watch` to subscribe to provider state changes and rebuild automatically. `ref.listen` is used for side effects based on state changes.
* **Caching (`keepAlive: true`):** Used for providers whose state should persist globally even when not actively watched (e.g., `sharedActivitiesProvider`, `currentUserProfileProvider`).

## 4.4 Data Access Layer (Repository & Service)

A two-layer approach isolates data source details from the application logic.

* **Repositories:**
    * Interface definition for data operations related to a specific domain (e.g., `UserRepository`, `EventRepository`).
    * Abstract away Supabase-specific implementation details.
    * Depend *only* on the `SupabaseService` (or other specific services like `LocationService`).
    * Handle errors returned from the Service layer. Convert exceptions into specific `Failure` types wrapped in `fpdart`'s `Either`.
    * Return type is always `Future<Either<Failure, T>>` for potentially fallible operations.
* **Services:**
    * **`SupabaseService`:** The *only* class that directly interacts with the `SupabaseClient` instance (`Supabase.instance.client`). Contains methods for all Supabase operations (Auth, Database CRUD, Storage uploads/downloads). Uses `try...catch` and `rethrows` exceptions to be handled by the calling Repository.
    * **`LocationService`:** Encapsulates interactions with location-related packages (`permission_handler`, `geolocator`, `geocoding`) and potentially `flutter_osm_plugin` (for `addressSuggestion`). Returns `Future<Either<Failure, T>>` or `Future<bool>` depending on the operation.

## 4.5 Routing (Beamer)

* **Declarative Navigation:** Uses `Beamer` for URL-based, declarative navigation.
* **`BeamLocation`:** Routes are organized into logical feature groups using classes extending `BeamLocation` (e.g., `AuthLocation`, `HomeLocation`, `EventsLocation`).
* **`BeamerDelegate` & `BeamerLocationBuilder`:** Configured in `main.dart` and `routes.dart` to manage the location stack.
* **Path Parameters:** Used for routes requiring dynamic IDs (e.g., `/profile/:userId`, `/events/:eventId`).
* **Guards (Deferred):** `BeamGuard`s will be implemented in the main project to handle authentication and onboarding status checks, redirecting users appropriately.
* **Deep Linking (Deferred):** Planned for MVP scope but implementation deferred to main project. `redirectTo` parameters added to relevant `AuthRepository` methods.

## 4.6 Data Modeling (Freezed & json_serializable)

* **Immutability:** All data model classes (in `lib/core/models/`) use the `@freezed` annotation to generate immutable classes.
* **Code Generation:** Relies on `build_runner` to generate `.freezed.dart` (for constructors, `copyWith`, equality, `toString`) and `.g.dart` (for `fromJson`/`toJson` via `json_serializable`).
* **Syntax:** Requires `abstract class` and `const factory` constructors.
* **JSON Handling:** Uses `json_serializable` for seamless conversion between Dart objects and JSON for Supabase interaction. `@JsonKey` is used for custom conversions (like `GeoPoint`) or name mapping.

## 4.7 Error Handling (`fpdart`, Custom Failures)

* **Functional Approach:** Uses `fpdart`'s `Either<Failure, T>` type as the return type for all potentially fallible operations in the Repository layer.
* **`Failure` Base Class:** An abstract `Failure` class is defined in `core/errors.dart`.
* **Specific Failure Subclasses:** Concrete subclasses (`AuthFailure`, `DatabaseFailure`, `PostgresqlFailure`, `LocationFailure`, `GeocodingFailure`, etc.) provide more context about the error type.
* **ViewModel Handling:** ViewModels use `.fold` (or sometimes pattern matching) on the `Either` result from repositories to update their `AsyncValue` state (`AsyncError` or `AsyncData`).
* **UI Display:** Widgets use `ref.watch(...).when(...)` or `switch (stateAsync)` to handle `AsyncError` states and display user-friendly messages (often via `SnackBar`). `ref.listen` is also used for error-triggered side effects.

## 4.8 Folder Structure

* **Feature-First:** Code is primarily organized by feature (`lib/features/auth`, `lib/features/discover`, etc.).
* **Core:** `lib/core/` contains shared elements: `models`, `services`, `repositories` (or merged into services), `providers`, `utils`, `ui` (shared widgets/states), `routes.dart`, `errors.dart`.
* **Widgets:** `lib/widgets/` contains general-purpose reusable widgets not tied to a specific feature.

# 5. Database Schema (Supabase/PostgreSQL)

This section outlines the structure of the database tables designed for the Watermelon application, hosted on Supabase PostgreSQL. Table and column names follow `snake_case`. Row Level Security (RLS) is enabled on all tables accessed directly by users unless noted otherwise.

**Extensions Required:** `uuid-ossp`, `postgis`, `postgis_topology`

---

### 5.1. `public."Users"`

* **Description:** Stores user profile information, extending `auth.users`.
* **RLS:** Enabled. Policies allow authenticated users to SELECT all profiles, INSERT own record, UPDATE own record. DELETE disallowed.
* **Columns:**
    * `user_id`: `uuid` (PK, FK to `auth.users(id)` ON DELETE CASCADE, NOT NULL)
    * `username`: `text` (Unique, Nullable initially)
    * `email`: `text` (Unique, NOT NULL)
    * `full_name`: `text` (Nullable)
    * `age`: `int4` (Nullable)
    * `gender`: `text` (Nullable)
    * `city`: `text` (Nullable)
    * `shared_activities`: `text[]` (Array of `SharedActivities.activity_id` UUIDs as text, Nullable) - *Note: Storing UUIDs as text in array.*
    * `my_interests`: `text[]` (Array of keyword strings, Nullable)
    * `location`: `geography(Point, 4326)` (Nullable)
    * `country`: `text` (Nullable)
    * `onboarding_complete`: `bool` (NOT NULL, Default: `false`)
    * `profile_picture_url`: `text` (Nullable) - URL or asset path.
    * `avatar_type`: `text` (Nullable) - 'uploaded', 'default', 'generated'.
    * `generated_avatar_color`: `text` (Nullable) - Hex color string (e.g., 'FF1122EE').
    * `location_updated_at`: `timestamptz` (Nullable)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
    * `discoverable`: `bool` (NOT NULL, Default: `true`)
* **Indexes:** `username` (unique), `email` (unique), `location` (GIST).

---

### 5.2. `public."Friendships"`

* **Description:** Manages relationships (friends, pending, blocked).
* **RLS:** Enabled. Policies allow users to manage/view relationships involving themselves.
* **Columns:**
    * `friendship_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `user_a_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `user_b_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `status_a_to_b`: `text` (NOT NULL) - 'none', 'pending', 'accepted', 'blocked', 'received'.
    * `status_b_to_a`: `text` (NOT NULL) - 'none', 'pending', 'accepted', 'blocked', 'received'.
    * `request_sent_at`: `timestamptz` (Nullable)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
    * `updated_at`: `timestamptz` (Nullable)
* **Constraints:** `UNIQUE(user_a_id, user_b_id)`, `CHECK(user_a_id <> user_b_id)`.
* **Indexes:** `(user_a_id, user_b_id)`, `(user_b_id, user_a_id)`.

---

### 5.3. `public."HiddenUsers"`

* **Description:** Tracks users hidden by other users.
* **RLS:** Enabled. Policies allow users to manage/view their own hidden list.
* **Columns:**
    * `hidden_user_entry_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `hider_user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `hidden_user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Constraints:** `UNIQUE(hider_user_id, hidden_user_id)`.
* **Indexes:** `hider_user_id`, `hidden_user_id`.

---

### 5.4. `public."SavedUsers"`

* **Description:** Tracks users "saved" or "bookmarked" by other users.
* **RLS:** Enabled. Policies allow users to manage/view their own saved list.
* **Columns:**
    * `saved_user_entry_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `saver_user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `saved_user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `saved_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Constraints:** `UNIQUE(saver_user_id, saved_user_id)`.
* **Indexes:** `saver_user_id`, `saved_user_id`.

---

### 5.5. `public."Categories"`

* **Description:** Stores predefined categories for Events and Wishlist Items.
* **RLS:** Enabled. Policy allows authenticated read access. Admin for writes.
* **Columns:**
    * `category_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `category_name`: `text` (NOT NULL, UNIQUE)
    * `sort_order`: `smallint` (Default: 0)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `category_name`, `sort_order`.

---

### 5.6. `public."SharedActivities"`

* **Description:** Stores predefined "Activities to Share".
* **RLS:** Enabled. Policy allows authenticated read access. Admin for writes.
* **Columns:**
    * `activity_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `activity_name`: `text` (NOT NULL, UNIQUE)
    * `category`: `text` (NOT NULL) - UI grouping text.
    * `sort_order`: `smallint` (NOT NULL, Default: 0)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `activity_name`, `(category, sort_order)`, `sort_order`.

---

### 5.7. `public."Keywords"`

* **Description:** Stores predefined interest keywords.
* **RLS:** Enabled. Policy allows authenticated read access. Admin for writes.
* **Columns:**
    * `keyword_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `keyword_text`: `text` (NOT NULL, UNIQUE)
    * `category`: `text` (Nullable) - For future filtering, NULL for initial data.
    * `usage_count`: `int4` (NOT NULL, Default: 0) - For admin curation.
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `keyword_text`.

---

### 5.8. `public."Events"`

* **Description:** Stores user-created event details.
* **RLS:** Enabled. Policies allow authenticated read, user INSERT/UPDATE/DELETE own events.
* **Columns:**
    * `event_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `creator_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `event_name`: `text` (NOT NULL)
    * `event_description`: `text` (Nullable)
    * `category_id`: `uuid` (FK to `public."Categories"(category_id)`, Nullable)
    * `event_date`: `timestamptz` (Nullable)
    * `place_name`: `text` (Nullable)
    * `place_location`: `geography(Point, 4326)` (Nullable)
    * `capacity`: `int4` (Nullable)
    * `image_url`: `text` (Nullable) - URL to `event-images` bucket.
    * `chat_room_id`: `uuid` (FK to `public."ChatRooms"(chat_room_id)`, Nullable, UNIQUE)
    * `status`: `text` (NOT NULL, Default: `'draft'`) - 'draft', 'published', 'cancelled'.
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
    * `updated_at`: `timestamptz` (Nullable)
* **Indexes:** `creator_id`, `category_id`, `event_date`, `place_location` (GIST), `status`.

---

### 5.9. `public."Attendees"`

* **Description:** Links users attending events. Tracks rejoin count.
* **RLS:** Enabled. Policies allow users to view all, INSERT/DELETE own attendance.
* **Columns:**
    * `attendee_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `event_id`: `uuid` (FK to `public."Events"(event_id)` ON DELETE CASCADE, NOT NULL)
    * `user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `join_count`: `smallint` (NOT NULL, Default: 1)
    * `joined_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Constraints:** `UNIQUE(event_id, user_id)`.
* **Indexes:** `event_id`, `user_id`.

---

### 5.10. `public."WishlistItems"`

* **Description:** Stores user wishlist items.
* **RLS:** Enabled. Policies allow users to view friends' items, INSERT/UPDATE/DELETE own items.
* **Columns:**
    * `wishlist_item_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `creator_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `item_name`: `text` (NOT NULL)
    * `item_description`: `text` (Nullable)
    * `item_url`: `text` (Nullable)
    * `category_id`: `uuid` (FK to `public."Categories"(category_id)`, Nullable)
    * `event_date`: `timestamptz` (Nullable) - Mutually exclusive with `general_date`.
    * `general_date`: `text` (Nullable) - Mutually exclusive with `event_date`.
    * `place_name`: `text` (Nullable)
    * `place_location`: `geography(Point, 4326)` (Nullable)
    * `chat_room_id`: `uuid` (FK to `public."ChatRooms"(chat_room_id)`, Nullable, UNIQUE)
    * `status`: `text` (NOT NULL, Default: `'draft'`) - 'draft', 'published', 'completed'.
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
    * `updated_at`: `timestamptz` (Nullable)
* **Indexes:** `creator_id`, `category_id`.

---

### 5.11. `public."InterestedUsers"`

* **Description:** Links users interested in wishlist items. Tracks interest level.
* **RLS:** Enabled. Policies allow users to view interest for friends' items, INSERT/UPDATE/DELETE own interest record. Creator can view all for own items.
* **Columns:**
    * `interested_user_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `wishlist_item_id`: `uuid` (FK to `public."WishlistItems"(wishlist_item_id)` ON DELETE CASCADE, NOT NULL)
    * `user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `status`: `text` (NOT NULL) - 'interested', 'lets_go', 'going'.
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Constraints:** `UNIQUE(wishlist_item_id, user_id)`.
* **Indexes:** `wishlist_item_id`, `user_id`.

---

### 5.12. `public."ChatRooms"`

* **Description:** Metadata for chat rooms (one-on-one, event, wishlist).
* **RLS:** Enabled. Policies allow users to SELECT rooms they are members of. INSERT/UPDATE/DELETE restricted (handled programmatically).
* **Columns:**
    * `chat_room_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `creator_id`: `uuid` (FK to `public."Users"(user_id)`, Nullable) - For 1-on-1 or if creator relevant.
    * `event_id`: `uuid` (FK to `public."Events"(event_id)` ON DELETE CASCADE, Nullable, UNIQUE)
    * `wishlist_item_id`: `uuid` (FK to `public."WishlistItems"(wishlist_item_id)` ON DELETE CASCADE, Nullable, UNIQUE)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `event_id`, `wishlist_item_id`.

---

### 5.13. `public."ChatRooms_Users"` (Join Table)

* **Description:** Links users to their chat rooms. Tracks read status.
* **RLS:** Enabled. Policies allow SELECT/UPDATE(`last_read_at`)/DELETE for rows where `user_id = auth.uid()`. INSERT handled programmatically.
* **Columns:**
    * `chat_rooms_users_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `chat_room_id`: `uuid` (FK to `public."ChatRooms"(chat_room_id)` ON DELETE CASCADE, NOT NULL)
    * `user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `joined_at`: `timestamptz` (NOT NULL, Default: `now()`)
    * `last_read_at`: `timestamptz` (Nullable)
* **Constraints:** `UNIQUE(chat_room_id, user_id)`.
* **Indexes:** `chat_room_id`, `user_id`.

---

### 5.14. `public."Messages"`

* **Description:** Stores individual chat messages.
* **RLS:** Enabled. Policies allow users to SELECT messages from rooms they are members of, INSERT messages where `sender_id = auth.uid()` and they are members. UPDATE/DELETE restricted.
* **Columns:**
    * `message_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `chat_room_id`: `uuid` (FK to `public."ChatRooms"(chat_room_id)` ON DELETE CASCADE, NOT NULL)
    * `sender_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE SET NULL, NOT NULL)
    * `message_text`: `text` (NOT NULL)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `chat_room_id`, `created_at`, `sender_id`.

---

### 5.15. `public."UserNotifications"`

* **Description:** Stores in-app notifications.
* **RLS:** Enabled. Policies allow users to SELECT/UPDATE/DELETE own notifications (`user_id = auth.uid()`). INSERT handled programmatically.
* **Columns:**
    * `notification_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL) - Recipient.
    * `notification_type`: `text` (NOT NULL)
    * `content`: `text` (NOT NULL)
    * `is_read`: `bool` (NOT NULL, Default: `false`)
    * `related_user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE SET NULL, Nullable) - e.g., sender.
    * `event_id`: `uuid` (FK to `public."Events"(event_id)` ON DELETE CASCADE, Nullable)
    * `wishlist_item_id`: `uuid` (FK to `public."WishlistItems"(wishlist_item_id)` ON DELETE CASCADE, Nullable)
    * `chat_room_id`: `uuid` (FK to `public."ChatRooms"(chat_room_id)` ON DELETE CASCADE, Nullable)
    * `created_at`: `timestamptz` (NOT NULL, Default: `now()`)
* **Indexes:** `user_id`, `is_read`, `created_at`.

---

### 5.16. `public."Questions"`

* **Description:** Stores the predefined questionnaire questions.
* **RLS:** Enabled. Policy allows authenticated read access. Admin for writes.
* **Columns:**
    * `question_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `question_text`: `text` (NOT NULL)
    * `category`: `text` (Nullable) - For grouping questions.
    * `sort_order`: `smallint` (Default: 0)
    * `is_active`: `bool` (Default: `true`)
    * `created_at`: `timestamptz` (Default: `now()`)

---

### 5.17. `public."Answers"`

* **Description:** Stores user's answers to questionnaire questions.
* **RLS:** Enabled. Policies allow users to SELECT/INSERT/UPDATE/DELETE own answers (`user_id = auth.uid()`).
* **Columns:**
    * `answer_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `user_id`: `uuid` (FK to `public."Users"(user_id)` ON DELETE CASCADE, NOT NULL)
    * `question_id`: `uuid` (FK to `public."Questions"(question_id)` ON DELETE CASCADE, NOT NULL)
    * `answer_text`: `text` (NOT NULL)
    * `created_at`: `timestamptz` (Default: `now()`)
    * `updated_at`: `timestamptz` (Nullable)
* **Constraints:** `UNIQUE(user_id, question_id)`.
* **Indexes:** `user_id`, `question_id`.

---

### 5.18. `public."QuestionKeywords"` (Admin Managed)

* **Description:** Maps Questions to suggested Keywords. (Data entered by Admin).
* **RLS:** Enabled. Policy allows authenticated read access. Admin for writes.
* **Columns:**
    * `mapping_id`: `uuid` (PK, Default: `gen_random_uuid()`)
    * `question_id`: `uuid` (FK to `public."Questions"(question_id)` ON DELETE CASCADE, NOT NULL)
    * `keyword_id`: `uuid` (FK to `public."Keywords"(keyword_id)` ON DELETE CASCADE, NOT NULL)
* **Constraints:** `UNIQUE(question_id, keyword_id)`.

---

### 5.19. `public."FrequentlyUsedKeywords"` (Potentially Managed by Trigger/Function)

* **Description:** Tracks frequency of *custom* keywords entered by users.
* **RLS:** Enabled. Read access likely admin/internal only. Writes handled by backend logic.
* **Columns:**
    * `keyword_text`: `text` (PK) - The normalized custom keyword.
    * `frequency`: `integer` (NOT NULL, Default: 1)
    * `last_used`: `timestamptz` (Default: `now()`)


# 6. Supabase Functions & Views (PostgreSQL)

This section details the custom PostgreSQL functions and views created within the Supabase database to support specific application logic, enhance query performance, and simplify Row Level Security (RLS) policies.

## 6.1. Functions

### 6.1.1. `is_blocked(user1_id UUID, user2_id UUID)`
* **Signature:** `FUNCTION is_blocked(user1_id UUID, user2_id UUID) RETURNS boolean`
* **Purpose:** Determines if a block exists between two specified users in either direction.
* **Logic:** Queries the `public."Friendships"` table to check if `status_a_to_b` or `status_b_to_a` is set to `'blocked'` for the given `user1_id` and `user2_id` pair.
* **Used By:** Primarily called by the `UserVisibility` view.

### 6.1.2. `is_hidden(hider_user_id UUID, hidden_user_id UUID)`
* **Signature:** `FUNCTION is_hidden(hider_user_id UUID, hidden_user_id UUID) RETURNS boolean`
* **Purpose:** Determines if the `hider_user_id` has hidden the `hidden_user_id`.
* **Logic:** Checks for the existence of a corresponding row in the `public."HiddenUsers"` table.
* **Used By:** Primarily called by the `UserVisibility` view.

### 6.1.3. `get_my_events_with_last_activity(user_id uuid)`
* **Signature:** `FUNCTION get_my_events_with_last_activity(user_id uuid) RETURNS TABLE(...)` (Returns columns matching the `Events` table plus a `last_activity` timestamp)
* **Purpose:** Fetches all events that a given `user_id` is associated with (either as creator or attendee) and calculates the timestamp of the last relevant activity (either the event's creation or the latest associated notification for that user).
* **Logic:** Joins `Events` with `Attendees` (optional) and `UserNotifications`. Uses `MAX()` and `COALESCE()` to find the latest relevant timestamp. Groups by event and orders by the calculated `last_activity` descending.
* **Used By:** `SupabaseService` (called by `EventRepository.getMyEventsWithLastActivity`) to populate the "My Events" tab, sorted by recency.

### 6.1.4. `get_unread_event_notification_count_for_user(user_id uuid)`
* **Signature:** `FUNCTION get_unread_event_notification_count_for_user(user_id uuid) RETURNS integer`
* **Purpose:** Counts unread notifications specifically related to events the user is associated with (created or attending).
* **Logic:** Joins `UserNotifications` with `Events` and `Attendees`. Filters notifications where `event_id` is not null, `is_read` is false, and the notification's target `user_id` matches the input `user_id` OR the user is the creator/attendee of the linked event.
* **Used By:** `SupabaseService` (called by `EventRepository`) potentially for displaying aggregated unread counts related to events. *(Self-correction: The primary unread count comes from the dedicated Notification repository methods/provider, this function might be less used or could be refined later)*.

### 6.1.5. `get_unread_count(user_id uuid, chat_room_id uuid)`
* **Signature:** `FUNCTION get_unread_count(user_id uuid, chat_room_id uuid) RETURNS integer`
* **Purpose:** Calculates the number of unread messages for a specific user within a specific chat room.
* **Logic:** Compares message timestamps in the `Messages` table against the `last_read_at` timestamp for the given user/room combination in the `ChatRooms_Users` table.
* **Used By:** `SupabaseService` (called by `ChatRepository`) to display unread message badges in the `ChatListScreen`.

### 6.1.6. `get_one_on_one_chat_room(user_id_1 uuid, user_id_2 uuid)`
* **Signature:** `FUNCTION get_one_on_one_chat_room(user_id_1 uuid, user_id_2 uuid) RETURNS TABLE(...)` (Returns columns matching the `ChatRooms` table)
* **Purpose:** Finds an existing *direct* one-on-one chat room between two specified users.
* **Logic:** Joins `ChatRooms` with `ChatRooms_Users` twice (once for each user ID). Filters for rooms where both users are participants *and* where `event_id` and `wishlist_item_id` are NULL.
* **Used By:** `SupabaseService` (called by `ChatRepository.getOrCreateChatRoom`) to check if a 1-on-1 room already exists before creating a new one.

## 6.2. Views

### 6.2.1. `public."UserVisibility"`
* **Purpose:** Provides a view of the `Users` table augmented with boolean flags indicating the visibility status relative to the *currently authenticated user*. Simplifies filtering out blocked/hidden users in application queries.
* **Logic:** Selects all columns from `public."Users"`. Calls the `is_blocked()` function twice (once checking `auth.uid()` vs `u.user_id`, once checking `u.user_id` vs `auth.uid()`) to create `is_blocked_by_me` and `is_blocked_by_other` columns. Calls the `is_hidden()` function (checking `auth.uid()` vs `u.user_id`) to create the `is_hidden_by_me` column. Uses `auth.uid()` internally to reference the current user session.
* **Used By:** `SupabaseService` (called by `UserRepository` methods like `searchUsers`, `searchUsersByLocation`, `getFriends`, and potentially others) as the target table instead of `public."Users"` when fetching lists of users to be displayed to the current user. Queries filter `WHERE is_blocked_by_me = false AND is_hidden_by_me = false`.


# 7. Data Models (Dart)

This section describes the core Dart classes used to represent the application's data structures. These models are defined within the `lib/core/models/` directory and utilize the `freezed` package for creating immutable data classes with helpful utilities (`copyWith`, `==`, `toString`) and `json_serializable` for handling conversion to/from JSON for Supabase interaction. These models directly correspond to the database tables defined in Section 5.

---

### 7.1. `User`
* **File:** `user.dart`
* **Purpose:** Represents a user's complete profile information, including authentication details, personal information, preferences, and settings.
* **Key Fields:** `userId`, `username`, `email`, `fullName`, `age`, `gender`, `city`, `country`, `location` (GeoPoint), `profilePictureUrl`, `avatarType`, `generatedAvatarColor`, `sharedActivities` `List<String>`, `myInterests` `List<String>`, `discoverable`, `onboardingComplete`.
* **Mapping:** Maps to the `public."Users"` table.

### 7.2. `Friendship`
* **File:** `friendship.dart`
* **Purpose:** Represents the relationship status between two users.
* **Key Fields:** `friendshipId`, `userAId`, `userBId`, `statusAtoB`, `statusBtoA`, `requestSentAt`, `createdAt`.
* **Mapping:** Maps to the `public."Friendships"` table.

### 7.3. `HiddenUser`
* **File:** `hidden_user.dart`
* **Purpose:** Represents an entry indicating one user has hidden another.
* **Key Fields:** `hiddenUserEntryId`, `hiderUserId`, `hiddenUserId`, `createdAt`.
* **Mapping:** Maps to the `public."HiddenUsers"` table.

### 7.4. `SavedUser`
* **File:** `saved_user.dart`
* **Purpose:** Represents an entry indicating one user has saved another user's profile.
* **Key Fields:** `savedUserEntryId`, `saverUserId`, `savedUserId`, `savedAt`.
* **Mapping:** Maps to the `public."SavedUsers"` table.

### 7.5. `Category`
* **File:** `category.dart`
* **Purpose:** Represents a predefined category for Events or Wishlist Items.
* **Key Fields:** `categoryId`, `categoryName`, `sortOrder`.
* **Mapping:** Maps to the `public."Categories"` table.

### 7.6. `SharedActivity`
* **File:** `shared_activity.dart`
* **Purpose:** Represents a predefined activity users might want to share.
* **Key Fields:** `activityId`, `activityName`, `category`, `sortOrder`.
* **Mapping:** Maps to the `public."SharedActivities"` table.

### 7.7. `Keyword`
* **File:** `keyword.dart`
* **Purpose:** Represents a predefined interest keyword.
* **Key Fields:** `keywordId`, `keywordText`, `category` (nullable), `usageCount`.
* **Mapping:** Maps to the `public."Keywords"` table.

### 7.8. `Event`
* **File:** `event.dart`
* **Purpose:** Represents a user-created event.
* **Key Fields:** `eventId`, `creatorId`, `eventName`, `eventDescription`, `categoryId`, `eventDate`, `placeName`, `placeLocation` (GeoPoint), `capacity`, `imageUrl`, `chatRoomId`, `status`.
* **Mapping:** Maps to the `public."Events"` table.

### 7.9. `Attendee`
* **File:** `attendee.dart`
* **Purpose:** Represents a user's attendance at an event.
* **Key Fields:** `attendeeId`, `eventId`, `userId`, `joinCount`, `joinedAt`.
* **Mapping:** Maps to the `public."Attendees"` table.

### 7.10. `WishlistItem`
* **File:** `wishlist_item.dart`
* **Purpose:** Represents a user's wishlist item or desired activity.
* **Key Fields:** `wishlistItemId`, `creatorId`, `itemName`, `itemDescription`, `categoryId`, `eventDate`, `generalDate`, `placeName`, `placeLocation` (GeoPoint), `chatRoomId`, `status`.
* **Mapping:** Maps to the `public."WishlistItems"` table.

### 7.11. `InterestedUser`
* **File:** `interested_user.dart`
* **Purpose:** Represents a user's expressed interest in a wishlist item.
* **Key Fields:** `interestedUserId`, `wishlistItemId`, `userId`, `status` ('interested', 'lets_go', 'going').
* **Mapping:** Maps to the `public."InterestedUsers"` table.

### 7.12. `ChatRoom`
* **File:** `chat_room.dart`
* **Purpose:** Represents metadata for a conversation thread (1-on-1, Event, or Wishlist).
* **Key Fields:** `chatRoomId`, `creatorId`, `eventId`, `wishlistItemId`, `createdAt`.
* **Mapping:** Maps to the `public."ChatRooms"` table.

### 7.13. `ChatRoomsUsers`
* **File:** `chat_rooms_users.dart`
* **Purpose:** Join table linking users to the chat rooms they are members of.
* **Key Fields:** `chatRoomsUsersId`, `chatRoomId`, `userId`, `joinedAt`, `lastReadAt`.
* **Mapping:** Maps to the `public."ChatRooms_Users"` table.

### 7.14. `Message`
* **File:** `message.dart`
* **Purpose:** Represents a single message within a chat room.
* **Key Fields:** `messageId`, `chatRoomId`, `senderId`, `messageText`, `createdAt`.
* **Mapping:** Maps to the `public."Messages"` table.

### 7.15. `UserNotification`
* **File:** `notification.dart`
* **Purpose:** Represents an in-app notification for a user.
* **Key Fields:** `notificationId`, `userId` (recipient), `notificationType`, `content`, `isRead`, `relatedUserId`, `eventId`, `wishlistItemId`, `chatRoomId`, `createdAt`.
* **Mapping:** Maps to the `public."UserNotifications"` table.

### 7.16. `Question`
* **File:** `question.dart`
* **Purpose:** Represents a question in the user questionnaire.
* **Key Fields:** `questionId`, `questionText`, `category`, `sortOrder`, `isActive`.
* **Mapping:** Maps to the `public."Questions"` table.

### 7.17. `Answer`
* **File:** `answer.dart`
* **Purpose:** Represents a user's answer to a specific questionnaire question.
* **Key Fields:** `answerId`, `userId`, `questionId`, `answerText`.
* **Mapping:** Maps to the `public."Answers"` table.

### 7.18. `QuestionKeyword`
* **File:** `question_keyword.dart`
* **Purpose:** Represents the mapping between a questionnaire question and a suggested keyword.
* **Key Fields:** `mappingId`, `questionId`, `keywordId`.
* **Mapping:** Maps to the `public."QuestionKeywords"` table.

### 7.19. `FrequentlyUsedKeyword`
* **File:** `frequently_used_keyword.dart`
* **Purpose:** Tracks the usage frequency of custom keywords (for admin analysis).
* **Key Fields:** `keywordText`, `frequency`, `lastUsed`.
* **Mapping:** Maps to the `public."FrequentlyUsedKeywords"` table.


# 8. Service Layer

The Service Layer acts as an intermediary between the Repository Layer and external systems or platform services. It encapsulates the direct interaction logic, keeping the repositories focused on data orchestration and the rest of the app shielded from the specifics of external APIs or platform channels.

## 8.1. `SupabaseService`

* **File:** `lib/core/services/supabase_service.dart`
* **Purpose:** This is the **central and sole point of interaction** with the Supabase backend. It wraps the `SupabaseClient` instance and provides dedicated methods for all required operations across Supabase's features (Authentication, Database, Storage, Realtime Functions, RPC). This ensures consistent interaction patterns, isolates Supabase dependencies, and simplifies testing/maintenance.
* **Implementation:** Contains a `SupabaseClient` instance. Methods typically perform a single Supabase operation (e.g., `select`, `insert`, `update`, `delete`, `rpc`, `auth.signUp`, `storage.from().upload()`), include necessary data mapping (like `.toJson()`), use `try...catch` blocks, and `rethrow` specific Supabase exceptions (`AuthException`, `PostgrestException`, `StorageException`) to be handled by the calling Repository.
* **Key Methods Provided (Grouped by Functionality):**
    * **Authentication:**
        * `signUp({email, password, emailRedirectTo})`: Handles auth user creation.
        * `signIn({email, password})`: Handles user login.
        * `signOut()`: Logs the user out.
        * `resetPassword({email, redirectTo})`: Initiates password reset flow.
        * `currentUser` (getter): Synchronously retrieves the current `supabase.User?`.
    * **Users (`Users` Table):**
        * `createUser(User user)`: Inserts the initial minimal user profile.
        * `getUser(String userId)`: Fetches a single user profile by ID.
        * `getUsers(List<String> userIds)`: Fetches multiple user profiles by IDs.
        * `updateUser(User user)`: Updates an entire user profile record.
        * `updateUserPartial(String userId, Map<String, dynamic> data)`: Updates specific fields of a user profile.
        * `isUsernameTaken(String username)`: Checks if a username exists.
        * `searchUsers(String query, {minAge, maxAge, gender, distanceFilter})`: Performs interest-based user search with filters.
        * `searchUsersByLocation({latitude, longitude, radiusMeters, minAge, maxAge, gender})`: Performs location-based user search using PostGIS and filters (via `UserVisibility` view).
    * **Storage (`user-images`, `event-images` Buckets):**
        * `uploadProfilePicture(String userId, XFile imageFile)`: Uploads user image.
        * `uploadEventImage(String eventId, XFile imageFile)`: Uploads event image.
        * (Implicitly uses `getPublicUrl` internally).
    * **Friendships (`Friendships` Table):**
        * `getFriendship(String userAId, String userBId)`: Fetches a specific friendship record.
        * `insertFriendship(Friendship friendship)`: Creates a new friendship record.
        * `updateFriendship(Friendship friendship)`: Updates an existing friendship record.
        * `deleteFriendship(String friendshipId)`: Deletes a friendship record.
        * `getFriendIds(String userId)`: Fetches IDs of accepted friends.
        * `getBlockedUsers(String currentUserId)`: Fetches users blocked by/blocking the current user.
        * `getFriendshipStatus(String userId1, String userId2)`: Determines the specific status between two users.
        * `areUsersFriends(String userId1, String userId2)`: Checks if two users have an accepted friendship.
    * **Hidden Users (`HiddenUsers` Table):**
        * `hideUser(String hiderUserId, String hiddenUserId)`: Creates a hidden user record.
        * `unhideUser(String hiderUserId, String hiddenUserId)`: Deletes a hidden user record.
        * `getHiddenUsers(String hiderUserId)`: Fetches users hidden by the specified user (via `UserVisibility` view).
        * `isUserHidden(String hiderUserId, String hiddenUserId)`: Checks if a specific hide relationship exists.
    * **Saved Users (`SavedUsers` Table):**
        * `saveUser(SavedUser savedUser)`: Creates a saved user record.
        * `unsaveUser(String saverUserId, String savedUserId)`: Deletes a saved user record.
        * `getSavedUsers(String saverUserId)`: Fetches users saved by the specified user (likely requires join or view).
        * `isUserSaved(String saverUserId, String savedUserId)`: Checks if a specific save relationship exists.
    * **Events (`Events` Table):**
        * `createEvent(Event event)`: Inserts a new event.
        * `updateEvent(Event event)`: Updates an existing event.
        * `deleteEvent(String eventId)`: Deletes an event.
        * `getEventById(String eventId)`: Fetches a single event.
        * `getEventsByLocation` (RPC Call): Calls `get_my_events_with_last_activity` function.
        * `getMyEventsWithNotificationCounts` (RPC Call): Calls `get_my_events_with_last_activity` function. *(Note: Naming consistency check needed between Repo and Service for these RPC calls)*.
        * `getHostedEvents`, `getAttendingEvents`, `getDraftEvents`, `getPastEvents`: Fetches specific subsets of events based on user ID and status/date.
    * **Attendees (`Attendees` Table):**
        * `insertAttendee(Attendee attendee)`: Adds a user to an event.
        * `updateAttendee(Attendee attendee)`: Updates attendance record (e.g., `join_count`).
        * `getAttendee(String userId, String eventId)`: Fetches a specific attendance record.
        * `getAttendees(String eventId)`: Fetches user profiles of attendees for an event (via `UserVisibility` view).
        * `getAttendeeCount(String eventId)`: Counts attendees for an event.
        * `isUserAttendingEvent(String userId, String eventId)`: Checks if a user is attending.
        * `leaveEvent(String userId, String eventId)`: Removes a user from event attendees.
    * **Wishlists (`WishlistItems` Table):**
        * `createWishlistItem(WishlistItem item)`
        * `updateWishlistItem(WishlistItem item)`
        * `deleteWishlistItem(String itemId)`
        * `getWishlistItemById(String itemId)`
        * `getWishlistItemsByCreator(String creatorId)`
        * `getWishlistItemsInterestedByUser(String userId)`
        * `getWishlistItemsLetsGoByUser(String userId)`
        * `getPastWishlistItems(String userId)`
    * **Interested Users (`InterestedUsers` Table):**
        * `checkInterestedUser(String userId, String wishlistItemId)`
        * `insertInterestedUser(InterestedUser interestedUser)`
        * `updateInterestedUser(InterestedUser interestedUser)`
        * `removeInterestedUser(String userId, String wishlistItemId)`
        * `getInterestedUsers(String wishlistItemId)`
    * **Chat (`ChatRooms`, `ChatRooms_Users`, `Messages` Tables):**
        * `createChatRoom(ChatRoom chatRoom)`
        * `getOneOnOneChatRoom(String userId1, String userId2)` (RPC Call)
        * `addUserToChatRoom(ChatRoomsUsers chatRoomsUsers)`
        * `removeUserFromChatRoom(String chatRoomId, String userId)`
        * `sendMessage(Message message)`
        * `getMessages(String chatRoomId, {DateTime? before, int limit})`
        * `getChatRooms(String userId)`
        * `updateLastRead(String chatRoomId, String userId)`
    * **Notifications (`UserNotifications` Table):**
        * `createNotification(Map<String, dynamic> notificationData)`
        * `getNotificationsForUser(String userId)`
        * `getUnreadNotifications(String userId)`
        * `getTotalUnreadNotificationCount(String userId)` (RPC Call - potentially using a view/function for efficiency)
        * `markNotificationAsRead(String notificationId)`
        * `markAllNotificationsAsRead(String userId)`
        * `deleteNotification(String notificationId)`
        * `getUnreadEventNotificationCountForUserEvents` (RPC Call)
        * `getUnreadCount` (RPC Call for chat room unread count)
    * **Lookup Tables (`Categories`, `SharedActivities`, `Keywords`):**
        * `getCategories()`
        * `getSharedActivities()`
        * `getAllKeywords()`

## 8.2. `LocationService`

* **File:** `lib/core/services/location_service.dart`
* **Purpose:** Encapsulates all interactions related to device location, permissions, and geocoding, abstracting away the details of the underlying packages (`permission_handler`, `geolocator`, `geocoding`, `flutter_osm_plugin`).
* **Key Methods Provided:**
    * `checkService()`: Checks if device location service is enabled. Returns `Future<bool>`.
    * `checkPermission()`: Checks app location permission. Returns `Future<bool>`.
    * `requestPermission()`: Requests location permission. Returns `Future<bool>`.
    * `openAppSettings()`: Opens app settings. Returns `Future<bool>`.
    * `getCurrentLocation()`: Gets current GPS location. Returns `Future<Either<Failure, GeoPoint>>`.
    * `reverseGeocode(GeoPoint)`: Gets address info from coordinates. Returns `Future<Either<Failure, Placemark?>>`.
    * `getCountryFromLocation(GeoPoint)`: Gets country from coordinates. Returns `Future<Either<Failure, String?>>`.
    * `getCityFromLocation(GeoPoint)`: Gets city from coordinates. Returns `Future<Either<Failure, String?>>`.
    * `getLocationAndCountryFromCity(String)`: Gets coordinates/country from city name. Returns `Future<Either<Failure, (GeoPoint?, String?)>>`.
    * `getCitySuggestions(String query)`: Gets city autocomplete suggestions. Returns `Future<Either<Failure, List<SearchInfo>>>`.


# 9. Repository Layer

The Repository Layer implements the Repository Pattern, providing a clean abstraction over the data sources (primarily `SupabaseService` and potentially `LocationService`). Repositories group data operations by domain (e.g., User, Event) and are responsible for calling the appropriate Service methods, handling any errors returned from the Service Layer (catching exceptions and converting them to specific `Failure` types), and returning results wrapped in `fpdart`'s `Either<Failure, T>` type to the ViewModels. Repositories depend only on Services, not on other Repositories or ViewModels.

---

### 9.1. `AuthRepository`
* **File:** `lib/core/services/auth_repository.dart`
* **Purpose:** Handles all user authentication flows and interaction with the authentication state.
* **Dependencies:** `SupabaseService`.
* **Key Methods:**
    * `signUp({email, password})`: Attempts user registration via `SupabaseService`, creates the minimal user profile record in the `Users` table upon auth success. Returns `Either<Failure, supabase.User?>`.
    * `signIn({email, password})`: Attempts user login via `SupabaseService`. Returns `Either<Failure, supabase.User?>`.
    * `signOut()`: Signs the user out via `SupabaseService`. Returns `Either<Failure, Unit>`.
    * `resetPassword({email})`: Initiates the password reset flow via `SupabaseService`, passing the required `redirectTo` URL. Returns `Either<Failure, Unit>`.
    * `currentUser()`: Retrieves the current authenticated `supabase.User?` state via `SupabaseService`. Returns `Future<Either<Failure, supabase.User?>>`. *(Note: Kept async for API consistency despite underlying service getter being sync)*.

---

### 9.2. `UserRepository`
* **File:** `lib/core/services/user_repository.dart`
* **Purpose:** Manages all data operations related to user profiles, relationships (friendships, blocking, hiding, saving), and user searching.
* **Dependencies:** `SupabaseService`.
* **Key Methods (Grouped):** Returns `Future<Either<Failure, T>>` for fallible operations.
    * **Profile:** `getUser`, `updateUser`, `updateDiscoverability`, `isUsernameTaken` (mocked in `watermelon_draft`), `uploadProfilePicture`.
    * **Search:** `searchUsersByLocation` (applies filters, excludes blocked/hidden via `UserVisibility`), `searchUsers` (handles name/username query, filters).
    * **Friendships:** `getFriendIds`, `getFriends` (excludes blocked), `areUsersFriends`, `getFriendshipStatus`, `getFriendshipByIds`, `sendFriendRequest`, `acceptFriendRequest`, `declineFriendRequest`, `cancelFriendRequest`.
    * **Blocking:** `blockUser`, `unblockUser`, `getBlockedUsers`.
    * **Hiding:** `hideUser`, `unhideUser`, `getHiddenUsers`, `isUserHidden`.
    * **Saving:** `saveUser`, `unsaveUser`, `getSavedUsers`, `isUserSaved`.

---

### 9.3. `EventRepository`
* **File:** `lib/core/services/event_repository.dart`
* **Purpose:** Manages data operations for events, attendance, and event categories.
* **Dependencies:** `SupabaseService`, `ChatRepository` (potentially, for ensuring chat room exists or participant management on join/leave).
* **Key Methods:** Returns `Future<Either<Failure, T>>`.
    * `createEvent` (handles image upload via `uploadEventImage`, creates associated `ChatRoom`, adds creator to chat).
    * `updateEvent`, `deleteEvent`, `getEventById`, `getEventsByLocation` (applies filters).
    * `getMyEventsWithLastActivity` (calls RPC).
    * `getAttendeeCount`, `getAttendees` (uses `UserVisibility`), `isUserAttendingEvent`.
    * `joinEvent` (updates `Attendees`, adds user to event `ChatRoom`).
    * `leaveEvent` (updates `Attendees`, removes user from event `ChatRoom`).
    * `getCategories`.
    * `uploadEventImage`.
    * `getUnreadEventNotificationCountForUserEvents` (calls RPC).

---

### 9.4. `WishlistRepository`
* **File:** `lib/core/services/wishlist_repository.dart`
* **Purpose:** Manages data operations for wishlist items and user interest tracking.
* **Dependencies:** `SupabaseService`, `ChatRepository`.
* **Key Methods:** Returns `Future<Either<Failure, T>>`.
    * `createWishlistItem` (creates associated `ChatRoom`, adds creator).
    * `updateWishlistItem`, `deleteWishlistItem`, `getWishlistItemById`.
    * Fetching methods: `getWishlistItemsByCreator`, `getWishlistItemsInterestedByUser`, `getWishlistItemsLetsGoByUser`, `getPastWishlistItems`.
    * Interest Management: `checkInterestedUser`, `insertInterestedUser`, `updateInterestedUser` (handles status changes 'interested'/'lets_go'/'going'), `removeInterestedUser` (removes from `InterestedUsers` and associated `ChatRoom`), `getInterestedUsers`.

---

### 9.5. `ChatRepository`
* **File:** `lib/core/services/chat_repository.dart`
* **Purpose:** Manages data operations for chat rooms, messages, participants, and read status.
* **Dependencies:** `SupabaseService`.
* **Key Methods:** Returns `Future<Either<Failure, T>>`.
    * `getChatRooms` (fetches rooms for current user).
    * `getMessages` (fetches messages for a room, supports pagination).
    * `sendMessage` (inserts message, triggers notification if non-friend).
    * `markAsRead` (updates `last_read_at` in `ChatRooms_Users`).
    * `getOrCreateChatRoom` (handles finding/creating 1-on-1 rooms).
    * `addUserToChatRoom` (adds user to `ChatRooms_Users`, used by Event/Wishlist repos).
    * `removeUserFromChatRoom` (removes user from `ChatRooms_Users`).
    * `getUnreadCount` (calls RPC).

---

### 9.6. `NotificationRepository`
* **File:** `lib/core/services/notification_repository.dart`
* **Purpose:** Manages CRUD operations for in-app user notifications.
* **Dependencies:** `SupabaseService`.
* **Key Methods:** Returns `Future<Either<Failure, T>>`.
    * `createNotification` (takes structured data, calls service).
    * `getNotificationsForUser` (fetches all notifications for a user).
    * `getUnreadNotifications` (fetches only unread notifications).
    * `getTotalUnreadNotificationCount` (fetches aggregate count, might call RPC).
    * `markNotificationAsRead(String notificationId)`.
    * `markAllNotificationsAsRead(String userId)`.
    * `deleteNotification(String notificationId)`.

---

### 9.7. `SharedActivitiesRepository`
* **File:** `lib/core/services/shared_activities_repository.dart`
* **Purpose:** Fetches the predefined list of "Activities to Share".
* **Dependencies:** `SupabaseService`.
* **Key Methods:**
    * `getSharedActivities()`: Returns `Future<Either<Failure, List<SharedActivity>>>`.

---

### 9.8. `KeywordsRepository`
* **File:** `lib/core/services/keywords_repository.dart`
* **Purpose:** Fetches the predefined list of interest keywords. (May later include methods for adding frequently used custom keywords).
* **Dependencies:** `SupabaseService`.
* **Key Methods:**
    * `getAllKeywords()`: Returns `Future<Either<Failure, List<Keyword>>>`.


# 10. State Management (Riverpod)

Riverpod (version 2.x) serves as the primary framework for both state management and dependency injection throughout the Watermelon application. It facilitates a reactive and scalable architecture, integrating seamlessly with the MVVM pattern. The implementation heavily utilizes Riverpod's code generation features (`@riverpod`) for enhanced type safety, conciseness, and maintainability.

## 10.1. Overview

* **Role:** Manages UI state, handles dependency injection, and enables reactive updates between the data/logic layers and the UI.
* **Code Generation:** Providers for Services, Repositories, and ViewModels are defined using the `@riverpod` annotation, automatically generating the necessary provider instances (e.g., `myViewModelProvider`).
* **Immutability:** State managed by providers is typically immutable, often using classes generated by the `freezed` package. State updates involve creating new state objects rather than mutating existing ones.

## 10.2. Provider Types

* **Service/Repository Providers (`@riverpod` functions):**
    * Defined as top-level functions annotated with `@riverpod`.
    * Typically provide singleton instances of `SupabaseService`, `LocationService`, and the various `Repository` classes (e.g., `authRepository`, `userRepository`).
    * Use `ref.watch` internally to access dependencies (e.g., `AuthRepository` watches `SupabaseService`).
    * Often use `@Riverpod(keepAlive: true)` for services or data that should persist globally (e.g., `sharedPreferencesProvider`, `currentUserProfileProvider`, `sharedActivitiesProvider`).
* **ViewModel Providers (`@riverpod` classes / `AsyncNotifierProvider`):**
    * Defined as classes annotated with `@riverpod` that extend the generated `_$ViewModelName` (which typically inherits from `AsyncNotifier<StateObject>`).
    * Responsible for managing the state of a specific screen or feature.
    * The state (`state` property) is an `AsyncValue<StateObject>`, automatically handling loading, data, and error states for asynchronous operations initiated in the `build` method.
    * Generated providers are typically `AutoDisposeAsyncNotifierProvider` unless `keepAlive` is specified.

## 10.3. ViewModel Implementation (`AsyncNotifier`)

* **Base Class:** ViewModels extend the code-generated `_$ViewModelName` (e.g., `class DiscoverViewModel extends _$DiscoverViewModel`).
* **`build()` Method:**
    * Responsible for initializing the ViewModel and returning its **initial state**.
    * Can be `async` and return `FutureOr<StateObject>`.
    * Initializes dependencies by calling `ref.watch(someRepositoryProvider)`.
    * Performs initial asynchronous data fetching required for the screen.
    * If initialization fails, it should `throw` a `Failure` (or other exception), which automatically puts the provider into the `AsyncError` state.
    * If successful, it returns the initial `StateObject` (e.g., `DiscoverState(...)`). Riverpod automatically wraps this in `AsyncData`.
* **State Management:**
    * The current state is accessed internally via the `state` property (type `AsyncValue<StateObject>`).
    * Methods performing actions update the state:
        * For operations returning `Either<Failure, T>`: Use `.fold`. On `Left(failure)`, set `state = AsyncError(failure, StackTrace.current)`. On `Right(data)`, set `state = AsyncData(newData)`.
        * For synchronous updates based on current data: Use helper methods or checks like `state.whenData((value) { state = AsyncData(value.copyWith(...)); });` to ensure updates only happen on valid data states.
* **Methods:** Public methods are defined to handle user interactions or events (e.g., `signUp`, `WorkspaceUsers`, `joinEvent`, `sendMessage`), containing business logic and calls to repositories.

## 10.4. UI Interaction (Widgets)

* **`ConsumerWidget` / `ConsumerStatefulWidget`:** Widgets that need to interact with providers extend these classes, providing access to a `WidgetRef` (`ref`).
* **Reading State (`ref.watch`):** Used within the `build` method to get the current state (`AsyncValue`) of a provider and subscribe to changes, triggering rebuilds.
* **Handling Async State (`switch`):** Dart's pattern matching (`switch (stateAsync) { ... }`) is used on the `AsyncValue` obtained from `ref.watch` to handle the `AsyncData`, `AsyncLoading`, and `AsyncError` cases and build the appropriate UI for each.
* **Calling Actions (`ref.read(provider.notifier)`):** Used inside event handlers (like `onPressed`, `onChanged`, `onTap`) to get a reference to the ViewModel (`Notifier` or `AsyncNotifier`) instance and call its public methods *without* rebuilding the widget.
* **Side Effects (`ref.listen`):** Used inside the `build` method to listen for specific state changes (often using `.select`) and perform side effects that *don't* require a UI rebuild, such as navigation (`context.beamToNamed`), showing `SnackBar`s, or displaying dialogs. Includes `mounted` checks when interacting with `BuildContext` after an `await`.

## 10.5. Error Handling Flow Summary

1.  **Service Layer (`SupabaseService`, `LocationService`):** Executes external calls, uses `try...catch`, `rethrows` platform/API exceptions.
2.  **Repository Layer:** Calls Service methods, uses `try...catch`, catches specific exceptions, returns `Future<Either<Failure, T>>` wrapping either success data (`Right<T>`) or a custom `Failure` object (`Left<Failure>`).
3.  **ViewModel Layer (`AsyncNotifier`):** `await`s Repository methods, uses `.fold` on the `Either` result. Sets `state = AsyncError(failure, stackTrace)` on `Left`, or `state = AsyncData(data)` on `Right`. The `build` method throws failures directly.
4.  **View Layer (Widgets):** Uses `ref.watch(...).when(...)` or `switch (stateAsync)` to display loading indicators, error messages/prompts, or the actual data based on the `AsyncValue` state. Uses `ref.listen` to show non-blocking error feedback like `SnackBar`s.


# 11. Key UI Components & Screens

This section describes the primary screens and reusable UI widgets that form the user interface of the Watermelon application, based on the MVVM architecture. Implementations in `watermelon_draft` use placeholders, with final UI styling and layout based on future Figma designs.

## 11.1. Overview

* **Framework:** Flutter SDK.
* **Structure:** UI components are organized primarily within feature folders (`lib/features/feature_name/screens/` and `lib/features/feature_name/widgets/`).
* **Shared Widgets:** Common reusable widgets (not tied to a specific feature's state logic) are placed in `lib/widgets/` or `lib/core/ui/widgets/`.
* **State Interaction:** Screens (primarily `ConsumerWidget` or `ConsumerStatefulWidget`) interact with ViewModels via Riverpod (`ref.watch`, `ref.read`, `ref.listen`) to display state and trigger actions.

## 11.2. Core Reusable Widgets

These widgets are designed to be used across multiple features to ensure consistency and reduce code duplication.

### 11.2.1. `UserProfileCard`
* **File:** `lib/features/profile/widgets/user_profile_card.dart`
* **Purpose:** Displays a summary of a user's profile. Used in lists and overlays.
* **Variants (`CardSize` enum):**
    * **`medium`:** Used in scrollable lists (Discover bottom sheet, Saved Profiles list, Friends list?). Displays placeholder image carousel (horizontal scroll) with Save/Unsave heart icon overlay, Full Name, Age & Gender, City, Shared Activities (up to 5 chips), and My Interests (up to 5 chips).
    * **`condensed`:** Used for map marker tap overlay. Displays a squarish image/avatar filling the left side, with Full Name, Age & Gender, City, and limited Shared Activities (first 2-3 chips) on the right. Tappable to navigate to the full profile.
* **Stateful:** Manages the local state for the Save/Unsave (heart) icon interaction.

### 11.2.2. `EventCard`
* **File:** (Likely `lib/features/events/widgets/event_card.dart`)
* **Purpose:** Displays a summary of an event. Used in event list views (Browse Events, My Events).
* **Content:** Event Image/Banner, Event Name, Date/Time, Location Snippet, Category, Attendee Count. May include a notification badge when used in "My Events". Tappable to navigate to `EventDetailPage`.

### 11.2.3. `WishlistCard`
* **File:** (Likely `lib/features/wishlists/widgets/wishlist_card.dart`)
* **Purpose:** Displays a summary of a wishlist item. Used in wishlist lists (Feed, My Wishlists).
* **Content:** Item Name, Creator Avatar/Name, Category, Date Snippet (Specific or General), Location Snippet. May include a notification badge when used in "My Wishlists". Tappable to navigate to `WishlistItemDetailPage`.

### 11.2.4. `ProfileImagePicker`
* **File:** `lib/core/ui/widgets/profile_image_picker.dart`
* **Purpose:** A self-contained component for selecting or generating a profile avatar. Used in Onboarding and Account/Edit Profile.
* **Features:** Provides options for Gallery/Camera (with cropping/compression), selecting a single Default Asset, using/customizing a Generated Initial Avatar (with color regeneration). Provides previews and callbacks (`onImageSelected`, `onDefaultAvatarSelected`, `onGeneratedAvatarSelected`) to the parent widget/ViewModel. Includes configurable "Confirm" ("Next"/"Update") and "Cancel"/"Back" actions.

### 11.2.5. `CitySelectorWidget`
* **File:** `lib/features/profile/widgets/city_selector_widget.dart`
* **Purpose:** A self-contained component for setting a user's location. Used in Onboarding and Edit Profile.
* **Features:** Provides options for "Use Current Location" (handles permission requests, fetching GPS via `LocationService`, reverse geocoding) and manual city text input (with autocomplete suggestions via `LocationService`). Provides a callback (`onLocationSet`) with the resulting `GeoPoint?`, `city?`, and `country?`.

### 11.2.6. `SharedActivitiesSelectorWidget`
* **File:** `lib/features/profile/widgets/shared_activities_selector_widget.dart`
* **Purpose:** A self-contained component for selecting "Activities to Share". Used in Onboarding, Account Dashboard (quick edit), Edit Profile, and Search Page.
* **Features:** Fetches all available activities, allows selection via autocomplete `TextFormField` and/or a categorized modal bottom sheet (`ExpansionTile` + `CheckboxListTile`). Displays selected activities as `Chip` widgets (with removal). Enforces maximum selection limit (5). Provides a callback (`onActivitiesChanged`) with the list of selected activity IDs.

### 11.2.7. `InterestsSelectorWidget`
* **File:** `lib/features/profile/widgets/interests_selector_widget.dart`
* **Purpose:** A self-contained component for selecting/adding "My Interests" keywords. Used in Onboarding (optional step) and Edit Profile.
* **Features:** Fetches predefined keywords. Allows selection via autocomplete `TextFormField`. Allows adding custom keywords (max 2) with fuzzy matching suggestions against predefined list. Displays selected keywords (predefined + custom) as reorderable `Chip` widgets (using `reorderables` package). Enforces maximum total limit (5). Provides a callback (`onKeywordsChanged`) with the list of selected keyword strings.

### 11.2.8. `WatermelonLoadingWidget` / Placeholders
* **File:** (e.g., `lib/core/ui/widgets/` or specific feature widgets)
* **Purpose:** Standardized loading indicators (e.g., `CircularProgressIndicator` with optional text) and potentially placeholder "skeleton" loaders for complex widgets like profile cards while data is loading.

## 11.3. Feature Screens (by Feature)

Describes the primary screens within each major feature area. In `watermelon_draft`, these exist as placeholder widgets linked by the routing system.

### 11.3.1. Authentication (`lib/features/auth/screens/`)

* **`WelcomeScreen.dart`**
    * **Route:** `/welcome` (Initial route)
    * **Purpose:** The initial entry point for new or logged-out users. Presents basic branding and options to proceed.
    * **Key UI Elements:** Welcome message, Login button, Sign Up button.
    * **Navigation:** Navigates to `/login` or `/signup`.
* **`LoginScreen.dart`**
    * **Route:** `/login`
    * **Purpose:** Allows existing users to sign in using email and password.
    * **Key UI Elements:** Email `TextFormField`, Password `TextFormField` (with visibility toggle), "Forgot Password?" link, "Login" button, "Don't have an account? Sign Up" link.
    * **ViewModel:** `LoginViewModel`.
    * **Navigation:** Navigates to `/home` (or `/onboarding`) on successful login, `/reset-password`, `/signup`.
* **`SignupScreen.dart`**
    * **Route:** `/signup`
    * **Purpose:** Allows new users to register an account using email and password.
    * **Key UI Elements:** Email `TextFormField`, Password `TextFormField`, "Sign Up" button, "Already have an account? Log in" link. (Note: Full Name and Username collected during onboarding).
    * **ViewModel:** `SignUpViewModel`.
    * **Navigation:** Navigates to `/onboarding` after successful signup and required email verification. Navigates to `/login`.
* **`ResetPasswordScreen.dart`**
    * **Route:** `/reset-password`
    * **Purpose:** Allows users to request a password reset link via email.
    * **Key UI Elements:** Email `TextFormField`, "Send Reset Link" button.
    * **ViewModel:** `ResetPasswordViewModel`.

### 11.3.2. Onboarding (`lib/features/onboarding/screens/`)

* **`OnboardingScreen.dart`**
    * **Route:** `/onboarding`
    * **Purpose:** Guides new users through the mandatory profile setup process after their first successful login.
    * **Key UI Elements (Structure):** Manages an 8-step flow using `PageView` (programmatic navigation only). Includes an `AppBar` displaying step progress (e.g., "Step X of 8") and a `LinearProgressIndicator`. Uses helper build methods (`_buildWelcomePage`, `_buildNameUsernamePage`, etc.) for each step.
    * **Steps (Built by Helpers):** Welcome, Name/Username (with availability check UI), DOB/Gender, Profile Picture (using `ProfileImagePicker`), Location (using `CitySelectorWidget`), Shared Activities (using `SharedActivitiesSelectorWidget`), My Interests (optional, using `InterestsSelectorWidget`), Summary (with Edit links).
    * **ViewModel:** `OnboardingViewModel`.
    * **Navigation:** Internal navigation between pages controlled by `PageController`. Final "Complete Onboarding" button navigates to `/home` (or `/discover`). Edit buttons on Summary page navigate back to specific steps.

### 11.3.3. Home & Main Navigation (`lib/features/home/<USER>/`)

* **`HomeScreen.dart`**
    * **Route:** `/home` (Typically the main destination after login/onboarding)
    * **Purpose:** Acts as the main container for the primary app sections accessible via bottom navigation.
    * **Key UI Elements (Structure):** `Scaffold` containing a `BottomNavigationBar` with items for Discover, Events, Friends, Wishlists, and Account/Me. The `body` dynamically displays the main screen widget corresponding to the selected bottom navigation tab (e.g., `DiscoverDashboard`, `EventsDashboard`, etc.).
    * **State:** Manages the currently selected bottom navigation index.

### 11.3.4. Discover (`lib/features/discover/screens/`)

* **`DiscoverDashboard.dart`**
    * **Route:** `/discover` (Likely the primary/default tab within `HomeScreen`)
    * **Purpose:** Primary screen for location-based discovery of nearby users.
    * **Key UI Elements (Implemented Structure):**
        * `AppBar` with buttons navigating to `CitySearchPage`, `FilterPage`, `DiscoverNotificationsPage`.
        * `OSMFlutter` map widget displaying custom user avatar markers fetched based on location.
        * Persistent, draggable bottom sheet (`DraggableScrollableSheet`) displaying a scrollable list of nearby users via medium `UserProfileCard` widgets.
        * Floating Action Buttons (visible when bottom sheet is collapsed) for centering map on user's location and navigating to `SearchPage`.
    * **ViewModel:** `DiscoverViewModel`.
    * **Interactions:** Map panning/zooming triggers debounced user fetches. Marker taps open a modal bottom sheet showing the full `ProfileScreen`. Map background taps collapse the results bottom sheet.
* **`CitySearchPage.dart`**
    * **Route:** `/city-search`
    * **Purpose:** Allows users to search for a specific city and set a distance radius to update the location-based search on the `DiscoverDashboard`.
    * **Design:** Input field with autocomplete (using `LocationService.getCitySuggestions`), potentially recent searches list, distance slider/selector, Apply/Search button. Returns selected location/radius.
* **`FilterPage.dart`**
    * **Route:** `/filter`
    * **Purpose:** Allows users to apply filters (Age Range, Gender) to discovery/search results.
    * **Design:** `RangeSlider` for age, `RadioListTile`s for gender, Reset/Apply buttons. Returns selected filter values.
* **`SearchPage.dart`**
    * **Route:** `/search` (Accessed via FAB on `DiscoverDashboard`)
    * **Purpose:** Provides non-map-based search options: searching directly by name/username and searching based on interests (Shared Activities, My Interests) combined with filters (Distance, Age, Gender).
    * **Design:** Section 1: Name/Username `TextFormField` with autocomplete user suggestions. Section 2: "Discover Members Based on Activities and Interests" header, display area for active filters (Age/Gender chips with removal 'x'), Filter icon button (navigates to `FilterPage`), Shared Activities selector UI, My Interests selector UI, Distance filter (Local/Regional/Global radio buttons), "Search by Interests" button.

### 11.3.5. Profile & Account (`lib/features/profile/screens/`, `lib/features/profile/widgets/`)

Handles viewing user profiles (own and others) and managing account-specific settings.

* **`ProfileScreen.dart`**
    * **Route:** `/profile/:userId` (Accepts `userId` as parameter).
    * **Purpose:** Displays the full profile information for a given user.
    * **Key UI Elements (Design):** Intended to include a profile picture/carousel, full name, username, age, gender, city, detailed sections for "About Me" (from questionnaire answers), "Shared Activities", "My Interests", and potentially tabs for user's Events/Wishlists. Action buttons (Add Friend, Message, Wave, etc.) dynamically shown based on relationship status. Includes a menu for Block, Hide, Share.
    * **ViewModel:** `ProfileViewModel`.
* **`EditProfileScreen.dart`**
    * **Route:** `/edit-profile` (Accessed from `AccountDashboard`).
    * **Purpose:** Allows the logged-in user to modify their own profile information.
    * **Key UI Elements (Design):** Form containing input fields/widgets for editable profile data: Full Name (`FullNameInputWidget`), DOB, Gender, City (`CitySelectorWidget`), Profile Picture (`ProfileImagePicker`), Shared Activities (`SharedActivitiesSelectorWidget`), Interests (`InterestsSelectorWidget`). Username field is read-only. Includes Save/Cancel buttons and unsaved changes confirmation.
    * **ViewModel:** `EditProfileViewModel`.
* **`AccountDashboard.dart`**
    * **Route:** `/account` (Likely accessed via `HomeScreen`'s bottom navigation).
    * **Purpose:** Central hub for the logged-in user to manage their account settings and related lists.
    * **Key UI Elements (Design):** Displays user's avatar/name/username. Buttons/Links to navigate to `EditProfileScreen`, view own `ProfileScreen`, `BlockedUsersScreen`, `HiddenUsersScreen`. Functional "Discoverable" `Switch`. Functional "Shared Activities" quick edit `ListTile` (opens bottom sheet selector). Functional "Sign Out" button.
    * **ViewModel:** `AccountViewModel`.
* **`BlockedUsersScreen.dart`**
    * **Route:** `/blocked-users` (Accessed from `AccountDashboard`).
    * **Purpose:** Displays the list of users the current user has blocked.
    * **Key UI Elements (Design):** Simple list displaying username/avatar of blocked users. Includes an "Unblock" button next to each user.
    * **ViewModel:** `BlockedUsersViewModel`.
* **`HiddenUsersScreen.dart`**
    * **Route:** `/hidden-users` (Accessed from `AccountDashboard`).
    * **Purpose:** Displays the list of users the current user has hidden from discovery feeds.
    * **Key UI Elements (Design):** Simple list displaying username/avatar of hidden users. Includes an "Unhide" button next to each user.
    * **ViewModel:** `HiddenUsersViewModel`.

### 11.3.6. Friends (`lib/features/friends/screens/`)

Manages friendships, chat lists, and saved profiles.

* **`FriendsDashboard.dart`**
    * **Route:** `/friends` (Likely accessed via `HomeScreen`'s bottom navigation).
    * **Purpose:** Container screen holding tabs for different friend/chat related views.
    * **Key UI Elements (Design):** `Scaffold` with `AppBar`, using a `TabBar` and `TabBarView` for "Chat", "Friends", and "Saved Profiles".
* **`ChatListScreen.dart`**
    * **Purpose:** Displays a list of the user's ongoing conversations (1-on-1 and group). Shown as a tab within `FriendsDashboard`.
    * **Key UI Elements (Design):** Scrollable list (`ListView`) of items, each showing recipient/group avatar(s), name, last message snippet, timestamp, and unread message indicator/badge. Tapping an item navigates to the corresponding `ChatScreen`.
    * **ViewModel:** `ChatListViewModel` (Handles fetching rooms and subscribing to real-time updates).
* **`FriendsListScreen.dart`**
    * **Purpose:** Displays the list of users the current user has an 'accepted' friendship with. Shown as a tab within `FriendsDashboard`.
    * **Key UI Elements (Design):** Scrollable list (`ListView`) of condensed `UserProfileCard` widgets. Tapping a card navigates to that user's `ProfileScreen`.
    * **ViewModel:** `FriendsListViewModel`.
* **`SavedProfilesScreen.dart`**
    * **Purpose:** Displays the list of user profiles explicitly saved ("favorited") by the current user. Shown as a tab within `FriendsDashboard`.
    * **Key UI Elements (Design):** Scrollable list (`ListView`) of medium `UserProfileCard` widgets (including the save/unsave heart icon). Tapping a card navigates to that user's `ProfileScreen`.
    * **ViewModel:** `SavedProfilesViewModel`.

### 11.3.7. Chat (`lib/features/chat/screens/`)

Handles direct messaging between users.

* **`ChatScreen.dart`**
    * **Route:** `/chat/:chatRoomId` (Accepts `chatRoomId` parameter). Accessed from `ChatListScreen` or potentially "Message" buttons on profiles/events/wishlists.
    * **Purpose:** Displays the message history for a specific chat room and allows the user to send new messages.
    * **Key UI Elements (Design):** `AppBar` showing recipient/group name and avatar. Scrollable message area displaying messages (sent/received styling). Text input field with a send button at the bottom.
    * **ViewModel:** `ChatViewModel` (Handles fetching message history, sending new messages, real-time subscriptions).

### 11.3.8. Events (`lib/features/events/screens/`, `lib/features/events/widgets/`)

Focuses on user-created real-world activities. Implementation prioritized first for MVP.

* **`EventsDashboard.dart`**
    * **Route:** `/events` (Likely accessed via `HomeScreen`'s bottom navigation).
    * **Purpose:** Main screen for discovering and managing events.
    * **Key UI Elements (Design):** `AppBar` (with City Search button/display and potentially Date Filter access), `TabBar` for "Browse Events" and "My Events", `TabBarView` displaying the content for each tab, `FloatingActionButton` to navigate to `/events/create`.
    * **ViewModel:** `EventsDashboardViewModel`.
* **"Browse Events" Tab (within `EventsDashboard`)**
    * **Purpose:** Displays publicly published events based on location and date filters.
    * **Key UI Elements (Design):** Scrollable list or grid of `EventCard` widgets. Filter controls (Date Chips) likely displayed above the list.
* **"My Events" Tab (within `EventsDashboard`)**
    * **Purpose:** Displays events relevant to the logged-in user.
    * **Key UI Elements (Design):** Scrollable list using `EventCard` widgets, grouped under headings: "I'm Hosting", "I'm Going", "Draft Events", "Past Events". Displays notification badges on relevant cards.
* **`CreateEventScreen.dart`**
    * **Route:** `/events/create` (Accessed via FAB on `EventsDashboard`).
    * **Purpose:** Form for users to create a new event.
    * **Key UI Elements (Design):** Input fields for Event Name, Description, Category (dropdown), Date & Time (picker), Place Name (autocomplete), Capacity (optional), Image Banner upload (optional). Buttons for "Save as Draft" and "Publish".
    * **ViewModel:** `CreateEventViewModel`.
* **`EventDetailPage.dart`**
    * **Route:** `/events/:eventId` (Accepts `eventId`). Accessed by tapping an `EventCard`.
    * **Purpose:** Displays the full details of a specific event.
    * **Key UI Elements (Design):** `Scaffold` with `AppBar`. Includes optional Image Banner. `TabBar` for "Details" and "Messages".
        * **Details Tab:** Shows all event info (name, host avatar/name link, date/time, category, location with map/directions links, description, capacity, attendee count/list link).
        * **Messages Tab:** Embeds the `ChatScreen` specific to this event's `chatRoomId`. Visible only to host and attendees.
    * **Action Button:** "Join Event" / "Leave Event" button displayed conditionally based on attendance status and capacity. "Edit Event" button shown for the host.
    * **ViewModel:** `EventDetailViewModel`.
* **`EditEventScreen.dart`**
    * **Route:** `/events/:eventId/edit` (Accessed from `EventDetailPage` by host).
    * **Purpose:** Allows the event host to modify an existing event.
    * **Key UI Elements (Design):** Form pre-filled with existing event data, similar to `CreateEventScreen`. Buttons for "Save Changes", "Cancel Event", "Delete Draft/Event".
    * **ViewModel:** `EditEventViewModel`.
* **`EventNotificationsPage.dart`**
    * **Route:** `/events/:eventId/notifications` (Accessed from `EventCard` badge or `EventDetailPage`).
    * **Purpose:** Displays notifications specifically related to a single event (e.g., new attendees).
    * **Key UI Elements (Design):** List of event-specific notifications.
    * **ViewModel:** `EventNotificationsViewModel`.

### 11.3.9. Wishlists (`lib/features/wishlists/screens/`, `lib/features/wishlists/widgets/`)

Focuses on shared desired activities or goals. Implementation follows Events in MVP cycle.

* **`WishlistsDashboard.dart`**
    * **Route:** `/wishlists` (Likely accessed via `HomeScreen`'s bottom navigation).
    * **Purpose:** Main screen for Browse friend wishlists and managing own items.
    * **Key UI Elements (Design):** `AppBar`, `TabBar` for "Feed" and "My Wishlists", `TabBarView`, `FloatingActionButton` to navigate to `/wishlists/create`.
    * **ViewModel:** `WishlistsDashboardViewModel`.
* **"Feed" Tab (within `WishlistsDashboard`)**
    * **Purpose:** Displays wishlist items created by the user's friends.
    * **Key UI Elements (Design):** Scrollable list of `WishlistCard` widgets.
* **"My Wishlists" Tab (within `WishlistsDashboard`)**
    * **Purpose:** Displays wishlist items relevant to the logged-in user.
    * **Key UI Elements (Design):** Scrollable list using `WishlistCard` widgets, grouped under headings: "I'm Hosting", "I'm Interested" (showing status), "Draft Wishlists", "Past/Completed Wishlists". Displays notification badges.
* **`CreateWishlistItemScreen.dart`**
    * **Route:** `/wishlists/create` (Accessed via FAB on `WishlistsDashboard`).
    * **Purpose:** Form for users to create a new wishlist item.
    * **Key UI Elements (Design):** Input fields for Item Name, Description, Category, Date (Specific OR General), Place Name (optional, autocomplete), URL (optional). Buttons for "Save as Draft" and "Publish".
    * **ViewModel:** `CreateWishlistViewModel`.
* **`WishlistItemDetailPage.dart`**
    * **Route:** `/wishlists/:wishlistId` (Accepts `wishlistId`). Accessed by tapping a `WishlistCard`.
    * **Purpose:** Displays the full details of a specific wishlist item.
    * **Key UI Elements (Design):** Displays item details (name, creator, description, category, date, place, etc.), list of interested users with their status. Buttons for current user to express interest ("Interested", "Let's Go", "Going"). Includes "Messages" tab linking to item's chat. Includes "Convert to Event" button. For creator: "Edit Item", "Mark as Completed" / "Delete Draft" buttons.
    * **ViewModel:** `WishlistItemDetailViewModel`.
* **`EditWishlistItemScreen.dart`**
    * **Route:** `/wishlists/:wishlistId/edit` (Accessed from `WishlistItemDetailPage` by creator).
    * **Purpose:** Allows the item creator to modify an existing wishlist item.
    * **Key UI Elements (Design):** Form pre-filled with existing item data, similar to `CreateWishlistItemScreen`. Buttons for "Save Changes", "Delete Draft/Item".
    * **ViewModel:** `EditWishlistViewModel`.
* **`WishlistItemNotificationsPage.dart`**
    * **Route:** `/wishlists/:wishlistId/notifications` (Accessed from `WishlistCard` badge or `WishlistItemDetailPage`).
    * **Purpose:** Displays notifications specifically related to a single wishlist item (e.g., new interested users).
    * **Key UI Elements (Design):** List of item-specific notifications.
    * **ViewModel:** `WishlistNotificationsViewModel`.

### 11.3.10. Notifications (`lib/features/notifications/screens/`)

Handles the display of aggregated notifications.

* **`DiscoverNotificationsPage.dart`**
    * **Route:** `/notifications/discover` (Accessed from `DiscoverDashboard` AppBar).
    * **Purpose:** Displays notifications related to general discovery and social interactions (Waves, Friend Requests, Non-Friend Messages).
    * **Key UI Elements (Design):** Scrollable list of notifications. Allows marking as read and deleting. Tapping navigates to appropriate context (profile, chat).
    * **ViewModel:** `DiscoverNotificationsViewModel`.


# 12. Key User Flows (MVP)

This section describes the primary user journeys for the MVP version of Watermelon, illustrating how users interact with the designed features and screens.

---

### 12.1. New User Signup & Onboarding Flow

1.  **User Action:** Opens app for the first time.
    * **App Response:** Displays `WelcomeScreen`.
2.  **User Action:** Taps "Sign Up".
    * **App Response:** Navigates to `SignupScreen`.
3.  **User Action:** Enters Email and Password, taps "Sign Up".
    * **App Response:** `SignupViewModel` validates input, calls `AuthRepository.signUp` (passing email, password, and the configured `redirectTo` URL). `AuthRepository` creates Supabase Auth user and minimal `public.Users` record. A confirmation email is sent by Supabase. UI shows loading indicator, then a success message (e.g., SnackBar) prompting user to check email. User remains on `SignupScreen` or is navigated to a "Check Email" screen (TBD).
4.  **User Action:** User checks email, clicks the verification link.
    * **App Response:** Link opens in a browser, displays Supabase default confirmation page. User account is marked as verified in Supabase Auth.
5.  **User Action:** User returns to the Watermelon app, taps "Login".
    * **App Response:** Navigates to `LoginScreen`.
6.  **User Action:** Enters verified Email and Password, taps "Login".
    * **App Response:** `LoginViewModel` validates, calls `AuthRepository.signIn`. Supabase Auth confirms user. App checks user's `onboarding_complete` status (via a guard or initial check). Since it's `false`, navigates to `OnboardingScreen`.
7.  **User Action:** Completes the 8 steps of the `OnboardingScreen` (Welcome, Name/Username, DOB/Gender, Profile Picture, Location, Shared Activities, My Interests [Optional], Summary), tapping "Next" or "Back" as needed. Input is validated at each step where necessary. `OnboardingViewModel` state is updated throughout.
    * **App Response:** `PageView` navigates between steps. UI reflects entered data. Username availability checked. Location permission requested if needed. Reusable selector widgets used.
8.  **User Action:** Reviews information on the Summary step, taps "Complete Onboarding".
    * **App Response:** `OnboardingViewModel` validates final state. Calls `UserRepository.updateUser` to save all collected data to the `Users` table (updating the minimal record). Sets `onboarding_complete` flag in `SharedPreferences`. Navigates user to the main `DiscoverDashboard` (or `HomeScreen`).

### 12.2. Existing User Login Flow

1.  **User Action:** Opens app.
    * **App Response:** App checks local auth state. If not logged in, displays `WelcomeScreen`.
2.  **User Action:** Taps "Login".
    * **App Response:** Navigates to `LoginScreen`.
3.  **User Action:** Enters Email and Password, taps "Login".
    * **App Response:** `LoginViewModel` validates, calls `AuthRepository.signIn`. Supabase Auth confirms user. App checks `onboarding_complete` status. Since it's `true`, navigates to the main `DiscoverDashboard` (or `HomeScreen`).

### 12.3. Password Reset Flow

1.  **User Action:** On `LoginScreen`, taps "Forgot Password?".
    * **App Response:** Navigates to `ResetPasswordScreen`.
2.  **User Action:** Enters their registered email, taps "Send Reset Link".
    * **App Response:** `ResetPasswordViewModel` validates email format, calls `AuthRepository.resetPassword` (passing email and `redirectTo` URL). Shows loading indicator, then a confirmation message (e.g., SnackBar) instructing user to check email.
3.  **User Action:** User checks email, clicks the password reset link.
    * **App Response:** Link opens in a browser, displays Supabase default password reset page where the user can enter a new password.
4.  **User Action:** User returns to the app and logs in with their new password via the `LoginScreen`.

### 12.4. Discovering Users (Location-Based Map Flow)

1.  **User Action:** Navigates to the Discover tab (likely the default view in `HomeScreen`).
    * **App Response:** `DiscoverDashboard` loads. `DiscoverViewModel`'s `build` method initiates:
        * Checks location service/permission status.
        * Attempts to fetch the user's current precise location (`getCurrentLocation`).
        * If GPS fails, attempts to fetch the user's profile and geocode their saved `city`.
        * If successful (GPS or city location found), the provider enters `AsyncData` state with the initial map center (`currentMapCenter`) and `userInitialLocation`. A background task (`_fetchUsersForArea`) is triggered to find initial nearby users.
        * If all location attempts fail, the provider enters `AsyncError`, and the UI displays the relevant error prompt (e.g., enable service, grant permission).
        * If loading profile/geocoding city, the provider stays `AsyncLoading`, and the UI shows a loading indicator.
2.  **App Response (On Success):** `DiscoverDashboard` UI builds:
    * Map (`OSMFlutter`) is displayed, centered on the `currentMapCenter` from the ViewModel state (moves to correct location via `onMapIsReady`).
    * The persistent Results Bottom Sheet (`DraggableScrollableSheet`) appears in its collapsed state (e.g., `initialChildSize: 0.15`). Initially, it shows a loading indicator or "No users found".
    * FABs (Center Location, Search Users) are visible above the collapsed sheet.
3.  **App Response (Markers Appear):** The initial `_fetchUsersForArea` completes. `DiscoverViewModel` updates `state.nearbyUsers`. `DiscoverDashboard` rebuilds, and the `_updateMapMarkers` function is called (via `ref.listen` or `onMapIsReady`), displaying avatar markers on the map for the fetched users. The Results Bottom Sheet list also populates with medium `UserProfileCard`s.
4.  **User Action:** User pans or zooms the map and stops.
    * **App Response:** `MapController`'s `listenerRegionIsChanging` fires. The debounced listener in `_DiscoverDashboardState` triggers `viewModel.mapPositionChanged(center, zoom)`. The ViewModel updates `currentMapCenter`/`currentZoom` and calls `_fetchUsersForArea` with the new parameters. The `nearbyUsers` state updates, and `_updateMapMarkers` updates the markers on the map. The list in the bottom sheet also updates.
5.  **User Action:** User taps on a user avatar marker on the map.
    * **App Response:** `OSMFlutter`'s `onGeoPointClicked` callback (or `onSingleTap` override logic) identifies the corresponding `User`. A modal bottom sheet is presented (`_showUserProfileSheet`), displaying the full `ProfileScreen` for the tapped user. The FABs remain visible (as the results sheet state hasn't changed).
6.  **User Action:** User dismisses the profile bottom sheet (drags down or taps 'X').
    * **App Response:** The modal sheet closes, returning the user to the map view.
7.  **User Action:** User taps on the map background.
    * **App Response:** `onSingleTap` override is triggered. It checks if the results bottom sheet is expanded (`_sheetController.size > threshold`). If yes, it calls `_sheetController.animateTo(minSize)` to collapse the sheet. The `_handleSheetScroll` listener detects the size change and updates the ViewModel (`isBottomSheetExpanded = false`), causing the FABs to reappear.
8.  **User Action:** User drags the results bottom sheet upwards.
    * **App Response:** The sheet expands. The `_handleSheetScroll` listener detects the size change exceeding the threshold and calls `viewModel.setBottomSheetExpanded(true)`. The FABs become hidden (`Visibility(visible: !isSheetExpanded)`). The user can scroll the `ListView` of `UserProfileCard`s.
9.  **User Action:** User taps on a `UserProfileCard` within the results bottom sheet list.
    * **App Response:** Navigates to the full `ProfileScreen` for that user (`context.beamToNamed('/profile/${user.userId}')`).
10. **User Action:** User taps the "Center Location" FAB.
    * **App Response:** Calls `locationService.getCurrentLocation`. If successful, calls `_mapController.moveTo()` to center the map. Triggers `_triggerFetchForCurrentMapView` which calls `viewModel.mapPositionChanged`, leading to a user fetch for the current location.
11. **User Action:** User taps the "Search Users" FAB.
    * **App Response:** Navigates to the `SearchPage` (`context.beamToNamed('/search')`).

### 12.5. Searching via City Flow

1.  **User Action:** On `DiscoverDashboard`, taps the City Search icon in the `AppBar`.
    * **App Response:** Navigates to `CitySearchPage`.
2.  **User Action:** Enters a city name, selects a suggestion (or confirms input), adjusts the distance filter (design TBD). Taps "Show Results".
    * **App Response (Design):** `CitySearchPage` pops and returns the selected `GeoPoint` and search `radius` (in meters).
3.  **User Action:** User is back on `DiscoverDashboard`.
    * **App Response (Design):** `DiscoverDashboard` receives the result, calls `viewModel.centerMapOnLocation(geoPoint)` (passing the new center and potentially adjusting zoom/radius state). The ViewModel updates `currentMapCenter`, triggers `_fetchUsersForArea`, map moves via `ref.listen`, and markers/list update.

### 12.6. Filtering Discover Results Flow

1.  **User Action:** On `DiscoverDashboard`, taps the Filter icon in the `AppBar`.
    * **App Response:** Navigates to `FilterPage`, passing any currently active filters from `DiscoverViewModel`.
2.  **User Action:** Adjusts Age Range slider and/or Gender radio buttons. Taps "Apply Filters".
    * **App Response (Design):** `FilterPage` pops and returns the selected filter values (`minAge`, `maxAge`, `gender`).
3.  **User Action:** User is back on `DiscoverDashboard`.
    * **App Response (Design):** `DiscoverDashboard` receives the result, calls `viewModel.applyFiltersAndSearch(...)`. The ViewModel stores the filters and calls `_fetchUsersForArea` with the new filter parameters. Map markers and bottom sheet list update.

### 12.7. Searching Users (via SearchPage) Flow

1.  **User Action:** On `DiscoverDashboard`, taps the Search Users FAB.
    * **App Response:** Navigates to `SearchPage`.
2.  **User Action (Option A - Name/Username):** Types in the "Search Members" text field.
    * **App Response (Design):** Autocomplete suggestions (user summaries) appear below the field. User taps a suggestion.
    * **App Response (Design):** Navigates directly to the selected user's `ProfileScreen`.
3.  **User Action (Option B - Interests/Activities):**
    * Selects "Shared Activities" using the selector widget/bottom sheet.
    * Selects/adds "My Interests" keywords using the selector widget.
    * Selects a distance filter ("Local", "Regional", "Global").
    * Optionally taps the Filter icon to set Age/Gender filters via `FilterPage`. Active filters are displayed as chips.
    * Taps "Search by Interests" button.
    * **App Response (Design):** `SearchViewModel` calls `UserRepository.searchUsers` with all specified criteria (keywords, activities, distance filter relative to current user, age, gender). Results are displayed (either on `SearchPage` itself or by navigating to a dedicated results screen - TBD).

### 12.8. Sending a Friend Request

1.  **User Action:** Views the `ProfileScreen` of a user they are not friends with and whose status is not 'pending' or 'received'.
    * **App Response:** The `ProfileScreen` displays an "Add Friend" button.
2.  **User Action:** Taps the "Add Friend" button.
    * **App Response:** The UI calls the relevant ViewModel method (e.g., `profileViewModel.sendFriendRequest(profileUserId)`).
    * **App Response:** The ViewModel calls `UserRepository.sendFriendRequest`.
    * **App Response:** The Repository creates a new row in the `Friendships` table with `status_a_to_b` = 'pending' (for the sender) and `status_b_to_a` = 'received' (for the recipient). It also triggers `NotificationRepository.createNotification` for the recipient (`notification_type` = 'friend_request').
    * **App Response:** The UI on the `ProfileScreen` optimistically updates the button state to show "Request Sent" (or similar disabled state).

### 12.9. Accepting a Friend Request

1.  **User Action (Option A):** Views the `ProfileScreen` of a user who sent them a request (`getFriendshipStatus` returns 'received').
    * **App Response:** The `ProfileScreen` displays "Accept Request" and "Decline Request" buttons.
2.  **User Action (Option B):** Views the `DiscoverNotificationsPage` and taps on a 'friend_request' notification.
    * **App Response:** A dialog appears with options like "View Profile", "Accept", "Decline". User might tap "View Profile" first, leading to Option A.
3.  **User Action:** Taps "Accept Request" (on profile or in dialog).
    * **App Response:** UI calls `ViewModel.acceptFriendRequest(senderUserId)`.
    * **App Response:** ViewModel calls `UserRepository.acceptFriendRequest`.
    * **App Response:** Repository updates the existing `Friendships` row, setting both `status_a_to_b` and `status_b_to_a` to 'accepted'. It may also trigger a `friend_accept` notification for the original sender.
    * **App Response:** The relevant notification is marked as read/deleted. UI on both users' views of each other's profiles updates to "Friends" state. Friend lists are updated on next fetch.

### 12.10. Declining or Cancelling a Friend Request

1.  **User Action (Decline):** Recipient taps "Decline" on `ProfileScreen` or notification dialog.
    * **App Response:** UI calls `ViewModel.declineFriendRequest(senderUserId)`. ViewModel calls `UserRepository.declineFriendRequest`. Repository *deletes* the corresponding row from the `Friendships` table. UI updates profile buttons to "Add Friend". Notification is handled.
2.  **User Action (Cancel):** Sender views recipient's `ProfileScreen` where button shows "Request Sent". Sender taps button/menu option to "Cancel Request".
    * **App Response:** UI calls `ViewModel.cancelFriendRequest(recipientUserId)`. ViewModel calls `UserRepository.cancelFriendRequest`. Repository *deletes* the corresponding row from the `Friendships` table. UI updates profile buttons to "Add Friend".

### 12.11. Sending/Receiving a One-on-One Message

1.  **User Action (Initiate):** User taps "Message" button on a friend's `ProfileScreen`, OR taps an existing conversation in `ChatListScreen`.
    * **App Response (From Profile):** UI calls `ViewModel.getOrCreateChatRoom(friendId)`. ViewModel calls `ChatRepository.getOrCreateChatRoom`. Repository checks if a room exists; if not, creates the `ChatRoom` record and adds both users to `ChatRooms_Users`. The `chatRoomId` is returned. UI navigates to `ChatScreen` using the `chatRoomId`.
    * **App Response (From List):** UI navigates directly to `ChatScreen` using the conversation's existing `chatRoomId`.
2.  **User Action (Send):** In `ChatScreen`, user types a message and taps "Send".
    * **App Response:** `ChatViewModel` calls `ChatRepository.sendMessage`. Repository inserts the message into the `Messages` table. If the recipient is not a friend, it also triggers `NotificationRepository.createNotification` ('new_message_nonfriend') for the recipient.
    * **App Response:** UI optimistically displays the sent message. Supabase Realtime (setup in `ChatViewModel`) pushes the new message to the recipient's `ChatScreen` (if open) and updates their `ChatListScreen` (unread count).
3.  **User Action (Receive):** User receives a new message.
    * **App Response:** If `ChatScreen` for that room is open, Realtime listener adds the message to the UI. If `ChatScreen` is not open, `ChatListScreen` listener updates the last message snippet and unread count. An in-app notification ('new_message_nonfriend') appears if applicable.
4.  **User Action (Read):** User opens a `ChatScreen` with unread messages.
    * **App Response:** `ChatViewModel` calls `ChatRepository.markAsRead(chatRoomId, userId)` which updates `last_read_at` in `ChatRooms_Users`. Unread indicators in `ChatListScreen` are cleared.

### 12.12. Blocking/Unblocking a User

1.  **User Action (Block):** Views another user's `ProfileScreen`, taps 3-dot menu, selects "Block User", confirms in dialog.
    * **App Response:** UI calls `ViewModel.blockUser(profileUserId)`. ViewModel calls `UserRepository.blockUser`. Repository updates/inserts `Friendship` row, setting status fields to 'blocked'. UI updates `ProfileScreen` to show "Blocked" state, disabling interaction buttons. Blocked user is immediately filtered from future searches/lists.
2.  **User Action (Unblock):** Views `BlockedUsersScreen`, taps "Unblock" next to a user. OR views a blocked user's `ProfileScreen`, taps "Unblock".
    * **App Response:** UI calls `ViewModel.unblockUser(blockedUserId)`. ViewModel calls `UserRepository.unblockUser`. Repository updates `Friendship` row, setting relevant status back to 'none'. UI on `ProfileScreen` reverts to default non-friend state (e.g., "Add Friend" button). User reappears in searches/lists on next fetch.

### 12.13. Hiding/Unhiding a User

1.  **User Action (Hide):** Views another user's `ProfileScreen`, taps 3-dot menu, selects "Hide User".
    * **App Response:** UI calls `ViewModel.hideUser(profileUserId)`. ViewModel calls `UserRepository.hideUser`. Repository inserts row into `HiddenUsers` table. User disappears from Discover/Search results on next fetch. UI might show a temporary confirmation (e.g., SnackBar).
2.  **User Action (Unhide):** Views `HiddenUsersScreen`, taps "Unhide" next to a user.
    * **App Response:** UI calls `ViewModel.unhideUser(hiddenUserId)`. ViewModel calls `UserRepository.unhideUser`. Repository deletes row from `HiddenUsers` table. User reappears in Discover/Search results on next fetch.

### 12.14. Saving/Unsaving a User Profile

1.  **User Action:** Views a medium `UserProfileCard` (e.g., in Discover bottom sheet, Saved Profiles list) or potentially the full `ProfileScreen`. Taps the Heart icon (filled or outline).
    * **App Response:** The `UserProfileCard`'s internal state handler (`_toggleSave`) calls `UserRepository.saveUser` or `unsaveUser`. Repository inserts/deletes row from `SavedUsers` table. The heart icon optimistically toggles its state. Success/error message shown via SnackBar. Saved list updates on next fetch.

### 12.15. Creating an Event

1.  **User Action:** On `EventsDashboard`, taps the Floating Action Button (+).
    * **App Response:** Navigates to `CreateEventScreen`.
2.  **User Action:** Fills out the event creation form:
    * Enters Event Name (Required).
    * Enters Description (Optional).
    * Selects Category (Required).
    * Selects Date & Time (Required).
    * Enters Place Name (Required, uses autocomplete/map picker). `placeLocation` (GeoPoint) is derived.
    * Enters Capacity (Optional).
    * Uploads Image Banner (Optional, uses `ProfileImagePicker` logic).
3.  **User Action (Option 1):** Taps "Save as Draft".
    * **App Response:** `CreateEventViewModel` validates input. Calls `EventRepository.createEvent` setting `status` to `'draft'`. An associated `ChatRoom` is created in the backend. User is likely navigated back to the "My Events" tab (Drafts section) with a confirmation message.
4.  **User Action (Option 2):** Taps "Publish".
    * **App Response:** `CreateEventViewModel` validates input. Calls `EventRepository.createEvent` setting `status` to `'published'`. An associated `ChatRoom` is created. The creator (`userId`) is automatically added to the `Attendees` table and the event's `ChatRoom` (`ChatRooms_Users`). User is navigated to the newly created `EventDetailPage`.

### 12.16. Browse and Finding Events

1.  **User Action:** Navigates to `EventsDashboard`, selects the "Browse Events" tab.
    * **App Response:** `EventsDashboardViewModel` fetches initial events using `EventRepository.getEventsByLocation` based on the user's current default/detected city and a default date range (e.g., upcoming week). Displays results as `EventCard`s.
2.  **User Action:** Taps the City Search button in the `AppBar`.
    * **App Response:** Navigates to `CitySearchPage`.
3.  **User Action:** Selects a city and distance on `CitySearchPage`, taps "Show Results".
    * **App Response:** Navigates back to `EventsDashboard`. ViewModel updates search location/radius and re-fetches events using `getEventsByLocation`. `EventCard` list updates.
4.  **User Action:** Taps Date Filter chips (e.g., "Today", "This Weekend", "Next Week") below the `AppBar`.
    * **App Response:** ViewModel updates date filter and re-fetches events using `getEventsByLocation`. `EventCard` list updates.
5.  **User Action:** Taps on an `EventCard`.
    * **App Response:** Navigates to the `EventDetailPage` for the selected event.

### 12.17. Viewing Event Details

1.  **User Action:** Navigates to `EventDetailPage` (from Browse, My Events, Notification, etc.).
    * **App Response:** `EventDetailViewModel` fetches the specific event data (`EventRepository.getEventById`), attendee list (`EventRepository.getAttendees`), and the current user's attendance status (`EventRepository.isUserAttendingEvent`).
    * **App Response:** "Details" tab displays event name, banner image, host info (tappable to profile), date, time, location (with map/directions links), description, category, current attendee count (with link/button to show full list, perhaps in a bottom sheet), and capacity (if set).
    * **App Response:** "Messages" tab is displayed *only* if the current user is the host or an attendee. It contains the `ChatScreen` for the event.
    * **App Response:** Action button(s) at the bottom are displayed based on user status: "Join Event" (if not attending, not host, not full), "Leave Event" (if attending), "Edit Event" / "Cancel Event" (if host).

### 12.18. Joining an Event

1.  **User Action:** On `EventDetailPage` for an event they are not attending and which is not full, taps "Join Event".
    * **App Response:** UI calls `ViewModel.joinEvent(eventId)`. ViewModel calls `EventRepository.joinEvent`. Repository adds the user to the `Attendees` table (incrementing `join_count` if rejoining), adds user to the event's `ChatRoom`. Triggers `new_attendee` notification for the host.
    * **App Response:** UI updates the button to "Leave Event", increments attendee count, and makes the "Messages" tab visible.

### 12.19. Leaving an Event

1.  **User Action:** On `EventDetailPage` for an event they are attending, taps "Leave Event". Confirms in a dialog.
    * **App Response:** UI calls `ViewModel.leaveEvent(eventId)`. ViewModel calls `EventRepository.leaveEvent`. Repository removes user from `Attendees` table and the event's `ChatRoom`.
    * **App Response:** UI updates the button to "Join Event", decrements attendee count, and hides the "Messages" tab.

### 12.20. Viewing "My Events"

1.  **User Action:** Navigates to `EventsDashboard`, selects the "My Events" tab.
    * **App Response:** `EventsDashboardViewModel` fetches events using `EventRepository.getMyEventsWithLastActivity`. Events are displayed using `EventCard`s, grouped under section headers: "I'm Hosting", "I'm Going", "Draft Events", "Past Events". Cards may display unread notification badges.

### 12.21. Editing an Event

1.  **User Action:** Host views their own event on `EventDetailPage`, taps "Edit Event".
    * **App Response:** Navigates to `EditEventScreen`, pre-filled with current event data.
2.  **User Action:** Host modifies details, taps "Save Changes".
    * **App Response:** `EditEventViewModel` validates, calls `EventRepository.updateEvent`. Optionally triggers `event_update` notification (deferred?). Navigates back to `EventDetailPage`.

### 12.22. Cancelling/Deleting an Event

1.  **User Action:** Host views own event, taps "Cancel Event" (if published) or "Delete Draft" (if draft). Confirms in dialog.
    * **App Response:** ViewModel calls `EventRepository.deleteEvent` (for drafts) or potentially `EventRepository.updateEvent` (to set status to 'cancelled'). Triggers `event_cancelled` notification if applicable. User navigated away from detail page (e.g., back to `EventsDashboard`).

### 12.23. Creating a Wishlist Item

1.  **User Action:** On `WishlistsDashboard`, taps the Floating Action Button (+).
    * **App Response:** Navigates to `CreateWishlistItemScreen`.
2.  **User Action:** Fills out the wishlist item creation form:
    * Enters Item Name (Required).
    * Enters Description (Optional).
    * Enters related URL (Optional).
    * Selects Category (Required).
    * Enters Date (Either specific `eventDate` via picker OR general text `generalDate`, one is required).
    * Enters Place Name (Optional, uses autocomplete). `placeLocation` (GeoPoint) is derived if place selected.
3.  **User Action (Option 1):** Taps "Save as Draft".
    * **App Response:** `CreateWishlistViewModel` validates input. Calls `WishlistRepository.createWishlistItem` setting `status` to `'draft'`. Associated `ChatRoom` is created. User navigated back to "My Wishlists" tab (Drafts section).
4.  **User Action (Option 2):** Taps "Publish".
    * **App Response:** `CreateWishlistViewModel` validates input. Calls `WishlistRepository.createWishlist` setting `status` to `'published'`. Associated `ChatRoom` created. Creator added to chat. User navigated to the new `WishlistItemDetailPage`.

### 12.24. Browse Wishlist Feed

1.  **User Action:** Navigates to `WishlistsDashboard`, selects the "Feed" tab.
    * **App Response:** `WishlistsDashboardViewModel` fetches items from the user's friends using `WishlistRepository.getFeedItems` (applying necessary RLS). Displays results as `WishlistCard`s.
2.  **User Action:** Taps on a `WishlistCard`.
    * **App Response:** Navigates to the `WishlistItemDetailPage` for the selected item.

### 12.25. Viewing Wishlist Item Details

1.  **User Action:** Navigates to `WishlistItemDetailPage`.
    * **App Response:** `WishlistItemDetailViewModel` fetches item data (`getWishlistItemById`), list of interested users/statuses (`getInterestedUsers`), and current user's status (`checkInterestedUser`).
    * **App Response:** "Details" tab displays item info (name, creator avatar/name link, description, category, date, place, etc.) and the list/count of interested users.
    * **App Response:** "Messages" tab displays the `ChatScreen` *if* the current user is the creator OR has expressed interest ('interested', 'lets_go', 'going').
    * **App Response:** Action buttons displayed based on user status: "I'm Interested" / "Let's Go!" / "Going" / "Leave Interest".
    * **App Response:** For creator: "Edit Item", "Mark as Completed" / "Delete Draft", "Convert to Event" buttons displayed.

### 12.26. Expressing/Changing Interest in Wishlist Item

1.  **User Action:** On `WishlistItemDetailPage`, taps "I'm Interested", "Let's Go!", or "Going" button.
    * **App Response:** UI calls `ViewModel.expressInterest(itemId, newStatus)`. ViewModel calls `WishlistRepository.insertInterestedUser` (if first time) or `updateInterestedUser` (if changing status). Repository updates `InterestedUsers` table. Triggers `new_interest` notification for creator. If status moves to 'lets_go' or 'going', user is added to the item's `ChatRoom`.
    * **App Response:** UI updates button states. "Messages" tab may become visible. Interested users list updates.

### 12.27. Removing Interest in Wishlist Item

1.  **User Action:** On `WishlistItemDetailPage` where user has interest, taps "Leave Interest" (or similar). Confirms if needed.
    * **App Response:** UI calls `ViewModel.removeInterest(itemId)`. ViewModel calls `WishlistRepository.removeInterestedUser`. Repository deletes row from `InterestedUsers` and removes user from the item's `ChatRoom`.
    * **App Response:** UI updates button state back to "I'm Interested". "Messages" tab may become hidden. Interested users list updates.

### 12.28. Viewing "My Wishlists"

1.  **User Action:** Navigates to `WishlistsDashboard`, selects "My Wishlists" tab.
    * **App Response:** `WishlistsDashboardViewModel` fetches items using `WishlistRepository.getMyWishlistItems`. Items displayed using `WishlistCard`s, grouped under "I'm Hosting", "I'm Interested" (showing status), "Draft Wishlists", "Past/Completed Wishlists". Notification badges may show on cards.

### 12.29. Editing a Wishlist Item

1.  **User Action:** Creator views own item on `WishlistItemDetailPage`, taps "Edit".
    * **App Response:** Navigate to `EditWishlistItemScreen`, pre-filled.
2.  **User Action:** Creator modifies details, taps "Save Changes".
    * **App Response:** `EditWishlistViewModel` validates, calls `WishlistRepository.updateWishlistItem`. Navigate back to `WishlistItemDetailPage`. Optionally trigger `wishlist_update` notification.

### 12.30. Completing/Deleting a Wishlist Item

1.  **User Action:** Creator views own item on `WishlistItemDetailPage`. Taps "Mark as Completed" (if published) or "Delete Draft" (if draft). Confirms.
    * **App Response:** ViewModel calls `updateWishlistItem` (sets `status='completed'`) or `deleteWishlistItem`. Item removed from active feeds/lists. Optionally trigger `wishlist_completed` notification.

### 12.31. Converting Wishlist Item to Event

1.  **User Action:** Creator views own item on `WishlistItemDetailPage`, taps "Convert to Event".
    * **App Response:** Navigate to `CreateEventScreen`, pre-filling relevant fields (Name, Description, Category, Date, Place Name/Location) from the `WishlistItem` data. User completes Event creation. Optionally, notify interested users about the new Event.

### 12.32. Viewing Notifications (Related to Events/Wishlists)

1.  **User Action:** User sees a badge on the "Events" or "Wishlists" bottom navigation item, or on a specific `EventCard` or `WishlistCard` in their "My Events"/"My Wishlists" tab.
    * **App Response:** User taps the badge/card.
2.  **User Action:** Navigates to the specific `EventNotificationsPage` or `WishlistItemNotificationsPage`.
    * **App Response:** Screen displays list of notifications specific to that item (e.g., "Jane Doe joined your event", "John expressed 'Let's Go' for your wishlist item").
3.  **User Action:** Taps a notification.
    * **App Response:** Marks notification as read. Navigates to relevant context (e.g., attendee's profile, event/wishlist detail page).


# 13. Non-Functional Requirements (NFRs)

This section outlines the quality attributes, constraints, and general requirements for the Watermelon application that are not tied to specific user features but describe *how* the system should operate.

## 13.1. Performance

* **UI Responsiveness:** The application interface must feel responsive to user interactions (taps, swipes, scrolling) with minimal perceived lag (aiming for common targets like <200ms for interactions and smooth 60fps scrolling on target devices).
* **Load Times:** Key data loading operations (e.g., initial user/event discovery, profile loading, chat history) should complete within acceptable timeframes under typical network conditions. Specific benchmarks may be defined during testing.
* **Resource Consumption:** The application should be reasonably efficient in terms of battery, memory, and CPU usage to provide a good user experience without unduly impacting the user's device.

## 13.2. Scalability

* **Backend:** The Supabase backend infrastructure (PostgreSQL, Auth, Storage) is expected to handle anticipated user growth for the MVP phase. Database queries and schema design should consider indexing and efficiency to support scaling.
* **Frontend Architecture:** The MVVM architecture, combined with Riverpod and the Repository pattern, is designed to be modular and allow for the addition of new features and scaling of the codebase with manageable complexity.

## 13.3. Reliability & Availability

* **Backend Uptime:** Relies on Supabase's service level agreements (SLAs) for database, auth, and storage availability.
* **Application Stability:** The application should aim for high stability with minimal crashes. Robust error handling (`Either`, `try...catch`, specific `Failure` types) is implemented throughout the data access and ViewModel layers. Network connectivity issues should be handled gracefully where possible.

## 13.4. Security

* **Authentication:** Handled securely by Supabase Auth, including password hashing and mandatory email verification. Session management follows Supabase client library standards.
* **Authorization (RLS):** Row Level Security policies are implemented extensively on Supabase database tables (`Users`, `Friendships`, `Events`, `WishlistItems`, `ChatRooms`, `Messages`, etc.) and Storage buckets (`user-images`, `event-images`) to ensure users can only access and modify data according to defined permissions (e.g., own profile, own messages, friend's wishlists).
* **Data Transmission:** Communication between the app and Supabase backend is secured via HTTPS/SSL managed by Supabase.
* **Input Validation:** Client-side validation is implemented on forms to prevent malformed data submission.

## 13.5. Usability

* **Intuitive Navigation:** The application utilizes Beamer for a clear, declarative navigation structure based on logical user flows and features.
* **Consistency:** A consistent design language, terminology (e.g., "Shared Activities"), and interaction patterns should be maintained across all features.
* **User Feedback:** The application must provide clear feedback for user actions, including loading states (indicators), success confirmations (SnackBars, messages), and user-friendly error messages (derived from `Failure` types).
* **Accessibility (Basic):** Adherence to basic mobile accessibility principles (e.g., sufficient contrast ratios, adequate tap target sizes) is expected. A full accessibility audit is deferred post-MVP.

## 13.6. Maintainability

* **Code Quality:** Adherence to Dart and Flutter best practices, effective linting rules, and clean code principles (e.g., SOLID where applicable).
* **Modularity:** The feature-first folder structure, separation of concerns (MVVM, Repository/Service layers), and use of reusable widgets contribute to maintainability.
* **Documentation:** Code comments for complex logic. This PRD serves as architectural documentation.

## 13.7. Portability

* **Cross-Platform:** Built with Flutter, targeting iOS and Android from the outset.