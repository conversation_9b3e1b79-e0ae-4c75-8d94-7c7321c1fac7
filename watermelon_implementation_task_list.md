# Watermelon App - Implementation Task List

## Phase 1: Core Foundation

### 1. Authentication (`lib/features/auth/`)

* **1.1. `AuthRepository` Final Check:**
    - [X] Verify `signUp` method only accepts `email`, `password`.
    - [X] Verify `signUp` calls `SupabaseService.signUp` (without `emailRedirectTo` for now).
    * [X] **Task:** Create minimal user record and connect to `Users` table
    - [X] Verify `signUp` handles `AuthException` and `PostgrestException`.
    - [X] Verify `resetPassword` calls `SupabaseService.resetPassword` (without `redirectTo` for now).
    - [X] Verify `currentUser` is a synchronous getter returning `User?`.

* **1.2. `SignUpViewModel` (`signup_viewmodel.dart`):**
    - [X] Ensure file exists, uses `@riverpod`, extends `_$SignUpViewModel`, returns `FutureOr<User?>` from `build`.
    - [X] Ensure `build` watches `authRepositoryProvider`.
    - [X] Ensure `signUp({email, password})` method signature is correct.
    - [X] **Task:** Implement `signUp` method logic:
        - Set state to `AsyncLoading`.
        - Call `_authRepository.signUp`.
        - Use `fold` to handle `Either` result.
        - Set state to `AsyncData(user)` on `Right`.
        * [X] **Task:** Connect to User table
        - Set state to `AsyncError(failure)` on `Left`.
        - Return the original `Either`.

* **1.3. `SignupScreen` (`signup_screen.dart`):**
    - [X] Ensure file exists (`ConsumerStatefulWidget`).
    - [X] Ensure `Form` with `GlobalKey` is present.
    * [X] **Task:** Create controllers for Email and Password input fields.
    * [X] **Task:** Initialize controllers.
    * [X] **Task:** Dispose controllers.
    - [X] **Task:** Implement `build` method:
        - Add `ref.watch(signUpViewModelProvider)` for state.
        - Add `ref.read(signUpViewModelProvider.notifier)` for view model instance.
        - Add `ref.listen` to handle `AsyncData` (navigate to `/onboarding`, show success SnackBar) and `AsyncError` (show error SnackBar).
        - Build `Scaffold` & `AppBar`.
        - Build `Form` widget.
        - Build Email `TextFormField` with controller, validation (required, email format), keyboard type, decoration.
        - Build Password `TextFormField` with controller, validation (required, min length), obscureText toggle logic, decoration.
        * [X] **Task:** Set up UI for password visibility toggle
        - Build "Sign Up" `ElevatedButton`:
            * Disable based on `state.isLoading`.
            * Show `CircularProgressIndicator` when `state.isLoading`.
            * `onPressed`: Validate form (`_formKey`), call `viewModel.signUp(email, password)`.
        - Build "Already have account?" `TextButton` navigating to `/login`.

* **1.4. `LoginViewModel` (`login_viewmodel.dart`):**
    - [X] Ensure file exists, uses `@riverpod`, extends `_$LoginViewModel`, returns `FutureOr<User?>` from `build`.
    - [X] Ensure `build` watches `authRepositoryProvider`.
    * [X] **Task:** Create the LoginViewModel class
    - [X] Ensure `signIn({email, password})` method signature is correct.
    - [X] **Task:** Implement `signIn` method logic:
        - Set state to `AsyncLoading`.
        - Call `_authRepository.signIn`.
        - Use `fold` to handle `Either` result.
        - Set state to `AsyncData(user)` on `Right`.
        - Set state to `AsyncError(failure)` on `Left`.
        - Return the original `Either`.

* **1.5. `LoginScreen` (`login_screen.dart`):**
    - [X] Ensure file exists (`ConsumerStatefulWidget`).
    * [X] **Task:** Create the LoginScreen UI structure
    - [X] Ensure `Form` with `GlobalKey` is present.
    - [X] **Task:** Add `TextEditingController` for Email, Password.
    * [X] **Task:** Dispose controllers.
    - [X] **Task:** Implement `build` method:
        - Add `ref.watch` for state (`isLoading`).
        - Add `ref.read` for notifier.
        - Add `ref.listen` to handle `AsyncData` (check `onboarding_complete`, navigate to `/home` or `/onboarding`) and `AsyncError` (show error SnackBar).
        * [X] **Task:** Set up UI for password visibility toggle
        * [X] **Task:** Build `Scaffold` & `AppBar`.
        - Build `Form` widget.
        - Build Email `TextFormField`.
        - Build Password `TextFormField`.
        * [X] **Task:** Build "Forgot Password?" `TextButton` navigating to `/reset-password`.
        * [X] **Task:** Build "Login" `ElevatedButton` (handle loading state, call `viewModel.signIn`).
        * [X] **Task:** Build "Don't have account?" `TextButton` navigating to `/signup`.

* **1.6. `ResetPasswordViewModel` (`reset_password_viewmodel.dart`):**
    - [ ] Ensure file exists, uses `@riverpod`, extends `_$ResetPasswordViewModel`, returns `FutureOr<void>` from `build`.
    - [ ] Ensure `build` watches `authRepositoryProvider`, returns `null`.
    * **Task:** Create the ResetPasswordViewModel class
    - [ ] Ensure `resetPassword({email})` method signature is correct.
    - [ ] **Task:** Implement `resetPassword` method logic:
        - Set state to `AsyncLoading`.
        - Call `_authRepository.resetPassword`.
        - Use `fold` to handle `Either` result.
        - Set state to `AsyncData(null)` on `Right` (or keep previous state).
        - Set state to `AsyncError(failure)` on `Left`.
        - Return the original `Either`.

* **1.7. `ResetPasswordScreen` (`reset_password_screen.dart`):**
    - [ ] (Draft) Ensure file exists (`ConsumerStatefulWidget`).
    * [ ] **Task:** Create the ResetPasswordScreen UI structure
    - [ ] (Draft) Ensure `Form` with `GlobalKey` is present.
    - [ ] **Task:** Add `TextEditingController` for Email.
    - [ ] **Task:** Dispose controller.
    - [ ] **Task:** Implement `build` method:
        - Add `ref.watch` for state (`isLoading`).
        - Add `ref.read` for notifier.
        * [ ] **Task:** Add `ref.listen` to handle `AsyncError` (show error SnackBar) and potentially `AsyncData` (show success SnackBar "Check your email").
        - Build `Scaffold` & `AppBar`.
        - Build `Form` widget.
        - Build Email `TextFormField`.
        - Build "Send Reset Link" `ElevatedButton` (handle loading state, call `viewModel.resetPassword`).

* **1.8. `WelcomeScreen` (`welcome_screen.dart`):**
    - [X] Ensure file exists (`StatelessWidget` or `ConsumerWidget`).
    - [X] Ensure `build` method creates `Scaffold`, `AppBar`.
    * [ ] **Task:** Build UI elements (Welcome text).
    - [X] **Task:** Add "Login" button navigating to `/login` (`context.beamToNamed`).
    - [X] **Task:** Add "Sign Up" button navigating to `/signup` (`context.beamToNamed`).
    - [ ] Remove temporary "Go to Onboarding" button.

* **1.9. Onboarding (`lib/features/onboarding/`):**
    - [X] Ensure `OnboardingViewModel` exists (`AsyncNotifier<OnboardingState>`).
    - [X] Ensure `OnboardingState` (`@freezed`) exists with all fields (including `isUsernameChecking`, `usernameError`, `avatarType`, `generatedAvatarColor`).
    - [X] Ensure `OnboardingScreen` exists (`ConsumerStatefulWidget`).
    * [X] **Task:** Set up OnboardingScreen with controllers
    - [X] Ensure `build` uses `PageView`, handles `AsyncValue` state (`switch`), builds steps via helper methods.
    - [X] Ensure reusable widgets (`FullNameInputWidget`, `ProfileImagePicker`, `CitySelectorWidget`, `SharedActivitiesSelectorWidget`, `InterestsSelectorWidget`) exist and are used correctly in the helper build methods.
    * [X] **Task:** Create `_buildNameUsernamePage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildDobGenderPage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildProfilePicturePage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildLocationPage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildSharedActivitiesPage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildMyInterestsPage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Create `_buildSummaryPage` widget helper method and add logic for UI + ViewModel
    * [X] **Task:** Implement Full name validation
    * [X] **Task:** Implement Username validation (including async check via ViewModel).
    * [ ] **Task:** Implement Date of Birth validation.
    * [ ] **Task:** Implement Gender validation.
    * [ ] **Task:** Implement Profile Picture validation (any option selected).
    * [ ] **Task:** Implement Location validation (GPS or City required).
    * [ ] **Task:** Implement Shared Activities validation (min 1 required).
    * [ ] **Task:** Implement "Complete Onboarding" logic in ViewModel (validation, image upload, `updateUser` call, set `SharedPreferences` flag).
    * [ ] **Task:** Implement "Edit" button navigation on Summary page.
    * [ ] **Task:** Implement step progress indicator in `AppBar`.
    * [ ] **Task:** Test entire onboarding flow.

* **1.10. Navigation Setup (`lib/core/routes.dart`):**
  - [X] Ensure `routes.dart` exists with Beamer setup.
  - [X] Ensure `AuthLocation`, `OnboardingLocation`, `HomeLocation` (and others) are defined.
  - [X] Ensure routes map correctly to placeholder/implemented screens.
  - [X] Ensure required parameters are passed in `BeamPage` constructors.
  - [X] Ensure `guards` list is empty for `watermelon_draft`.
  * [X] **Task:** Create placeholder screens for ALL routes defined.

* **1.11. Core Files (`lib/core/`):**
  - [X] Ensure `providers.dart` defines providers for all Services & Repositories using `@riverpod`. Includes `currentUserProfileProvider`. Includes `sharedPreferencesProvider`.
  - [X] Ensure `errors.dart` defines `Failure` and all specific subtypes.
  - [X] Ensure `utils/avatar_utils.dart` contains avatar generation helpers.
  - [X] Ensure `utils/dialog_utils.dart` contains location permission dialog helpers.
  - [X] Ensure all `models` defined with `@freezed`.
  - [X] Run `build_runner` to ensure all generated files are up-to-date and error-free.
* [ ] **Task:** Set up RLS policy
* [X] **Task:** Set up Database functions
* [X] **Task:** Set up Storage
* [ ] **Task:** Set up Database triggers


## Phase 2: Core Profile and Discovery

### 4. Account Dashboard (`lib/features/profile/screens/account_dashboard.dart`, `lib/features/profile/viewmodels/account_viewmodel.dart`)

* **ViewModel (`AccountViewModel`):**
    * [ ] Create `AccountViewModel` class (`@riverpod`, `AsyncNotifier<User?>`).
    * [ ] Implement `build` method:
        * `watch` `currentUserProfileProvider`.
        * Return the `AsyncValue<User?>` directly (or handle its loading/error state if needed before returning initial data).
    * [ ] Implement `signOut()` method:
        * Call `AuthRepository.signOut`.
        * Handle `Either` result.
        * (Navigation handled by Auth state listener or guard).
    * [ ] Implement `updateDiscoverability(bool isDiscoverable)`:
        * Get current user ID.
        * Call `UserRepository.updateUserPartial` with `{'discoverable': isDiscoverable}`.
        * Handle `Either` result.
        * On success, invalidate `currentUserProfileProvider` using `ref.invalidate()` to force re-fetch of updated user data.
        * Return `Future<Either<Failure, Unit>>`.
    * [ ] Implement `updateSharedActivities(List<String> activityIds)`:
        * Get current user ID.
        * Call `UserRepository.updateUserPartial` with `{'shared_activities': activityIds}`.
        * Handle `Either` result.
        * On success, invalidate `currentUserProfileProvider`.
        * Return `Future<Either<Failure, Unit>>`.
* **Screen (`AccountDashboard`):**
    * [ ] Create `AccountDashboard` widget (`ConsumerWidget`).
    * [ ] Use `ref.watch(accountViewModelProvider)` to get `AsyncValue<User?>`.
    * [ ] Handle `AsyncLoading` (show `CircularProgressIndicator`) and `AsyncError` (show error message) states using `switch` or `.when`.
    * [ ] On `AsyncData(user)`:
        * Build `Scaffold` with `AppBar` ("Account").
        * Display User Info: Use `_buildAvatar` (from `avatar_utils`), `Text` for `user.fullName`, `Text` for `user.username`. Handle nulls.
        * Build "Edit Profile" `ListTile` or `ElevatedButton` -> `onTap` navigates to `/edit-profile`.
        * Build "View My Profile" `ListTile` or `ElevatedButton` -> `onTap` navigates to `/profile/:userId` (using `user.userId`).
        * Build "Discoverable" `ListTile`:
            * `title`: Text("Profile Discoverable").
            * `subtitle`: Text("Allow others to find you in search results.").
            * `trailing`: `Switch(value: user.discoverable ?? true, onChanged: (value) => ref.read(accountViewModelProvider.notifier).updateDiscoverability(value))`. Handle loading state during update.
        * Build "Activities to Share" `ListTile`:
            * `title`: Text("Activities to Share").
            * `subtitle`: Display selected activities (e.g., `user.sharedActivities?.join(', ')` or chips).
            * `trailing`: `Icon(Icons.edit)`.
            * `onTap`: Implement `_showSharedActivitiesSheet` helper function.
        * Build `_showSharedActivitiesSheet` helper method:
            * Uses `showModalBottomSheet`.
            * Contains `SharedActivitiesSelectorWidget`.
            * Pass `initialActivities: user.sharedActivities ?? []`.
            * Callback `onActivitiesChanged` calls `ref.read(accountViewModelProvider.notifier).updateSharedActivities(newActivities)`.
        * Build "Blocked Users" `ListTile` -> `onTap` navigates to `/blocked-users`.
        * Build "Hidden Users" `ListTile` -> `onTap` navigates to `/hidden-users`.
        * Build "Sign Out" `ElevatedButton`:
            * `onPressed` calls `ref.read(accountViewModelProvider.notifier).signOut()`.
            * Include confirmation dialog before signing out.

### 5. Edit Profile Screen (`lib/features/profile/screens/edit_profile_screen.dart`, `lib/features/profile/viewmodels/edit_profile_viewmodel.dart`)

* **ViewModel (`EditProfileViewModel`):**
    * [ ] Create `EditProfileViewModel` class (`@riverpod`, `AsyncNotifier<EditProfileState>`). Define `EditProfileState` (`@freezed`) to hold original user data, edited values, loading status, save status.
    * [ ] Implement `build`: Fetch initial data from `currentUserProfileProvider`. Populate initial `EditProfileState`.
    * [ ] Implement methods to update state fields as user edits (e.g., `updateFullName`, `updateCity`, `updateSharedActivities`, etc.). Set 'isDirty' flag.
    * [ ] Implement `saveChanges()`:
        * Validate edited data.
        * Upload new profile picture if changed (`UserRepository.uploadProfilePicture`).
        * Construct updated `User` object.
        * Call `UserRepository.updateUser`.
        * Handle `Either` result, update saving status in state.
        * On success, invalidate `currentUserProfileProvider`.
        * Return success/failure status.
* **Screen (`EditProfileScreen`):**
    * [ ] (Draft) Ensure placeholder screen and route (`/edit-profile`) exist.
    * [ ] Implement `EditProfileScreen` (`ConsumerStatefulWidget`).
    * [ ] Add `AppBar` with "Cancel" (pops, checks for unsaved changes) and "Save" (`onPressed` calls `viewModel.saveChanges`, handles loading/error, pops on success).
    * [ ] Implement "Unsaved Changes" confirmation dialog.
    * [ ] Build `SingleChildScrollView` containing a `Form`.
    * [ ] Use `ProfileImagePicker` (pass initial data, connect callbacks to ViewModel).
    * [ ] Use `FullNameInputWidget` (connect controller/`onChanged` to ViewModel).
    * [ ] Display Username (`Text`, read-only).
    * [ ] Add DOB Picker (connect to ViewModel).
    * [ ] Add Gender Selector (connect to ViewModel).
    * [ ] Use `CitySelectorWidget` (pass initial data, connect callback to ViewModel).
    * [ ] Use `SharedActivitiesSelectorWidget` (pass initial data, connect callback to ViewModel).
    * [ ] Use `InterestsSelectorWidget` (pass initial data, connect callback to ViewModel).

### 6. Location Services (`lib/core/services/location_service.dart`)

* [X] Fix all remaining errors/warnings related to `flutter_osm_plugin: ^1.3.6` and `geolocator`/`geocoding` integration, ensuring all methods function correctly as per documentation.

### 7. Discover Feature (`lib/features/discover/`)

* **ViewModel (`DiscoverViewModel`):**
    * [X] Refine `_calculateRadiusFromZoom` for more accuracy (or switch to using map bounds from `Region` object).
    * [X] Implement `applyFilters` method logic (store filters in state, call `_fetchUsersForArea`).
    * [X] Implement `centerMapOnLocation` method logic (handle result from `CitySearchPage`, update state, call `_fetchUsersForArea`).
* **Screen (`DiscoverDashboard`):**
    * [X] Finalize `_updateMapMarkers` logic:
        * [X] Ensure efficient clearing/adding/removing of markers.
        * [X] Ensure custom avatars (network, asset, generated) render reliably.
    * [X] Implement Marker Tap Handling in `onSingleTap` (or `onGeoPointClicked`):
        * [X] Implement robust proximity check to find tapped `User` from `nearbyUsers`.
        * [X] Call `_showUserProfileSheet` correctly.
    * [X] Implement Results Bottom Sheet UI:
        * [X] Use `DraggableScrollableSheet`.
        * [X] Implement `ListView.builder` with medium `UserProfileCard`s.
        * [X] Ensure scrolling works with drag controller.
        * [X] Handle empty/loading state for `nearbyUsers`.
    * [X] Implement Profile Modal Bottom Sheet (`_showUserProfileSheet`):
        * [X] Use `showMaterialModalBottomSheet`.
        * [X] Embed `ProfileScreen` widget, passing `userId`.
        * [X] Ensure correct dismissal.
    * [X] Implement FABs:
        * [X] Connect "Center" button to `viewModel.getCurrentLocation` (needs adding?) and `_mapController.moveTo`.
        * [X] Connect "Search" button navigates to `/search`.
        * [X] Ensure `Visibility` logic based on `state.isBottomSheetExpanded` works reliably.
    * [X] Connect AppBar Buttons:
        * [X] City Search: Navigate to `/city-search`. Handle returned `GeoPoint`/`radius` and call `viewModel.centerMapOnLocation`.
        * [X] Filter: Navigate to `/filter`. Handle returned filters (`minAge`, `maxAge`, `gender`) and call `viewModel.applyFiltersAndSearch`.
        * [X] Notifications: Navigate to `/notifications/discover`.
* **Screen (`CitySearchPage` - Placeholder -> Implementation):**
    * [X] Implement UI: `AppBar`, `TextFormField` for city input, `ListView` for suggestions/recent searches, `Slider` for distance, "Show Results" button.
    * [X] Implement ViewModel/State logic: Handle text input, call `LocationService.getCitySuggestions`, display suggestions, manage distance value, handle "Show Results" tap (pop with data).
* **Screen (`FilterPage` - Placeholder -> Implementation):**
    * [X] Implement UI: `AppBar` with Close/Reset, `RangeSlider` for Age, `RadioListTile`s for Gender, "Apply Filters" button.
    * [X] Implement ViewModel/State logic: Manage selected range/gender, handle Reset, handle Apply (pop with data).


## Phase 3: Core Social Features

### 8. User Profiles (`lib/features/profile/`)

* **`ProfileViewModel` (`profile_viewmodel.dart`):**
    * [ ] Create `ProfileViewModel` (`@riverpod`, `AsyncNotifier<ProfileState>`). Needs to accept `userId` argument (`@Riverpod(keepAlive: false) Future<ProfileState> profileViewModel(ProfileViewModelRef ref, String userId)`).
    * [ ] Define `ProfileState` (`@freezed`) to hold `User? user`, `String? friendshipStatus`, `bool isSaved`, `bool isBlockedByMe`, `bool isHiddenByMe`, etc.
    * [ ] Implement `build(String userId)` method:
        * Fetch the profile user's data: `await userRepository.getUser(userId)`.
        * Fetch friendship status: `await userRepository.getFriendshipStatus(currentUserId, userId)`.
        * Fetch saved status: `await userRepository.isUserSaved(currentUserId, userId)`.
        * Fetch block/hide status (can be derived from friendship status or separate checks).
        * Return `ProfileState` wrapped in `AsyncData` on success, handle errors by throwing `Failure` (puts provider in `AsyncError`).
    * [ ] Implement action methods (most call `UserRepository` and then `ref.invalidateSelf()` or invalidate `currentUserProfileProvider`):
        * `sendFriendRequest(String profileUserId)`
        * `acceptFriendRequest(String senderUserId)`
        * `declineFriendRequest(String senderUserId)`
        * `cancelFriendRequest(String recipientUserId)`
        * `blockUser(String profileUserId)`
        * `unblockUser(String profileUserId)`
        * `hideUser(String profileUserId)`
        * `unhideUser(String profileUserId)`
        * `saveUser(String profileUserId)`
        * `unsaveUser(String profileUserId)`
        * `waveUser(String profileUserId)` (Creates notification)
    * [ ] Manage loading/error states for actions if necessary (e.g., showing indicator on buttons).
* **`ProfileScreen` (`profile_screen.dart`):**
    * [ ] Create `ProfileScreen` widget (`ConsumerWidget`). Accepts `required String userId`.
    * [ ] Watch the state: `final stateAsync = ref.watch(profileViewModelProvider(userId));`.
    * [ ] Handle `AsyncLoading` and `AsyncError` states (show loading indicator or error message).
    * [ ] On `AsyncData(profileState)`:
        * Build `Scaffold` with `AppBar` (show user's name, 3-dot menu).
        * Implement Image Carousel placeholder (fetch multiple images later).
        * Build main info section (Full Name, Age, Gender, City).
        * Build "About Me" section (display formatted Questionnaire answers - requires fetching answers).
        * Build "Shared Activities" section (display chips using `_buildActivityChips` helper).
        * Build "My Interests" section (display chips using `_buildInterestChips` helper).
        * Build Action Buttons Row: Conditionally display buttons ("Add Friend", "Request Sent", "Accept"/"Decline", "Message", "Wave") based on `profileState.friendshipStatus`. Wire `onPressed` to `viewModel` methods.
        * Build 3-Dot Menu: Add "Share Profile", "Block User" (or "Unblock"), "Hide User" (or "Unhide") options. Wire actions to `viewModel` methods (add confirmation dialogs for block/hide).
        * Implement Blocked/Hidden Overlay: If profile user is blocked/hidden, display an overlay message instead of the main content.

### 9. Friendships (UI Integration)

* **`ProfileScreen`:** (Covered above) Ensure action buttons correctly reflect status and trigger ViewModel methods.
* **`DiscoverNotificationsPage`:**
    * [ ] Ensure 'friend_request' notification tile has functional "Accept" and "Decline" buttons calling the relevant `UserRepository` methods (potentially via a shared `NotificationViewModel` or directly).
    * [ ] Ensure 'friend_accept' notification tile navigates to the friend's profile on tap.
* **`FriendsListScreen` (`lib/features/friends/screens/friends_list_screen.dart` - Placeholder -> Implementation):**
    * [ ] Create `FriendsListViewModel` (`@riverpod`, `AsyncNotifier<List<User>>`).
    * [ ] Implement `build`: Calls `UserRepository.getFriends(currentUserId)`. Handles errors.
    * [ ] Create `FriendsListScreen` (`ConsumerWidget`).
    * [ ] Watch `friendsListViewModelProvider`. Handle loading/error/empty states.
    * [ ] Build `ListView` displaying condensed `UserProfileCard` for each friend.
    * [ ] Tapping a card navigates to `/profile/:userId`.
* **`FriendsDashboard`:** Ensure the "Friends" tab displays the `FriendsListScreen`.

### 10. One-on-One Chat (`lib/features/chat/`)

* **`ChatListViewModel` (`chat_list_viewmodel.dart`):**
    * [ ] Create `ChatListViewModel` (`@riverpod`, likely `AsyncNotifier<List<ChatRoomSummary>>`). Define `ChatRoomSummary` model if needed (or use `ChatRoom` + fetch last message separately).
    * [ ] Implement `build`: Calls `ChatRepository.getChatRooms(currentUserId)`.
    * [ ] **Task:** Set up Supabase Realtime subscription in `build` (or separate method) to listen for changes in the `Messages` table *and* potentially `ChatRooms_Users` (for `last_read_at`). Trigger state refresh on relevant updates.
    * [ ] **Task:** Implement logic to calculate unread counts based on `last_read_at`.
* **`ChatListScreen` (`chat_list_screen.dart`):**
    * [ ] Create `ChatListScreen` (`ConsumerWidget`). Shown in `FriendsDashboard`.
    * [ ] Watch `chatListViewModelProvider`. Handle loading/error/empty states.
    * [ ] Build `ListView` displaying chat items (Avatar, Name, Last Message Snippet, Timestamp, Unread Indicator).
    * [ ] Tapping an item navigates to `/chat/:chatRoomId`.
* **`ChatViewModel` (`chat_viewmodel.dart`):**
    * [ ] Create `ChatViewModel` (`@riverpod`, `AsyncNotifier<List<Message>>`). Accepts `chatRoomId` (`@Riverpod(keepAlive: false) ... build(String chatRoomId)`).
    * [ ] Implement `build(String chatRoomId)`: Calls `ChatRepository.getMessages(chatRoomId)` for initial batch. Calls `markAsRead` on initial load.
    * [ ] Set up Supabase Realtime subscription for *new* messages in *this specific* `chatRoomId`. Append new messages to the state list.
    * [ ] Implement `sendMessage(String text)`: Calls `ChatRepository.sendMessage`, handles errors.
    * [ ] Implement `loadMoreMessages()`: Fetches older messages using pagination.
    * [ ] Implement `markAsRead()`: Calls `ChatRepository.markAsRead`.
* **`ChatScreen` (`chat_screen.dart`):**
    * [ ] Create `ChatScreen` (`ConsumerStatefulWidget`). Accepts `required String chatRoomId`.
    * [ ] Implement `build` method:
        * Watch `chatViewModelProvider(chatRoomId)`. Handle loading/error.
        * Build `AppBar` with recipient info (requires fetching participant data).
        * Build `ListView` displaying `Message` items (reversed, styled for sender/receiver). Implement pagination trigger.
        * Build message input area (`TextFormField` controller, `IconButton` for send). Send button calls `viewModel.sendMessage`.
        * Call `viewModel.markAsRead()` when screen is visible.

### 11. Saving Profiles (`lib/features/profile/`)

* **`UserProfileCard` (Medium):** Heart icon interaction already implemented in stateful widget.
* **`SavedProfilesScreen` (`lib/features/friends/screens/saved_profiles_screen.dart` - Placeholder -> Implementation):**
    * [ ] Create `SavedProfilesViewModel` (`@riverpod`, `AsyncNotifier<List<User>>`).
    * [ ] Implement `build`: Calls `UserRepository.getSavedUsers(currentUserId)`. Handles errors.
    * [ ] Create `SavedProfilesScreen` (`ConsumerWidget`). Shown in `FriendsDashboard`.
    * [ ] Watch `savedProfilesViewModelProvider`. Handle loading/error/empty states.
    * [ ] Build `ListView` displaying medium `UserProfileCard` for each saved user.
    * [ ] Ensure heart icon on these cards correctly reflects status and calls `_toggleSave` (which updates the repository and local card state). Tapping card navigates to `/profile/:userId`.


## Phase 4: Events & Wishlists

Implement the core features for user-generated activities. Events are the initial implementation focus, but tasks for the complete design of both are included for the `watermelon_draft` blueprint.

### Events Feature (`lib/features/events/`)

#### 12. Core Event Dependencies & Setup:
  * [ ] **Verify:** `public."Events"`, `public."Attendees"`, `public."Categories"` tables exist and match PRD schema.
  * Create `Events` table in Supabase with specified columns and constraints.
  * Create `Attendees` table in Supabase with specified columns and constraints.
  * Create `Categories` table in Supabase with specified columns and constraints.
  * [ ] **Verify:** `Event`, `Attendee`, `Category` models (`lib/core/models/`) exist and match schema.
  * [ ] **Verify:** `EventRepository`, `SupabaseService`, `ChatRepository`, `NotificationRepository` have all required method signatures defined.
  * [ ] Define `eventRepositoryProvider` using `@riverpod` in `providers.dart` (or feature providers).
  * [ ] Define `categoriesProvider` using `@riverpod` (e.g., `FutureProvider` or `AsyncNotifier`) to fetch all categories via `EventRepository.getCategories`. Set `keepAlive: true`.

#### 13. `EventCard` Widget (`lib/features/events/widgets/event_card.dart`):
  * [ ] Create `EventCard` widget file.
  * [ ] Define `EventCard` as `ConsumerWidget`.
  * [ ] Define parameters: `required Event event`, `VoidCallback? onTap`, `int? unreadCount`.
  * [ ] Implement UI Structure (`Card`, `InkWell`).
  * [ ] Display Banner Image (`CachedNetworkImage` or placeholder).
  * [ ] Display Event Name, Formatted Date/Time, Location Snippet.
  * [ ] Display event status (i.e. draft, publish, cancel)
  * [ ] Display Category Name (watch `categoriesProvider`, find by ID, handle loading/error).
  * [ ] Display Attendee Count (requires data in `Event` object or separate fetch).
  * [ ] Conditionally display notification `Badge` based on `unreadCount`.
  * [ ] Implement `onTap` navigation using Beamer (`/events/:eventId`).

#### 14. `EventsDashboardViewModel` (`lib/features/events/viewmodels/events_dashboard_viewmodel.dart`):
  * [ ] Create `EventsDashboardViewModel` file.
  * [ ] Define `EventsDashboardState` (`@freezed`) with fields for filters (`selectedCity`, `searchRadius`, `dateFilter`), event lists (`browseEvents`, `hostedEvents`, etc.), loading flags (`isLoadingBrowse`, `isLoadingMyEvents`), and error strings (`errorBrowse`, `errorMyEvents`).
  * [ ] Create `EventsDashboardViewModel` class (`@riverpod`, `AsyncNotifier<EventsDashboardState>`).
  * [ ] Implement `build` method:
    * Watch required repositories/services.
    * Fetch initial "Browse Events" (call helper `_fetchBrowseEvents`).
    * Fetch initial "My Events" (call helper `_fetchMyEvents`).
    * Return initial `EventsDashboardState` in `AsyncData` or handle initial errors by `throw`ing.
  * [ ] Implement `_fetchBrowseEvents({city, radius, dateRange})`:
    * Set `isLoadingBrowse = true`.
    * Call `eventRepository.getEventsByLocation`.
    * Handle `Either` result.
    * Update `browseEvents`, `isLoadingBrowse`, `errorBrowse` in state (`AsyncData`).
  * [ ] Implement `_fetchMyEvents()`:
    * Set `isLoadingMyEvents = true`.
    * Call `eventRepository.getMyEventsWithLastActivity`.
    * Handle `Either`.
    * Categorize results into `hostedEvents`, `attendingEvents`, `draftEvents`, `pastEvents`.
    * Update corresponding state lists, `isLoadingMyEvents`, `errorMyEvents`.
  * [ ] Implement `updateCitySearch(String city, double radius)`: Updates state filters, calls `_fetchBrowseEvents`.
  * [ ] Implement `updateDateFilter(DateTimeRange? range)`: Updates state filters, calls `_fetchBrowseEvents`.
  * [ ] Implement `refreshBrowse()`: Calls `_fetchBrowseEvents` with current filters.
  * [ ] Implement `refreshMyEvents()`: Calls `_fetchMyEvents`.

#### 15. `EventsDashboard` Screen (`lib/features/events/screens/events_dashboard.dart`):
  * [ ] (Draft) Ensure placeholder exists.
  * [ ] Create `EventsDashboard` widget (`ConsumerStatefulWidget`).
  * [ ] Add `TabController` in `initState`, dispose in `dispose`.
  * [ ] Implement `build`:
    * Watch `eventsDashboardViewModelProvider`.
    * Handle main `AsyncLoading` / `AsyncError` states.
    * Build `Scaffold`.
    * Build `AppBar`: Title, City Search button (show selected city, opens `/city*search`), Date Filter Chips (connect to `viewModel.updateDateFilter`).
    * Build `TabBar` ("Browse Events", "My Events").
    * Build `FloatingActionButton` (navigates `/events/create`).
  * [ ] Build `TabBarView`:
    * **"Browse Events" Tab:**
      * Use `RefreshIndicator` calling `viewModel.refreshBrowse()`.
      * Show loading based on `state.isLoadingBrowse`.
      * Show error based on `state.errorBrowse`.
      * Build `ListView`/`GridView` using `EventCard` with `state.browseEvents`. Handle empty list.
    * **"My Events" Tab:**
      * Use `RefreshIndicator` calling `viewModel.refreshMyEvents()`.
      * Show loading based on `state.isLoadingMyEvents`.
      * Show error based on `state.errorMyEvents`.
      * Build `ListView` with sticky headers (or sections) for "Hosting", "Going", "Drafts", "Past". Use `EventCard` with categorized state lists. Handle empty sections.

#### 16. `CreateEventViewModel` (`lib/features/events/viewmodels/create_event_viewmodel.dart`):
  * [ ] Create `CreateEventViewModel` file.
  * [ ] Define `CreateEventState` (`@freezed`) holding form fields (name, desc, categoryId, dateTime, placeName, geoPoint, capacity, imageFile), validation errors map, `isSaving`, `saveError`.
  * [ ] Create `CreateEventViewModel` class (`@riverpod`, `Notifier<CreateEventState>`).
  * [ ] Implement `build`: Return initial default `CreateEventState`.
  * [ ] Implement field update methods (`updateName`, `updateDescription`, etc.).
  * [ ] Implement `saveEvent(bool isPublished)`:
    * Set `isSaving = true`.
    * Validate inputs, update validation errors state if needed.
    * If `imageFile` exists, call `eventRepository.uploadEventImage`. Store URL or handle failure.
    * Call `chatRepository.createChatRoom` (or ensure Event repo handles it). Store ID or handle failure.
    * Construct `Event` object (`status = publish ? 'published' : 'draft'`).
    * Call `eventRepository.createEvent`.
    * If published & successful, call `eventRepository.joinEvent` (for creator) and `chatRepository.addUserToChatRoom`.
    * Handle `Either` results from repo calls, update `isSaving`/`saveError`.
    * Return success/failure indicator.

#### 17. `CreateEventScreen` (`lib/features/events/screens/create_event_screen.dart`):
  * [ ] (Draft) Ensure placeholder exists.
  * [ ] Create `CreateEventScreen` (`ConsumerStatefulWidget`).
  * [ ] Add `TextEditingController`s, `FormKey`. Init/dispose controllers.
  * [ ] Implement `build`: Watch VM provider. Build `Scaffold`, `AppBar`. Build `SingleChildScrollView` + `Form`.
  * [ ] Build Name `TextFormField`. Connect to VM.
  * [ ] Build Description `TextFormField`. Connect to VM.
  * [ ] Build Category `DropdownButtonFormField` (watch `categoriesProvider`). Connect to VM.
  * [ ] Build Date/Time Picker. Connect to VM.
  * [ ] Build Place Name `TextFormField` (consider map picker integration button?). Connect to VM.
  * [ ] Build Capacity `TextFormField` (numeric). Connect to VM.
  * [ ] Build Image Picker input (e.g., button + preview). Connect to VM.
  * [ ] Display validation errors from VM state on fields.
  * [ ] Build "Save as Draft" button (`onPressed` calls `viewModel.saveEvent(false)`).
  * [ ] Build "Publish" button (`onPressed` calls `viewModel.saveEvent(true)`).
  * [ ] Handle `state.isSaving` (e.g., disable buttons, show indicator). Show `saveError` via SnackBar. Navigate on success.

#### 18. `EventDetailViewModel` (`lib/features/events/viewmodels/event_detail_viewmodel.dart`):
  * [ ] Create `EventDetailViewModel` file.
  * [ ] Define `EventDetailState` (`@freezed`) holding `Event? event`, `List<User> attendees`, `bool isAttending`, `bool isHost`, `bool isLoadingAttendees`.
  * [ ] Create `EventDetailViewModel` (`@riverpod`, `AsyncNotifier<EventDetailState>`). Accepts `eventId`.
  * [ ] Implement `build(String eventId)`: Fetch event (`getEventById`), attendance status (`isUserAttendingEvent`), determine `isHost`. Fetch attendees (`getAttendees`) asynchronously *after* initial event load or in parallel. Handle loading/error states.
  * [ ] Implement `joinEvent()`: Call repo, handle errors, invalidate self to refresh.
  * [ ] Implement `leaveEvent()`: Call repo, handle errors, invalidate self to refresh.
  * [ ] Implement `WorkspaceAttendees()` (called separately if needed).

#### 19. `EventDetailPage` (`lib/features/events/screens/event_detail_page.dart`):
  * [ ] (Draft) Ensure placeholder exists. Accepts `eventId`.
  * [ ] Create `EventDetailPage` (`ConsumerStatefulWidget` for TabController).
  * [ ] Initialize/dispose `TabController`.
  * [ ] Implement `build`: Watch `eventDetailViewModelProvider(eventId)`. Handle main loading/error. Build `Scaffold`, `AppBar` (Event Name, potentially Edit button for host). Build `TabBar` ("Details", "Messages").
  * [ ] Build `TabBarView`:
    * **"Details" Tab:** Display event info (Banner, Host, Date/Time, Location, Desc, Category). Show Attendee count/button (opens modal sheet with attendee list). Handle loading state for attendees.
    * **"Messages" Tab:** Conditionally display `ChatScreen(chatRoomId: state.event.chatRoomId)` based on `state.isAttending` or `state.isHost`.
  * [ ] Build conditional action button/bar (Join/Leave/Edit) calling ViewModel methods. Handle button loading states.

#### 20. `EditEventViewModel` (`lib/features/events/viewmodels/edit_event_viewmodel.dart`):
  * [ ] Create `EditEventViewModel` class (`@riverpod`, `AsyncNotifier<EditEventState>`). Accepts `eventId`.
  * [ ] Define `EditEventState` (`@freezed`) to hold `initialEvent` (fetched), current form field values (name, desc, category, date, place, etc.), `isSaving`, `saveError`.
  * [ ] Implement `build(String eventId)`: Fetch the existing event data using `EventRepository.getEventById(eventId)`. Populate initial state fields. Handle loading/error.
  * [ ] Implement methods to update individual form field values in the state.
  * [ ] Implement `saveChanges()`: Validate inputs, upload new image if changed, construct updated `Event` object, call `EventRepository.updateEvent`, handle `Either` result, update `isSaving`/`saveError` state.
  * [ ] Implement `deleteEvent()` / `cancelEvent()`: Call relevant `EventRepository` method, handle result, potentially manage navigation state.

#### 21. `EditEventScreen` (`lib/features/events/screens/edit_event_screen.dart`):
  * [ ] (Draft) Ensure placeholder screen and route (`/events/:eventId/edit`) exist.
  * [ ] Implement `EditEventScreen` (`ConsumerStatefulWidget`). Accepts `eventId`.
  * [ ] Add necessary `TextEditingController`s, initialize in `initState` based on initial data from ViewModel, dispose in `dispose`.
  * [ ] Implement `build`: Watch `EditEventViewModel(eventId)`. Handle overall loading/error state.
  * [ ] Build `Scaffold` and `AppBar` ("Edit Event", "Cancel" button, "Save" button).
  * [ ] Implement "Cancel" button logic (pop, check for unsaved changes).
  * [ ] Implement "Save" button logic (calls `viewModel.saveChanges()`, handles loading state, shows success/error, pops on success).
  * [ ] Build `SingleChildScrollView` + `Form`.
  * [ ] Build input fields similar to `CreateEventScreen`, pre-filled with data from ViewModel state. Connect inputs to ViewModel update methods. Display validation errors.
  * [ ] Add "Cancel Event" / "Delete Draft" button at the bottom, calling ViewModel methods (include confirmation dialog).

#### 22. `EventNotificationsViewModel` (`lib/features/events/viewmodels/event_notifications_viewmodel.dart`):
* [ ] Create `EventNotificationsViewModel` file.
* [ ] Define `EventNotificationsViewModel` class (`@riverpod`, `AsyncNotifier<List<UserNotification>>`). Ensure it accepts `eventId` argument (`@Riverpod(keepAlive: false) ... build(String eventId)`).
* [ ] Implement `build(String eventId)` method:
    * `watch` `notificationRepositoryProvider`.
    * `read` current user ID.
    * Call `_notificationRepository.getNotificationsForUser(currentUserId)`.
    * Handle `Either` result from repository.
    * On success (`Right(allNotifications)`):
        * Filter the list to include only notifications where `notification.eventId == eventId` AND `notification_type` is relevant (e.g., 'new_attendee', 'event_message', 'event_cancelled').
        * Sort the filtered list (e.g., `createdAt` descending).
        * Return the filtered list.
    * On failure (`Left(failure)`): `throw failure` to set provider state to `AsyncError`.
* [ ] Implement `markAsRead(String notificationId)` method:
    * Call `_notificationRepository.markNotificationAsRead(notificationId)`.
    * Handle `Either` result (log error on failure).
    * On success, call `ref.invalidateSelf()` to trigger a refetch and UI update.
* [ ] Implement `deleteNotification(String notificationId)` method:
    * Call `_notificationRepository.deleteNotification(notificationId)`.
    * Handle `Either` result (log error on failure).
    * On success, call `ref.invalidateSelf()` to trigger a refetch and UI update (or update state list locally).

#### 23. `EventNotificationsPage` (`lib/features/events/screens/event_notifications_page.dart`):
  * [ ] (Draft) Ensure placeholder screen exists. Accepts `required String eventId`.
  * [ ] Implement `EventNotificationsPage` as `ConsumerWidget`.
  * [ ] Implement `build` method:
      * `watch` `eventNotificationsViewModelProvider(eventId)`.
      * Use `switch` on the `AsyncValue` state to handle Loading/Error/Data.
      * Build `Scaffold` with `AppBar` (Title: "Event Notifications").
      * **Error State:** Display a user-friendly error message.
      * **Loading State:** Display a `CircularProgressIndicator`.
      * **Empty State:** If data is empty list, display "No notifications for this event."
      * **Data State:** Build `ListView.builder`:
          * `itemCount`: `notifications.length`.
          * `itemBuilder`: Return a `Dismissible` widget wrapping a `NotificationListTile`.
              * **`Dismissible`:**
                  * `key`: `ValueKey(notification.notificationId)`.
                  * `onDismissed`: Call `ref.read(eventNotificationsViewModelProvider(eventId).notifier).deleteNotification(notification.notificationId)`.
                  * `background`: Configure swipe background (e.g., red with delete icon).
              * **`NotificationListTile`:** (Define or use shared widget `lib/widgets/notification_list_tile.dart`)
                  * Pass `notification` object.
                  * Display sender avatar (fetch user from `notification.relatedUserId`), notification content, formatted time ago (`timeago` package).
                  * Indicate read/unread status visually (e.g., background color, dot).
                  * Implement `onTap`:
                      * Call `ref.read(eventNotificationsViewModelProvider(eventId).notifier).markAsRead(notification.notificationId)`.
                      * Navigate using `context.beamToNamed` based on `notification.notificationType` and related IDs (e.g., `/profile/:relatedUserId` for `new_attendee`, `/chat/:chatRoomId` for `event_message`).

### Wishlists Feature (`lib/features/wishlists/`)

*(Tasks mirror the Events structure)*

#### 24. Core Wishlist Dependencies & Setup:
  * [ ] **Verify:** `public."WishlistItems"`, `public."InterestedUsers"` tables exist.
  * [ ] Create `WishlistItems` table in Supabase with specified columns and constraints.
  * [ ] Create `InterestedUsers` table in Supabase with specified columns and constraints.
  * [ ] **Verify:** `WishlistItem`, `InterestedUser` models exist.
  * [ ] **Verify:** `WishlistRepository`, `SupabaseService`, `ChatRepository`, `NotificationRepository` methods exist.
  * [ ] Define `wishlistRepositoryProvider` in `providers.dart`.

#### 25. `WishlistCard` Widget (`lib/features/wishlists/widgets/wishlist_card.dart`):
  * [ ] Create `WishlistCard` (`ConsumerWidget`). Params (`item`, `onTap`).
  * [ ] Implement UI: Item Name, Creator Avatar, Category Name, Date Snippet. `onTap`.

#### 26. `WishlistsDashboardViewModel` (`lib/features/wishlists/viewmodels/wishlists_dashboard_viewmodel.dart`):
  * [ ] Create VM/State (`AsyncNotifier<WishlistsDashboardState>`). State holds `feedItems`, categorized `myWishlistItems`, loading/error flags.
  * [ ] Implement `build` (fetch feed/my items).
  * [ ] Implement `_fetchFeedItems`, `_fetchMyWishlists`.
  * [ ] Implement refresh methods.

#### 27. `WishlistsDashboard` Screen (`lib/features/wishlists/screens/wishlists_dashboard.dart`):
  * [ ] Create Screen (`ConsumerStatefulWidget`+TabController).
  * [ ] Implement `build`: Watch VM. `Scaffold`, `AppBar`, `TabBar` ("Feed", "My Wishlists"), FAB (`/wishlists/create`), `TabBarView`.
  * [ ] Build "Feed" Tab (`ListView` of `WishlistCard`s).
  * [ ] Build "My Wishlists" Tab (Categorized `ListView` of `WishlistCard`s).

#### 28. `CreateWishlistViewModel` (`lib/features/wishlists/viewmodels/create_wishlist_viewmodel.dart`):
  * [ ] Create VM/State (`Notifier<CreateWishlistState>`). State holds form fields, validation, saving state.
  * [ ] Implement field update methods.
  * [ ] Implement `saveWishlistItem(bool isPublished)` (Validate, Create Chat, Create Item repo call, Add creator to chat).

#### 29. `CreateWishlistItemScreen` (`lib/features/wishlists/screens/create_wishlist_item_screen.dart`):
  * [ ] Implement Screen (`ConsumerStatefulWidget`). Add controllers/key.
  * [ ] Implement `build`: Watch VM. `Scaffold`, `AppBar`. `Form`. Inputs (Name, Desc, URL, Category, Date[Specific/General], Place). Connect inputs. Save Draft/Publish buttons call VM. Handle loading/error.

#### 30. `WishlistItemDetailViewModel` (`lib/features/wishlists/viewmodels/wishlist_item_detail_viewmodel.dart`):
  * [ ] Create VM (`AsyncNotifier<WishlistItemDetailState>`). Accepts `wishlistId`.
  * [ ] Define State (`@freezed`) holding `item`, `interestedUsers`, `currentUserInterestStatus`, `isCreator`.
  * [ ] Implement `build`: Fetch item, interest status, interested users.
  * [ ] Implement `expressInterest(String status)`, `removeInterest()`, `convertToEvent()`, `markCompleted()`, `deleteItem()`.

#### 31. `WishlistItemDetailPage` (`lib/features/wishlists/screens/wishlist_item_detail_page.dart`):
  * [ ] Implement Screen (`ConsumerWidget`). Accepts `wishlistId`.
  * [ ] Implement `build`: Watch VM. Handle loading/error. `Scaffold`, `AppBar`. `TabBar` ("Details", "Messages").
  * [ ] Build "Details" Tab: Display item info, interested users list.
  * [ ] Build "Messages" Tab: Conditionally display `ChatScreen`.
  * [ ] Build conditional action buttons calling VM methods.

#### 32. `EditWishlistViewModel` (`lib/features/wishlists/viewmodels/edit_wishlist_viewmodel.dart`):
  * [ ] Create `EditWishlistViewModel` class (`@riverpod`, `AsyncNotifier<EditWishlistState>`). Accepts `wishlistItemId`.
  * [ ] Define `EditWishlistState` (`@freezed`) holding `initialItem`, current form field values, `isSaving`, `saveError`.
  * [ ] Implement `build(String wishlistItemId)`: Fetch existing item data using `WishlistRepository.getWishlistItemById(wishlistItemId)`. Populate initial state. Handle loading/error.
  * [ ] Implement methods to update individual form field values in the state.
  * [ ] Implement `saveChanges()`: Validate inputs, construct updated `WishlistItem`, call `WishlistRepository.updateWishlistItem`, handle `Either`, update state.
  * [ ] Implement `deleteItem()` / `markCompleted()` methods calling repo and refreshing state.

#### 33. `EditWishlistItemScreen` (`lib/features/wishlists/screens/edit_wishlist_item_screen.dart`):
  * [ ] (Draft) Ensure placeholder screen and route (`/wishlists/:wishlistId/edit`) exist.
  * [ ] Implement `EditWishlistItemScreen` (`ConsumerStatefulWidget`). Accepts `wishlistItemId`.
  * [ ] Add necessary `TextEditingController`s, initialize/dispose.
  * [ ] Implement `build`: Watch `EditWishlistViewModel(wishlistId)`. Handle loading/error. Build `Scaffold`, `AppBar` ("Edit Wishlist Item", "Cancel", "Save").
  * [ ] Implement Cancel/Save button logic (similar to Edit Event).
  * [ ] Build `SingleChildScrollView` + `Form`.
  * [ ] Build input fields similar to `CreateWishlistItemScreen`, pre-filled from VM state. Connect inputs to VM update methods. Display validation errors.
  * [ ] Add "Delete Item" / "Mark Completed" button at the bottom, calling ViewModel methods (include confirmation dialog).

#### 34. `WishlistNotificationsViewModel` (`lib/features/wishlists/viewmodels/wishlist_notifications_viewmodel.dart`):
* [ ] Create `WishlistNotificationsViewModel` file.
* [ ] Define `WishlistNotificationsViewModel` class (`@riverpod`, `AsyncNotifier<List<UserNotification>>`). Ensure it accepts `wishlistItemId` argument (`@Riverpod(keepAlive: false) ... build(String wishlistItemId)`).
* [ ] Implement `build(String wishlistItemId)` method:
    * `watch` `notificationRepositoryProvider`.
    * `read` current user ID.
    * Call `_notificationRepository.getNotificationsForUser(currentUserId)`.
    * Handle `Either` result.
    * On success (`Right(allNotifications)`):
        * Filter list for matching `wishlist_item_id` AND relevant types (e.g., 'new_interest', 'wishlist_message', 'wishlist_completed').
        * Sort the filtered list.
        * Return filtered list.
    * On failure (`Left(failure)`): `throw failure`.
* [ ] Implement `markAsRead(String notificationId)` method (calls repo, invalidates self).
* [ ] Implement `deleteNotification(String notificationId)` method (calls repo, invalidates self).

#### 35. `WishlistItemNotificationsPage` (`lib/features/wishlists/screens/wishlist_item_notifications_page.dart`):
* [ ] (Draft) Ensure placeholder screen exists. Accepts `required String wishlistItemId`.
* [ ] Implement `WishlistItemNotificationsPage` as `ConsumerWidget`.
* [ ] Implement `build` method:
    * `watch` `wishlistNotificationsViewModelProvider(wishlistItemId)`.
    * Use `switch` for Loading/Error/Data states.
    * Build `Scaffold` with `AppBar` (Title: "Wishlist Notifications").
    * **Error State:** Display error message.
    * **Loading State:** Display `CircularProgressIndicator`.
    * **Empty State:** Display "No notifications for this wishlist item."
    * **Data State:** Build `ListView.builder`:
        * `itemCount`: `notifications.length`.
        * `itemBuilder`: Return `Dismissible` wrapping `NotificationListTile`.
            * **`Dismissible`:** Configure `key`, `onDismissed` (calls `viewModel.deleteNotification`), `background`.
            * **`NotificationListTile`:** Pass `notification`. Display avatar, content, time, read status. Implement `onTap`:
                * Call `viewModel.markAsRead`.
                * Navigate based on `notificationType` (e.g., `/profile/:relatedUserId`, `/wishlists/:wishlistId`, `/chat/:chatRoomId`).


## Phase 5: Notifications (In-App Only for MVP Implementation)

Implement the system for creating, fetching, displaying, and managing in-app notifications triggered by various user interactions. Push notification integration (FCM/APNS) is deferred.

### 36. Notification Infrastructure (Verify & Finalize)

* **Database Table (`UserNotifications`):**
    * [ ] Confirm table exists with correct schema (columns, types, FKs) and RLS policies (users can CRUD own notifications).
* **Data Model (`UserNotification`):**
    * [ ] Confirm `UserNotification.dart` model exists and matches the table schema.
* **Repository (`NotificationRepository`):**
    * [ ] Confirm repository exists and all methods are defined (`createNotification`, `getNotificationsForUser`, `getUnreadNotifications`, `getTotalUnreadNotificationCount`, `markNotificationAsRead`, `markAllNotificationsAsRead`, `deleteNotification`). Ensure they return `Future<Either<Failure, T>>`.
* **Service (`SupabaseService`):**
    * [ ] Confirm corresponding methods exist and correctly interact with the `UserNotifications` table or RPCs.
* **Providers (`core/providers.dart` or `notifications_providers.dart`):**
    * [ ] Define `notificationRepositoryProvider` using `@riverpod`.
    * [ ] Define `totalUnreadNotificationsProvider = StreamProvider<int>(...)` or `FutureProvider<int>(...)` that uses `NotificationRepository.getTotalUnreadNotificationCount` (consider if a stream is needed via Supabase Realtime, or if periodic/event-driven refresh is sufficient for MVP).
    * [ ] Define feature-specific notification list providers using `@riverpod` (e.g., `discoverNotificationsViewModelProvider`, `eventNotificationsViewModelProvider(eventId)`, `wishlistNotificationsViewModelProvider(wishlistId)`).

### 37. Notification Creation Trigger Points (Integrate)

* [ ] Ensure `NotificationRepository.createNotification` is correctly called with appropriate data within the relevant methods of other repositories:
    * `UserRepository.sendWave` (type: `new_wave`)
    * `UserRepository.sendFriendRequest` (type: `friend_request`)
    * `UserRepository.acceptFriendRequest` (type: `friend_accept`)
    * `ChatRepository.sendMessage` (type: `new_message_nonfriend` - conditional)
    * `EventRepository.joinEvent` (type: `new_attendee` - conditional, to host)
    * `EventRepository.deleteEvent` (type: `event_cancelled` - conditional, to attendees)
    * `WishlistRepository.expressInterest` (type: `new_interest` - conditional, to creator)
    * `WishlistRepository.updateWishlistItem` (type: `wishlist_completed` - conditional, to interested users?)
    * `ChatRepository.sendMessage` (type: `event_message` / `wishlist_message` - conditional)

### 38. Notification Badges (UI)

* [ ] Implement `Badge` widget display on `BottomNavigationBar` items (Discover, Events, Wishlists, Friends/Chat) in `HomeScreen`. The badge visibility/count should be driven by `ref.watch` on relevant unread count providers (e.g., `totalUnreadNotificationsProvider` or feature-specific count providers).
* [ ] Implement `Badge` widget display on the Notifications `IconButton` in the `DiscoverDashboard` `AppBar`, driven by `ref.watch(totalUnreadNotificationsProvider)`.
* [ ] **Task:** Implement conditional `Badge` display on `EventCard` and `WishlistCard` widgets (within "My Events" / "My Wishlists" tabs), requiring the parent ViewModel (`EventsDashboardViewModel`, `WishlistsDashboardViewModel`) to fetch and pass the specific unread count for each item.

### 39. Discover Notifications (`lib/features/notifications/screens/discover_notifications_page.dart`)

* [ ] Create `DiscoverNotificationsViewModel` (`@riverpod`, `AsyncNotifier<List<UserNotification>>`).
    * Implement `build`: Calls `NotificationRepository.getNotificationsForUser`, filters for types (`new_wave`, `friend_request`, `friend_accept`, `new_message_nonfriend`).
    * Implement `markAsRead`, `deleteNotification` methods calling the repository and refreshing state.
* [ ] Implement `DiscoverNotificationsPage` (`ConsumerWidget`).
    * Watch `discoverNotificationsViewModelProvider`. Handle loading/error/empty states.
    * Build `ListView` displaying notifications using a reusable `NotificationListTile` widget.
    * Implement `NotificationListTile` (`lib/widgets/notification_list_tile.dart`?): Displays avatar (`related_user_id`), content, time ago, read/unread indicator. Handles `onTap`.
    * Connect `NotificationListTile.onTap` to: Mark notification read (`viewModel.markAsRead`), navigate/show dialog based on `notificationType` (Profile, Chat, Friend Request Dialog).
    * Wrap `NotificationListTile` in `Dismissible` for swipe-to-delete, calling `viewModel.deleteNotification`.
    * Add "Mark all as read" and/or "Clear all" buttons/actions (optional).

### 23. Event Notifications (`lib/features/events/screens/event_notifications_page.dart`)

* [ ] (Draft) Ensure placeholder screen exists. Accepts `required String eventId`.
* [ ] Implement `EventNotificationsPage` as `ConsumerWidget`.
* [ ] Implement `build` method:
  * `watch` `eventNotificationsViewModelProvider(eventId)`.
  * Use `switch` on the `AsyncValue` state to handle Loading/Error/Data.
  * Build `Scaffold` with `AppBar` (Title: "Event Notifications").
  * **Error State:** Display a user-friendly error message.
  * **Loading State:** Display a `CircularProgressIndicator`.
  * **Empty State:** If data is empty list, display "No notifications for this event."
  * **Data State:** Build `ListView.builder`:
      * `itemCount`: `notifications.length`.
      * `itemBuilder`: Return a `Dismissible` widget wrapping a `NotificationListTile`.
          * **`Dismissible`:**
              * `key`: `ValueKey(notification.notificationId)`.
              * `onDismissed`: Call `ref.read(eventNotificationsViewModelProvider(eventId).notifier).deleteNotification(notification.notificationId)`.
              * `background`: Configure swipe background (e.g., red with delete icon).
          * **`NotificationListTile`:** (Define or use shared widget `lib/widgets/notification_list_tile.dart`)
              * Pass `notification` object.
              * Display sender avatar (fetch user from `notification.relatedUserId`), notification content, formatted time ago (`timeago` package).
              * Indicate read/unread status visually (e.g., background color, dot).
              * Implement `onTap`:
                  * Call `ref.read(eventNotificationsViewModelProvider(eventId).notifier).markAsRead(notification.notificationId)`.
                  * Navigate using `context.beamToNamed` based on `notification.notificationType` and related IDs (e.g., `/profile/:relatedUserId` for `new_attendee`, `/chat/:chatRoomId` for `event_message`).

### 35. Wishlist Notifications (`lib/features/wishlists/screens/wishlist_item_notifications_page.dart`)

* [ ] (Draft) Ensure placeholder screen exists. Accepts `required String wishlistItemId`.
* [ ] Implement `WishlistItemNotificationsPage` as `ConsumerWidget`.
* [ ] Implement `build` method:
    * `watch` `wishlistNotificationsViewModelProvider(wishlistItemId)`.
    * Use `switch` for Loading/Error/Data states.
    * Build `Scaffold` with `AppBar` (Title: "Wishlist Notifications").
    * **Error State:** Display error message.
    * **Loading State:** Display `CircularProgressIndicator`.
    * **Empty State:** Display "No notifications for this wishlist item."
    * **Data State:** Build `ListView.builder`:
        * `itemCount`: `notifications.length`.
        * `itemBuilder`: Return `Dismissible` wrapping `NotificationListTile`.
            * **`Dismissible`:** Configure `key`, `onDismissed` (calls `viewModel.deleteNotification`), `background`.
            * **`NotificationListTile`:** Pass `notification`. Display avatar, content, time, read status. Implement `onTap`:
                * Call `viewModel.markAsRead`.
                * Navigate based on `notificationType` (e.g., `/profile/:relatedUserId`, `/wishlists/:wishlistId`, `/chat/:chatRoomId`).


## Phase 6: Remaining Secondary Features

Implement the structure and basic state management for the Search features and the Blocked/Hidden user lists.

### 40. Search (`lib/features/search/`)

* **`SearchViewModel` (`search_viewmodel.dart`):**
    * [ ] Create `SearchViewModel` (`@riverpod`, `AsyncNotifier<SearchState>`).
    * [ ] Define `SearchState` (`@freezed`) to hold state for *both* search types:
        * Name/Username Search: `String userQuery`, `List<User> userSuggestions`, `bool isLoadingNameSearch`, `String? nameSearchError`.
        * Interest Search: `List<String> selectedActivities`, `List<String> selectedKeywords`, `String selectedDistanceFilter` ('local', 'regional', 'global'), `int? minAge`, `int? maxAge`, `String? selectedGender`, `List<User> interestSearchResults`, `bool isLoadingInterestSearch`, `String? interestSearchError`.
    * [ ] Implement `build`: Return initial default `SearchState`.
    * [ ] Implement `searchUsersByName(String query)`: Debounced method. Calls `UserRepository.searchUsers` (passing only query). Updates `userSuggestions`, `isLoadingNameSearch`, `nameSearchError` in state.
    * [ ] Implement methods to update filters: `updateSelectedActivities`, `updateSelectedKeywords`, `updateDistanceFilter`, `updateAgeGenderFilters` (takes results from `FilterPage`). Clear `interestSearchResults` when filters change.
    * [ ] Implement `searchByInterests()`: Sets `isLoadingInterestSearch=true`. Gathers all filters from state. Calls `UserRepository.searchUsers` with all relevant parameters (keywords, activities, distance, age, gender). Updates `interestSearchResults`, `isLoadingInterestSearch`, `interestSearchError`.
* **`SearchPage` (`search_page.dart`):**
    * [X] Implement `SearchPage` (`ConsumerStatefulWidget`).
    * [X] Add necessary `TextEditingController`s (user search, keyword input within `InterestsSelectorWidget`). Initialize/Dispose controllers.
    * [X] Implement `build`: Watch `SearchViewModel`. Build `Scaffold`, `AppBar` ("Search Members"). Build `SingleChildScrollView`.
    * [X] **Name/Username Section:**
        * Build `TextFormField` for user search. `onChanged` triggers debounced `viewModel.searchUsersByName`.
        * Build `ListView` below input to display `state.userSuggestions` (or loading/error for name search). `ListTile` shows name/username, `onTap` navigates to `/profile/:userId`.
    * [X] **Discover by Interests Section:**
        * Build header text.
        * Build Filter display area: `Row` with Filter `IconButton` (navigates to `/filter`, passes current filters, updates VM on return) and `Wrap` of `Chip`s displaying `state.minAge`/`maxAge`/`selectedGender` (with 'x' to call VM filter update methods).
        * Build `SharedActivitiesSelectorWidget` (pass `state.selectedActivities`, callback calls `viewModel.updateSelectedActivities`).
        * Build `InterestsSelectorWidget` (pass `state.selectedKeywords`, callback calls `viewModel.updateKeywords`).
        * Build Distance Filter `Row` with `Radio` buttons bound to `state.selectedDistanceFilter`, calling `viewModel.updateDistanceFilter` on change.
        * Build "Search by Interests" `ElevatedButton` (`onPressed` calls `viewModel.searchByInterests()`, disable/show indicator based on `state.isLoadingInterestSearch`).
    * [X] **Interest Search Results:** Display results *below* the "Search by Interests" button (or navigate to separate screen). Use `ListView` of medium `UserProfileCard`s based on `state.interestSearchResults`. Handle loading/error/empty states for interest search.
* **`FilterPage` (`filter_page.dart`):**
    * [X] Implement `FilterPage` (`StatefulWidget` is fine for local form state). Accept initial filter values as parameters.
    * [X] Build UI: `AppBar` ("Filter", Close button), `RangeSlider` for Age, `RadioListTile`s for Gender ("Both", "Male", "Female"), "Reset" button, "Apply Filters" button.
    * [X] Manage local state (`_minAge`, `_maxAge`, `_selectedGender`) initialized from widget parameters.
    * [X] "Reset" button resets local state to defaults.
    * [X] "Apply Filters" button calls `Navigator.pop(context, {'minAge': _minAge, ...})`, returning selected values.
* **`CitySearchPage` (`city_search_page.dart`):**
    * [X] Implement `CitySearchPage` (`ConsumerStatefulWidget`).
    * [X] Build UI: `AppBar` ("Search City"), `TextFormField` for city input, `ListView` for suggestions, `Slider` or other widget for distance radius, "Show Results" button.
    * [X] Manage local state (`_selectedGeoPoint`, `_selectedCityName`, `_selectedRadius`, `_suggestions`).
    * [X] `onChanged` for input calls `LocationService.getCitySuggestions` via `ref.read`, updates `_suggestions` list via `setState`.
    * [X] Tapping suggestion updates local state.
    * [X] "Show Results" button calls `Navigator.pop(context, {'geoPoint': _selectedGeoPoint, 'radius': _selectedRadius})`.

### 41. Blocked Users (`lib/features/profile/screens/blocked_users_screen.dart`)

* **`BlockedUsersViewModel`:**
    * [ ] Create ViewModel (`@riverpod`, `AsyncNotifier<List<User>>`).
    * [ ] `build`: Calls `UserRepository.getBlockedUsers`.
    * [ ] `unblockUser(userId)`: Calls repo, handles error, calls `ref.invalidateSelf()` on success.
* **`BlockedUsersScreen`:**
    * [ ] Implement UI (`ConsumerWidget`). Watch ViewModel. Handle loading/error/empty states.
    * [ ] Build `Scaffold`, `AppBar` ("Blocked Users").
    * [ ] Build `ListView.builder`: Each item shows user avatar/name, includes "Unblock" button calling `viewModel.unblockUser`. Add confirmation dialog.

### 42. Hidden Users (`lib/features/profile/screens/hidden_users_screen.dart`)

* **`HiddenUsersViewModel`:**
    * [ ] Create ViewModel (`@riverpod`, `AsyncNotifier<List<User>>`).
    * [ ] `build`: Calls `UserRepository.getHiddenUsers`.
    * [ ] `unhideUser(userId)`: Calls repo, handles error, calls `ref.invalidateSelf()` on success.
* **`HiddenUsersScreen`:**
    * [ ] Implement UI (`ConsumerWidget`). Watch ViewModel. Handle loading/error/empty states.
    * [ ] Build `Scaffold`, `AppBar` ("Hidden Users").
    * [ ] Build `ListView.builder`: Each item shows user avatar/name, includes "Unhide" button calling `viewModel.unhideUser`. Add confirmation dialog.


==============================================

# Deferred Tasks
### 1. Push Notification Implementation Tasks (Beyond In-App):

1. **Firebase Project Setup:** Creating and configuring a Firebase project.
2. **Apple Developer Setup:** Enrolling in the Apple Developer Program (if not already), creating App IDs, push notification certificates or keys.
3. **FCM/APNS Configuration:** Linking your Firebase project to your Apple developer account for APNS.
4. **Native Setup (iOS):** Configuring Xcode (`Info.plist`, capabilities, `AppDelegate.swift` or `Objective-C` equivalent) to handle push notifications.
5. **Native Setup (Android):** Configuring Android (`build.gradle`, `AndroidManifest.xml`, potentially `google-services.json`) for FCM.
6. **Flutter Packages:** Adding and configuring `firebase_core` and `firebase_messaging`.
7. **Permission Request:** Implementing the UI and logic to request push notification permission from the user (required on iOS, good practice on Android).
8. **FCM Token Management:** Getting the unique device token for each user, saving it securely (e.g., in your `Users` table or a dedicated table), and handling token refreshes.
9. **Background Handler:** Setting up Flutter background message handlers to process incoming notifications when the app isn't in the foreground.
10. **Backend Trigger:** Creating backend logic (likely a **Supabase Database Function/Trigger** or an **Edge Function**) that listens for new rows inserted into your `public."UserNotifications"` table and then calls the appropriate FCM/APNS APIs to send the actual push notification to the target user's device (using their saved FCM token).

### 2. Convert Wishlist Item to Event Feature:

* [ ] **UI (`WishlistItemDetailPage`):**
	* Add "Convert to Event" button.
	* Implement conditional visibility (show only for the item's creator).
	* Implement `onPressed` handler:
		* Read the current `WishlistItem` data from the ViewModel state.
		* Navigate to `CreateEventScreen`, passing the relevant `WishlistItem` data as arguments.
* [ ] **Routing/Screen Arguments (`CreateEventScreen`):**
	* Modify `CreateEventScreen` constructor to accept optional `WishlistItem? initialWishlistItemData`.
	* Modify `CreateEventScreen`'s `initState` (or corresponding ViewModel `build`/init method): If `initialWishlistItemData` is provided, pre-fill the relevant form fields/controllers (Event Name, Description, Category, Date/Time [handle specific vs general], Place Name, Place Location).
* [ ] **ViewModel (`CreateEventViewModel`):**
	* Modify ViewModel state/initialization logic to accept and use `initialWishlistItemData` for pre-filling form state.
* [ ] **ViewModel/Repository (Post-Event Creation):**
	* Determine mechanism for handling the original wishlist item after the event is successfully created/published (e.g., does `CreateEventScreen` pop with a success flag?).
	* Implement logic (likely in `WishlistItemDetailViewModel` or triggered after popping back from `CreateEventScreen`) to call `WishlistRepository.updateWishlistItem` to change the status of the original `WishlistItem` to 'completed' or a new 'converted' status.
* [ ] **Notifications (Optional Enhancement):**
	* Implement logic (after successful event creation) to fetch users who were interested ('lets_go'/'going') in the original `WishlistItem`.
	* Call `NotificationRepository.createNotification` to inform these users about the newly created Event, potentially linking them to the `EventDetailPage`.


==============================================

**Identify Potential Additions:** Is there anything _else_ fundamentally missing from the PRD or high-level Task List that we should define _before_ starting detailed implementation discussions or coding?

- **Deployment/Platform:** Briefly mentioned, but no detailed strategy. (Likely post-MVP concern).
- **Analytics:** Not discussed. (Post-MVP).
- **Testing Strategy:** Mentioned briefly in checklists/NFRs, but no detailed plan. (Could discuss now or defer).
- **UI Theming/Styling:** Not detailed. (Handled during implementation).
- **State Management (Complex Cases):** While the basics are covered, complex inter-provider communication or specific state persistence needs might arise. (Handle as needed).
- **Security (Deeper Dive):** RLS basics are covered. Deeper security aspects (input sanitization, rate limiting, etc.) are implementation details or post-MVP.

