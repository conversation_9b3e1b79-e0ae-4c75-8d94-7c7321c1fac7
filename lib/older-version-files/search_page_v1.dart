// lib/features/search/screens/search_page_v1.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/features/discover/viewmodels/discover_viewmodel.dart';
import 'package:watermelon_draft/features/profile/screens/profile_screen.dart'; // Import ProfileScreen

class SearchPage extends ConsumerStatefulWidget {
  // Use ConsumerStatefulWidget
  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends ConsumerState<SearchPage> {
  final TextEditingController _userSearchController =
      TextEditingController(); // Controller
  List<User> _userSuggestions = [];

  List<String> _selectedLookingFor = []; // Keep for interest
  List<String> _selectedKeywords = []; // Keep for interest

  // Add methods for search user
  // Future<void> _searchUsers(String query) async {
  //   final result = await ref
  //       .read(discoverViewModelProvider.notifier)
  //       .searchUsers(query); //search user
  //   result.fold((l) {}, (r) {
  //     setState(() {
  //       _userSuggestions = r;
  //     });
  //   });
  // }

  // Additions for ChoiceChip
  final List<String> _distanceOptions = [
    'local',
    'regional',
    'national',
    'global'
  ];
  String? _selectedDistanceFilter =
      'local'; // Default to 'local', and use String?

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Search Members'),
      ),
      body: SingleChildScrollView(
        // Use SingleChildScrollView for the entire body
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // --- Member Search (by Name/Username) ---
              Text('Search Members',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              TextField(
                controller: _userSearchController,
                decoration: InputDecoration(
                  labelText: 'Search by name or username',
                  hintText: 'Enter name or username',
                  prefixIcon: Icon(Icons.search),
                  counterText: "",
                ),
                onChanged: (value) async {
                  // Call ViewModel method to search for users
                  // final result = await ref
                  //     .read(discoverViewModelProvider.notifier)
                  //     .searchUsers(value); //search user
                  // result.fold((l) {
                  //   //Handle error
                  // }, (r) {
                  //   setState(() {
                  //     _userSuggestions = r;
                  //   });
                  // });
                },
              ),

              // --- Autocomplete Suggestions ---
              if (_userSuggestions.isNotEmpty)
                Container(
                  // height: 200, //  set height
                  constraints: BoxConstraints(maxHeight: 200), // Add max height
                  child: ListView.builder(
                    itemCount: _userSuggestions.length,
                    itemBuilder: (context, index) {
                      final user = _userSuggestions[index];
                      return ListTile(
                        title: Text(user.fullName ??
                            user.username ??
                            'Unknown User'), // Display full name
                        subtitle: Text(
                            '@${user.username ?? 'unknown'}'), // Display username
                        onTap: () {
                          // Navigate to user's profile
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  ProfileScreen(userId: user.userId),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),

              SizedBox(height: 24),

              // --- Discover Members Based on Activities and Interests ---
              Text(
                'Discover Members Based on Activities and Interests',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),

              // --- "Looking For" Categories ---
              // ... (Your existing multi-select UI for "Looking For" - bottom sheet, etc.) ...
              GestureDetector(
                // Use GestureDetector to make the whole row tappable
                onTap: () {
                  // _showLookingForSelectionSheet,  Use method to show selection.
                },
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Looking For',
                    hintText: 'Select categories',
                    border: OutlineInputBorder(), // Add a border
                  ),
                  child: _selectedLookingFor.isEmpty
                      ? Text('Select categories')
                      : Wrap(
                          // Display selected categories as chips
                          spacing: 8.0,
                          runSpacing: 4.0,
                          children: _selectedLookingFor.map((categoryId) {
                            // Find category name (you might want a helper function for this)
                            final category =
                                []; //_lookingForCategories.firstWhereOrNull((cat) => cat.categoryId == categoryId);
                            return Chip(
                              label: Text(
                                  ""), // category?.categoryName ?? 'Unknown'
                              onDeleted: () {
                                setState(() {
                                  _selectedLookingFor.remove(categoryId);
                                });
                              },
                            );
                          }).toList(),
                        ),
                ),
              ),

              // --- Interest Keywords ---
              // ... (Your existing UI for entering/selecting interest keywords) ...
              TextField(
                // controller: _keywordController,
                decoration: InputDecoration(
                    labelText: 'Search by Interests',
                    hintText: 'Enter keywords (e.g., travel, cooking)',
                    suffixIcon: Icon(Icons.search),
                    counterText: ""),
                onChanged: (value) {
                  // _filterSuggestions; //Filter suggestion.
                },
                onSubmitted: (value) {
                  // Add the entered keyword (if it's in the suggestions)
                  // if (_suggestedKeywords.contains(value)) {
                  //   _addKeyword(value);
                  // }
                },
              ),

              // --- Display Suggestions ---
              // if (_suggestedKeywords.isNotEmpty)
              //   Container(
              //     height: 200, // Set a fixed height or use Expanded
              //     child: ListView.builder(
              //       itemCount: _suggestedKeywords.length,
              //       itemBuilder: (context, index) {
              //         final keyword = _suggestedKeywords[index];
              //         return ListTile(
              //           title: Text(keyword),
              //           onTap: () => _addKeyword(keyword), // Add on tap
              //         );
              //       },
              //     ),
              //   ),

              // --- Display Selected Keywords (Chips) ---
              Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children: _selectedKeywords
                    .map((keyword) => Chip(
                        label: Text(keyword),
                        deleteIcon: Icon(Icons.close),
                        onDeleted: () {
                          // _removeKeyword(keyword) //Remove keyword
                        }))
                    .toList(),
              ),

              // --- Distance Filter (Chips) ---
              SizedBox(height: 16),
              Text('Distance:', style: TextStyle(fontWeight: FontWeight.bold)),
              Wrap(
                // Use Wrap instead of Row
                spacing: 8.0, // Space between chips
                runSpacing: 4.0, // Space between rows of chips (if they wrap)
                children: _distanceOptions
                    .map((option) => ChoiceChip(
                          label: Text(
                              option.capitalize()), // Capitalize for display
                          selected: _selectedDistanceFilter == option,
                          onSelected: (selected) {
                            setState(() {
                              _selectedDistanceFilter =
                                  selected ? option : null;
                            });
                          },
                        ))
                    .toList(),
              ),

              // const Spacer(flex: 2,), // Pushes buttons to the bottom

              // --- Search Button (Triggers Interest-Based Search) ---
              SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        // Perform interest-based search (using _selectedLookingFor and _selectedKeywords)
                        // and navigate to a results page (or update the UI to show results).
                      },
                      child: Text('Search by Interests'),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 10), // Spacing at bottom
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _userSearchController.dispose();
    super.dispose();
  }
}

//Add extension
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
