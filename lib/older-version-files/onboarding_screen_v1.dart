// // lib/features/onboarding/screens/onboarding_screen.dart
// import 'package:beamer/beamer.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:watermelon_draft/core/ui/widgets/profile_image_picker.dart';
// import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';

// class OnboardingScreen extends ConsumerStatefulWidget {
//   const OnboardingScreen({super.key});

//   @override
//   _OnboardingScreenState createState() => _OnboardingScreenState();
// }

// class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
//   final PageController _pageController =
//       PageController(); // Controller for PageView
//   // You *don't* need local state variables here for the form fields.
//   // The OnboardingViewModel manages ALL the onboarding data.

//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final state = ref
//         .watch(onboardingViewModelProvider); // Get the state from the ViewModel
//     final viewModel = ref.read(onboardingViewModelProvider.notifier);

//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Onboarding'),
//       ),
//       body: PageView(
//         controller: _pageController,
//         physics:
//             NeverScrollableScrollPhysics(), // Prevent swiping between pages
//         children: [
//           _buildWelcomePage(
//               context), // Step 1: Welcome (can be merged to step 2)
//           _buildBasicInfoPage(context,
//               viewModel), // Step 2: Basic Info (Name, Username, DOB, Gender)
//           _buildProfilePicturePage(
//               context, viewModel), // Step 3: Profile Picture
//           _buildLocationPage(
//               context, viewModel), // Step 4: Location (permissions, city input)
//           _buildLookingForPage(
//               context, viewModel), // Step 5: "Looking For" Activities
//           _buildSummaryPage(context, viewModel), // Step 6: Summary
//         ],
//       ),
//     );
//   }

//   // --- Step 1: Welcome Page (Example - can be customized/merged) ---
//   Widget _buildWelcomePage(BuildContext context) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Welcome to Watermelon!', style: TextStyle(fontSize: 24)),
//           SizedBox(height: 20),
//           Text('Connect with others through shared activities.',
//               textAlign: TextAlign.center),
//           SizedBox(height: 40),
//           ElevatedButton(
//             onPressed: () {
//               //
//             },
//             child: Text('Get Started'),
//           ),
//         ],
//       ),
//     );
//   }

//   // --- Step 2: Basic Info Page ---
//   Widget _buildBasicInfoPage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     final _formKey = GlobalKey<FormState>(); // Add form key
//     final _fullNameController = TextEditingController(); // Add controllers
//     final _usernameController = TextEditingController();
//     DateTime? _selectedDate; // Add a variable to store the selected date

//     return Form(
//       key: _formKey,
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             TextFormField(
//               controller: _fullNameController, // Use controller here.
//               decoration: InputDecoration(labelText: 'Full Name'),
//               validator: (value) {
//                 if (value == null || value.isEmpty) {
//                   return 'Please enter your full name';
//                 }
//                 return null;
//               },
//               onChanged: (value) {
//                 viewModel.updateFullName(value); // Update ViewModel
//               },
//             ),
//             TextFormField(
//               controller: _usernameController,
//               decoration: InputDecoration(labelText: 'Username'),
//               validator: (value) {
//                 if (value == null || value.isEmpty) {
//                   return 'Please enter a username';
//                 }
//                 // Add more username validation as needed
//                 return null;
//               },
//               onChanged: (value) {
//                 viewModel.updateUsername(value); // Update ViewModel
//               },
//             ),
//             // Date picker
//             GestureDetector(
//               // Use GestureDetector for tap
//               onTap: () async {
//                 final DateTime? pickedDate = await showDatePicker(
//                     context: context,
//                     initialDate: DateTime.now(), // Set start date
//                     firstDate: DateTime(1900), // Set start range.
//                     lastDate: DateTime.now()); // Set end range.
//                 if (pickedDate != null && pickedDate != _selectedDate) {
//                   // Check if date is picked
//                   setState(() {
//                     _selectedDate = pickedDate; // Set state, update date.
//                   });
//                   viewModel.updateBirthdate(pickedDate); // Update ViewModel
//                 }
//               },
//               child: InputDecorator(
//                 decoration: InputDecoration(
//                   labelText: 'Date of Birth',
//                   border: OutlineInputBorder(), // Add a border
//                 ),
//                 child: Text(
//                   _selectedDate == null
//                       ? 'Select Date'
//                       : '${_selectedDate.toLocal()}'
//                           .split(' ')[0], // Display selected date
//                 ),
//               ),
//             ),
//             // Gender selection (replace with your actual implementation)
//             // You can use Radio buttons, Dropdown, etc.
//             DropdownButtonFormField<String>(
//               value:
//                   ref.watch(onboardingViewModelProvider).gender, // Use Riverpod
//               items: ['Male', 'Female', 'Other', 'Prefer not to say']
//                   .map((gender) => DropdownMenuItem(
//                         value: gender,
//                         child: Text(gender),
//                       ))
//                   .toList(),
//               onChanged: (value) {
//                 if (value != null) {
//                   // Set state.
//                   viewModel.updateGender(value); // Update ViewModel
//                 }
//               },
//               decoration: InputDecoration(labelText: 'Gender'),
//               validator: (value) {
//                 // Validation
//                 if (value == null || value.isEmpty) {
//                   return 'Please select your gender';
//                 }
//                 return null;
//               },
//             ),
//             SizedBox(height: 20),
//             ElevatedButton(
//               onPressed: () {
//                 if (_formKey.currentState!.validate()) {
//                   // Validate form
//                   viewModel.nextPage(); // Next
//                   _pageController.nextPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 }
//               },
//               child: Text('Next'),
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 viewModel.previousPage(); // Previous
//                 _pageController.previousPage(
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//               child: Text('Back'),
//             )
//           ],
//         ),
//       ),
//     );
//   }

//   // --- Step 3: Profile Picture Page ---
//   Widget _buildProfilePicturePage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Add a Profile Picture', style: TextStyle(fontSize: 20)),
//           SizedBox(height: 16),
//           // Use your ProfileImagePicker widget here!
//           ProfileImagePicker(
//             onImageSelected: (image) {
//               viewModel.updateProfileImage(image);
//             },
//             updateButtonText: 'Next',
//             cancelButtonText: 'Back',
//             onUpdate: () {
//               viewModel.nextPage();
//               if (viewModel.state.value!.currentPage < 5) {
//                 _pageController.nextPage(
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut); //
//               }
//             },
//             onBack: () {
//               viewModel.previousPage();
//               if (viewModel.state.value!.currentPage >= 0) {
//                 // Make sure not negative
//                 _pageController.previousPage(
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               }
//             },
//           ),
//           TextButton(
//               onPressed: () {
//                 //skip
//               },
//               child: Text("Skip"))
//         ],
//       ),
//     );
//   }

//   // --- Step 4: Location Page ---
//   Widget _buildLocationPage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Set Your Location', style: TextStyle(fontSize: 20)),
//           SizedBox(height: 16),
//           ElevatedButton(
//             onPressed: () async {
//               // Request location permission
//               final hasPermission = await ref
//                   .read(locationServiceProvider)
//                   .requestPermission(); // Request permission

//               if (hasPermission) {
//                 // Get the current location
//                 final location = await ref
//                     .read(locationServiceProvider)
//                     .getCurrentLocation();

//                 if (location != null) {
//                   // Update the ViewModel with the location
//                   viewModel
//                       .updateLocationFromPoint(location); // Update view model
//                   //
//                 } else {
//                   // Handle the case where location is null (e.g., show an error)
//                   if (mounted) {
//                     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                         content: Text(
//                             "No location get, please try again, or enter city manually")));
//                   }
//                 }
//               } else {
//                 if (mounted) {
//                   ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                       content:
//                           Text("Location is denied"))); // Show error message
//                 }
//               }
//             },
//             child: Text('Use Current Location'),
//           ),
//           SizedBox(height: 8),
//           Text('OR', style: TextStyle(fontWeight: FontWeight.bold)),
//           SizedBox(height: 8),
//           // TextField for manual city entry (you can use your CitySearchPage logic here)
//           TextFormField(
//             decoration: InputDecoration(
//               labelText: 'Enter City Manually',
//               border: OutlineInputBorder(),
//             ),
//             onChanged: (value) {
//               // Update the ViewModel with the entered city
//               // You'll likely want to debounce this input
//               viewModel.updateCountryFromCity(value); // Update city
//             },
//           ),

//           SizedBox(height: 20),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.nextPage(); // Next page
//               _pageController.nextPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut);
//             },
//             child: Text('Next'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.previousPage(); // Previous page
//               _pageController.previousPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut);
//             },
//             child: Text('Back'),
//           )
//         ],
//       ),
//     );
//   }

//   // --- Step 5: "Looking For" Activities Page ---
//   Widget _buildLookingForPage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Select Shared Activities', style: TextStyle(fontSize: 20)),
//           SizedBox(height: 16),
//           // You would integrate your "Looking For" selection UI here.
//           // This is likely a combination of a text field with autocomplete
//           // and a button to open the bottom sheet.

//           // Example (replace with your actual UI):
//           ElevatedButton(
//             onPressed: () async {
//               final List<String>? selectedActivities =
//                   await _showLookingForSelectionSheet(context,
//                       ref.read(onboardingViewModelProvider).lookingFor ?? []);
//               if (selectedActivities != null) {
//                 viewModel
//                     .updateLookingFor(selectedActivities); // Update ViewModel
//               }
//             },
//             child: Text('Select Activities'),
//           ),
//           SizedBox(height: 24),
//           Text(
//             // Add some spacing
//             'You can change it any time from Account page',
//             style: TextStyle(color: Colors.grey, fontSize: 12),
//             textAlign: TextAlign.center, // center
//           ),

//           SizedBox(height: 20),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.nextPage();
//               _pageController.nextPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut); // Animate
//             },
//             child: Text('Next'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.previousPage();
//               _pageController.previousPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut); // Animate
//             },
//             child: Text('Back'),
//           ),
//         ],
//       ),
//     );
//   }

//   // --- Step 6: Summary Page ---
//   Widget _buildSummaryPage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     final state =
//         ref.watch(onboardingViewModelProvider); // For read only purpose
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Summary', style: TextStyle(fontSize: 20)),
//           SizedBox(height: 16),
//           // Display a summary of the collected information.
//           // You can use ListTile widgets or a custom layout.
//           // Example:
//           ListTile(
//             title: Text('Full Name'),
//             subtitle: Text(state.fullName ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.previousPage(
//                   // Back to page index 1
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut,
//                 );
//               },
//             ),
//           ),
//           ListTile(
//             title: Text('Username'),
//             subtitle: Text(state.username ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.previousPage(
//                   // Back to page index 1
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut,
//                 );
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Date of Birth"),
//             subtitle: Text(state.birthdate?.toIso8601String() ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 1
//                     1,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Gender"),
//             subtitle: Text(state.gender ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 1
//                     1,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("City"),
//             subtitle: Text(state.city ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 3
//                     3,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Country"),
//             subtitle: Text(state.country ?? 'N/A'),
//           ),
//           ListTile(
//             title: Text("Looking For"),
//             subtitle: Text(state.lookingFor?.join(", ") ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 4
//                     4,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           // ... display other fields ...
//           ListTile(
//             title: Text("My Interests"),
//             subtitle: Text(state.myInterests?.join(", ") ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 // Navigate to a page where user set interest
//               },
//             ),
//           ),
//           SizedBox(height: 20),
//           ElevatedButton(
//             onPressed: () async {
//               // Complete onboarding (save to Supabase, set onboarding_complete flag)
//               final result = await viewModel.completeOnboarding();
//               result.fold((l) {
//                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                   content: Text(l.message),
//                   backgroundColor: Colors.red,
//                 ));
//               }, (r) {
//                 // Navigate to home
//                 if (mounted) {
//                   context.beamToNamed('/home'); // Navigate using Beamer
//                 }
//               });
//             },
//             child: Text('Complete Onboarding'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.previousPage();
//               _pageController.previousPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut); // Animate
//             },
//             child: Text('Back'),
//           ),
//         ],
//       ),
//     );
//   }
// }
