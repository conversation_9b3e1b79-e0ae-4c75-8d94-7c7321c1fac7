// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:watermelon_draft/core/errors.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:watermelon_draft/core/services/auth_repository.dart';
// import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
// import 'package:riverpod_annotation/riverpod_annotation.dart'; //

// // part 'signup_viewmodel.g.dart';

// @riverpod
// class SignUpViewModel extends _$SignUpViewModel {
//   late final AuthRepository _authRepository;

//   @override
//   FutureOr<supabase.User?> build() {
//     // Returns FutureOr<User?>
//     _authRepository =
//         ref.watch(authRepositoryProvider); // Use ref.read inside build
//     return null; // Initial state: null (no user yet)
//   }

//   Future<Either<Failure, supabase.User?>> signUp({
//     required String email,
//     required String password,
//     required String username,
//     required String fullName,
//   }) async {
//     // No need to set loading state here

//     final result = await _authRepository.signUp(
//       email: email,
//       password: password,
//       username: username,
//       fullName: fullName,
//     );

//     return result.fold(
//       (failure) {
//         // No need to update state here; we're handling errors in ref.listen
//         state = AsyncValue.error(failure, StackTrace.current);
//         return left(failure); // Just return
//       },
//       (user) {
//         state = AsyncValue.data(user); // Set state in Riverpod.
//         return right(user);
//       },
//     );
//   }
// }
