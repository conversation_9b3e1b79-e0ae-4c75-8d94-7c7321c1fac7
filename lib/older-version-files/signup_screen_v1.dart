// lib/features/auth/screens/signup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/auth/viewmodels/signup_viewmodel.dart'; // Import ViewModel
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/errors.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _usernameController.dispose();
    _fullNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use ref.listen for navigation and SnackBars (side effects)
    ref.listen(signUpViewModelProvider, (previous, next) {
      if (next is AsyncData) {
        //If success
        if (mounted) {
          context.beamToNamed("/home"); // Go to home page
        }
      } else if (next is AsyncError) {
        // If error
        final error = next.error;
        if (error is Failure && mounted) {
          //If it's failure
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Error on sign up ${error.message}"),
              backgroundColor: Colors.red,
            ),
          ); // Show error message
        }
      }
    });

    final state = ref
        .watch(signUpViewModelProvider); // Use generated provider and .notifier
    final viewModel = ref.read(signUpViewModelProvider.notifier);

    return Scaffold(
      appBar: AppBar(title: const Text('Sign Up')),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _fullNameController,
                decoration: const InputDecoration(
                    labelText: 'Full Name',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                    counterText: ""),
                maxLength: 70, //
                keyboardType: TextInputType.name,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your full name';
                  }
                  return null; // Return null if the input is valid
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(
                    labelText: 'Username',
                    prefixIcon: Icon(Icons.alternate_email), // Suitable icon
                    border: OutlineInputBorder(),
                    counterText: ""),
                maxLength: 20, // Set a reasonable max length
                keyboardType: TextInputType.text, // Allows letters, numbers
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a username';
                  }
                  if (value.length < 3) {
                    return 'Username must be at least 3 characters long';
                  }
                  if (value.contains(' ')) {
                    return 'Username cannot contain spaces';
                  }
                  //Ideally call isUsernameAvailable()
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    // Basic email validation
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'Password',
                  prefixIcon: Icon(Icons.lock),
                  border: OutlineInputBorder(),
                  suffixIcon: IconButton(
                    // Show/hide password
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                obscureText: _obscurePassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters long';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: state.isLoading
                    ? null // Disable button while loading
                    : () async {
                        if (_formKey.currentState!.validate()) {
                          // If the form is valid, try to sign up
                          await viewModel.signUp(
                            email: _emailController.text,
                            password: _passwordController.text,
                            // username: _usernameController.text,
                            // fullName: _fullNameController.text,
                          );
                        }
                      },
                child: state.isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Sign Up'), // Show loading indicator
              ),
              TextButton(
                onPressed: () {
                  context.beamToNamed('/login');
                },
                child: const Text('Already have an account? Log in'),
              )
            ],
          ),
        ),
      ),
    );
  }
}
