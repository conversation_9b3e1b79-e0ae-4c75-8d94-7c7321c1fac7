// // lib/features/profile/widgets/interests_selector_widget.dart
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:reorderables/reorderables.dart'; // Import the package
// import 'package:watermelon_draft/core/models/keyword.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:fuzzywuzzy/fuzzywuzzy.dart' as fuzz;
// import 'package:collection/collection.dart';

// class InterestsSelectorWidget extends ConsumerStatefulWidget {
//   final List<String> initialKeywords;
//   final Function(List<String> updatedKeywords) onKeywordsChanged;
//   final int maxTotalKeywords;
//   final int maxCustomKeywords;

//   const InterestsSelectorWidget({
//     super.key,
//     required this.initialKeywords,
//     required this.onKeywordsChanged,
//     this.maxTotalKeywords = 5,
//     this.maxCustomKeywords = 2,
//   });

//   @override
//   ConsumerState<InterestsSelectorWidget> createState() =>
//       _InterestsSelectorWidgetState();
// }

// class _InterestsSelectorWidgetState
//     extends ConsumerState<InterestsSelectorWidget> {
//   final TextEditingController _keywordController = TextEditingController();
//   final TextEditingController _customKeywordController =
//       TextEditingController();
//   List<Keyword> _allKeywords = [];
//   List<Keyword> _suggestedKeywords = [];
//   List<String> _selectedPredefinedKeywords = [];
//   List<String> _selectedCustomKeywords = [];
//   bool _isLoadingKeywords = true;

//   @override
//   void initState() {
//     super.initState();
//     _fetchKeywords();
//   }

//   Future<void> _fetchKeywords() async {
//     final result = await ref.read(keywordsRepositoryProvider).getAllKeywords();
//     result.fold(
//       (l) {
//         if (mounted) {
//           ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//               content: Text("Error fetching interests: ${l.message}")));
//           setState(() => _isLoadingKeywords = false);
//         }
//       },
//       (keywords) {
//         if (mounted) {
//           setState(() {
//             _allKeywords = keywords;
//             // Initialize selected lists based on initialKeywords
//             _selectedPredefinedKeywords = widget.initialKeywords
//                 .where((kw) => _allKeywords.any((k) => k.keywordText == kw))
//                 .toList();
//             _selectedCustomKeywords = widget.initialKeywords
//                 .where((kw) => !_allKeywords.any((k) => k.keywordText == kw))
//                 .toList();
//             _isLoadingKeywords = false;
//           });
//         }
//       },
//     );
//   }

//   @override
//   void dispose() {
//     _keywordController.dispose();
//     _customKeywordController.dispose();
//     super.dispose();
//   }

//   void _filterSuggestions(String input) {
//     if (input.isEmpty) {
//       if (mounted) setState(() => _suggestedKeywords = []);
//       return;
//     }
//     final lowerCaseInput = input.toLowerCase();
//     final currentSelection = [
//       ..._selectedPredefinedKeywords,
//       ..._selectedCustomKeywords
//     ];
//     if (mounted) {
//       setState(() {
//         _suggestedKeywords = _allKeywords.where((keyword) {
//           return keyword.keywordText.toLowerCase().contains(lowerCaseInput) &&
//               !currentSelection
//                   .contains(keyword.keywordText); // Exclude already selected
//         }).toList();
//       });
//     }
//   }

//   Keyword? _findKeyword(String keywordText) {
//     return _allKeywords.firstWhereOrNull((k) => k.keywordText == keywordText);
//   }

//   void _addKeyword(Keyword keyword) {
//     // ... (Same add logic, call _notifyParent) ...
//     if ((_selectedPredefinedKeywords.length + _selectedCustomKeywords.length) <
//             widget.maxTotalKeywords &&
//         !_selectedPredefinedKeywords.contains(keyword.keywordText) &&
//         !_selectedCustomKeywords.contains(keyword.keywordText)) {
//       // Also check custom list for duplicates before adding predefined
//       if (mounted) {
//         setState(() {
//           _selectedPredefinedKeywords.add(keyword.keywordText);
//           _keywordController.clear();
//           _suggestedKeywords = [];
//         });
//         _notifyParent(); // Notify parent widget
//       }
//     } else {
//       _showMaxKeywordError();
//     }
//   }

//   void _removePredefinedKeyword(String keywordText) {
//     // ... (Same remove logic, call _notifyParent) ...
//     if (mounted) {
//       setState(() {
//         _selectedPredefinedKeywords.remove(keywordText);
//       });
//       _notifyParent(); // Notify parent widget
//     }
//   }

//   void _addCustomKeyword(String keyword) async {
//     // ... (Same custom add logic, call _notifyParent) ...
//     final normalizedKeyword = keyword.trim().toLowerCase();
//     final totalSelected =
//         _selectedPredefinedKeywords.length + _selectedCustomKeywords.length;

//     if (normalizedKeyword.isEmpty) return;

//     if (totalSelected < widget.maxTotalKeywords &&
//         _selectedCustomKeywords.length < widget.maxCustomKeywords &&
//         !_selectedPredefinedKeywords.contains(normalizedKeyword) &&
//         !_selectedCustomKeywords.contains(normalizedKeyword)) {
//       // --- Fuzzy Match Check ---
//       String? bestMatch;
//       int bestRatio = 0;
//       for (final existingKeyword in _allKeywords) {
//         final ratio = fuzz.ratio(
//             normalizedKeyword, existingKeyword.keywordText.toLowerCase());
//         if (ratio > bestRatio) {
//           bestRatio = ratio;
//           bestMatch = existingKeyword.keywordText;
//         }
//       }

//       bool useSuggestion = false;
//       if (bestRatio > 85 && bestMatch != null) {
//         // Suggestion threshold
//         if (!mounted) return; // Check if mounted
//         final confirmed = await showDialog<bool>(
//           context: context,
//           builder: (context) => AlertDialog(
//             title: Text('Similar Interest Found'),
//             content: Text(
//                 'Did you mean "$bestMatch"? Using existing interests helps with matching.'),
//             actions: [
//               TextButton(
//                   onPressed: () => Navigator.of(context).pop(false),
//                   child: Text('No, use "$normalizedKeyword"')),
//               TextButton(
//                   onPressed: () => Navigator.of(context).pop(true),
//                   child: Text('Yes, use "$bestMatch"')),
//             ],
//           ),
//         );
//         useSuggestion = confirmed ?? false;
//       }

//       if (useSuggestion && bestMatch != null) {
//         final existingKeywordObj = _findKeyword(bestMatch);
//         if (existingKeywordObj != null) {
//           _addKeyword(existingKeywordObj); // Add the suggested keyword
//         }
//       } else {
//         // Add as custom keyword
//         if (mounted) {
//           setState(() {
//             _selectedCustomKeywords.add(normalizedKeyword);
//             _customKeywordController.clear();
//           });
//           _notifyParent(); // Notify parent widget
//           // Optionally, add to FrequentlyUsedKeywords table here or later
//           // await ref.read(keywordsRepositoryProvider).addFrequentlyUsedKeyword(normalizedKeyword);
//         }
//       }
//     } else if (totalSelected >= widget.maxTotalKeywords) {
//       _showMaxKeywordError();
//     } else if (_selectedCustomKeywords.length >= widget.maxCustomKeywords) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//             content: Text(
//                 "Maximum ${widget.maxCustomKeywords} custom keywords allowed.")));
//       }
//     } else if (_selectedPredefinedKeywords.contains(normalizedKeyword) ||
//         _selectedCustomKeywords.contains(normalizedKeyword)) {
//       if (mounted) {
//         ScaffoldMessenger.of(context)
//             .showSnackBar(SnackBar(content: Text("Interest already added.")));
//       }
//     }
//   }

//   void _removeCustomKeyword(String keyword) {
//     // ... (Same remove logic, call _notifyParent) ...
//     if (mounted) {
//       setState(() {
//         _selectedCustomKeywords.remove(keyword);
//       });
//       _notifyParent(); // Notify parent widget
//     }
//   }

//   void _showMaxKeywordError() {
//     if (mounted) {
//       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           content:
//               Text("Maximum ${widget.maxTotalKeywords} interests allowed.")));
//     }
//   }

//   // Reorder handler
//   void _onReorder(int oldIndex, int newIndex) {
//     if (mounted) {
//       setState(() {
//         final List<String> combinedList = [
//           ..._selectedPredefinedKeywords,
//           ..._selectedCustomKeywords
//         ];

//         // ReorderableWrap provides indices within the combined list.
//         final String item = combinedList.removeAt(oldIndex);
//         combinedList.insert(newIndex, item);

//         // Separate them back - IMPORTANT to maintain distinction
//         _selectedPredefinedKeywords = combinedList
//             .where((kw) => _allKeywords.any((k) => k.keywordText == kw))
//             .toList();
//         _selectedCustomKeywords = combinedList
//             .where((kw) => !_allKeywords.any((k) => k.keywordText == kw))
//             .toList();
//       });
//       _notifyParent(); // Notify parent widget with the new order
//     }
//   }

//   // Call the callback function to notify the parent
//   void _notifyParent() {
//     widget.onKeywordsChanged([
//       ..._selectedPredefinedKeywords,
//       ..._selectedCustomKeywords,
//     ]);
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoadingKeywords) {
//       return Center(child: CircularProgressIndicator());
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select up to ${widget.maxTotalKeywords} interests (max ${widget.maxCustomKeywords} custom). Drag to reorder.', // Added "Drag to reorder"
//           style: TextStyle(fontSize: 12, color: Colors.grey),
//         ),
//         SizedBox(height: 16),

//         // --- Selected Keywords (ReorderableWrap) ---
//         ReorderableWrap(
//           // Use ReorderableWrap
//           spacing: 8.0,
//           runSpacing: 4.0,
//           needsLongPressDraggable: false, // Optional: allow immediate drag
//           onReorder: _onReorder, // Pass the handler
//           children: [
//             ..._selectedPredefinedKeywords.map((keywordText) {
//               return Chip(
//                 key: ValueKey(
//                     'predefined_$keywordText'), // Unique key is crucial
//                 label: Text(keywordText),
//                 deleteIcon: Icon(Icons.close),
//                 onDeleted: () => _removePredefinedKeyword(keywordText),
//               );
//             }),
//             ..._selectedCustomKeywords.map((keyword) => Chip(
//                   key: ValueKey('custom_$keyword'), // Unique key is crucial
//                   label: Text(keyword),
//                   deleteIcon: Icon(Icons.close),
//                   onDeleted: () => _removeCustomKeyword(keyword),
//                 )),
//           ],
//         ),
//         SizedBox(height: 16),

//         // --- Predefined Keywords (Autocomplete) ---
//         TextFormField(
//           controller: _keywordController,
//           decoration: InputDecoration(
//             labelText: 'Select Interests',
//             hintText: 'Start typing to see suggestions...',
//             prefixIcon: Icon(Icons.search),
//           ),
//           onChanged: _filterSuggestions,
//           onFieldSubmitted: (value) {
//             final suggested = _suggestedKeywords
//                 .firstWhereOrNull((k) => k.keywordText == value);
//             if (suggested != null) {
//               _addKeyword(suggested);
//             }
//           },
//         ),
//         // --- Autocomplete Suggestions ---
//         if (_suggestedKeywords.isNotEmpty)
//           Container(
//             constraints: BoxConstraints(maxHeight: 150),
//             child: ListView.builder(
//               shrinkWrap: true,
//               itemCount: _suggestedKeywords.length,
//               itemBuilder: (context, index) {
//                 final keyword = _suggestedKeywords[index];
//                 return ListTile(
//                   title: Text(keyword.keywordText),
//                   onTap: () => _addKeyword(keyword),
//                 );
//               },
//             ),
//           ),

//         // --- Custom Keywords ---
//         SizedBox(height: 16),
//         Row(
//           children: [
//             Expanded(
//               child: TextFormField(
//                 controller: _customKeywordController,
//                 decoration: InputDecoration(
//                   labelText: 'Add Custom Interest',
//                   hintText: 'Enter custom interest',
//                 ),
//                 maxLength: 25,
//               ),
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 _addCustomKeyword(_customKeywordController.text);
//               },
//               child: Text('Create'),
//             ),
//           ],
//         ),
//         SizedBox(height: 8),
//         Text(
//           "Can't find an interest? Add your own (max 2).",
//           style: TextStyle(fontSize: 12, color: Colors.grey),
//         ),
//       ],
//     );
//   }
// }
