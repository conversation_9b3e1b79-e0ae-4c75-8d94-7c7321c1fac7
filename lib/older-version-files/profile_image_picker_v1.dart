// // lib/core/ui/widgets/profile_image_picker.dart

// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'dart:io';
// import 'package:image_cropper/image_cropper.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'dart:math';

// class ProfileImagePicker extends ConsumerStatefulWidget {
//   final String?
//       initialImageUrl; // URL of the current profile picture (can be null)
//   final Function(XFile?) onImageSelected; // Callback when an image is selected
//   final VoidCallback? onUpdate; // Optional callback for update
//   final VoidCallback? onBack;
//   final String updateButtonText; // to set button text
//   final String cancelButtonText;

//   ProfileImagePicker({
//     this.initialImageUrl,
//     required this.onImageSelected,
//     this.onUpdate,
//     this.onBack,
//     this.updateButtonText = 'Update', // Default to "Update"
//     this.cancelButtonText = 'Cancel', // Default to "Cancel"
//   });

//   @override
//   _ProfileImagePickerState createState() => _ProfileImagePickerState();
// }

// class _ProfileImagePickerState extends ConsumerState<ProfileImagePicker> {
//   final _picker = ImagePicker();
//   XFile? _selectedImage; // Keep track of locally selected image
//   String? _selectedDefaultAvatar;

//   // Helper function to generate avatar
//   Widget generateAvatar(String name, {double radius = 30.0}) {
//     final initials = _getInitials(name);
//     final randomColor = _getRandomColor(); // You'll implement this

//     return CircleAvatar(
//       radius: radius, // Use the provided radius
//       backgroundColor: randomColor,
//       child: Text(
//         initials,
//         style: TextStyle(
//             color: Colors.white, fontSize: radius * 0.8), // Adjust font size
//       ),
//     );
//   }

//   String _getInitials(String name) {
//     if (name.isEmpty) return '?';
//     final parts = name.trim().split(' ');
//     if (parts.length == 1) {
//       return parts[0][0].toUpperCase();
//     } else {
//       // return '${parts[0][0]}${parts[1][0]}'.toUpperCase(); //First and Last name
//       return parts[0][0].toUpperCase(); // Only use first name.
//     }
//   }

//   Color _getRandomColor() {
//     // Generate a random color (you might want a predefined set of colors)
//     final List<Color> colors = [
//       Colors.red,
//       Colors.blue,
//       Colors.green,
//       Colors.orange,
//       Colors.purple,
//       Colors.teal,
//       Colors.pink
//     ];

//     return colors[Random().nextInt(colors.length)]; // Use nextInt
//   }

//   Future<void> _pickImage(ImageSource source) async {
//     final XFile? image = await _picker.pickImage(source: source);
//     if (image != null) {
//       // --- Crop the image ---
//       final croppedFile = await ImageCropper().cropImage(
//         sourcePath: image.path,
//         aspectRatio: CropAspectRatio(
//             ratioX: 1, ratioY: 1), // Force a square aspect ratio
//         uiSettings: [
//           AndroidUiSettings(
//               toolbarTitle: 'Crop Image',
//               toolbarColor: Colors.deepOrange,
//               toolbarWidgetColor: Colors.white,
//               initAspectRatio: CropAspectRatioPreset.original,
//               lockAspectRatio: true), // Lock to square
//           IOSUiSettings(title: 'Crop Image', aspectRatioLockEnabled: true)
//         ],
//         compressQuality: 80,
//         compressFormat: ImageCompressFormat.jpg,
//       );

//       if (croppedFile != null) {
//         setState(() {
//           _selectedImage =
//               XFile(croppedFile.path); // Update with the CROPPED image
//           _selectedDefaultAvatar = null; // Clear default avatar selection
//         });
//       }
//     }
//   }

//   Future<void> _showDefaultAvatarSelection(
//       BuildContext context, WidgetRef ref, StateSetter setSheetState) async {
//     final selectedAvatar = await showDialog<String>(
//       context: context,
//       builder: (context) => SimpleDialog(
//         title: Text('Choose Default Avatar'),
//         children: [
//           // Replace these with your actual default avatar assets
//           ListTile(
//             leading: CircleAvatar(
//                 backgroundImage:
//                     AssetImage('assets/images/default_avatar_1.png')),
//             title: Text('Avatar 1'),
//             onTap: () =>
//                 Navigator.pop(context, 'assets/images/default_avatar_1.png'),
//           ),
//           ListTile(
//             leading: CircleAvatar(
//                 backgroundImage:
//                     AssetImage('assets/images/default_avatar_2.png')),
//             title: Text('Avatar 2'),
//             onTap: () =>
//                 Navigator.pop(context, 'assets/images/default_avatar_2.png'),
//           ),
//           // ... more options ...
//         ],
//       ),
//     );

//     if (selectedAvatar != null) {
//       setSheetState(() {
//         //Important, update current sheet state.
//         _selectedImage = null; // Clear any uploaded image
//         _selectedDefaultAvatar = selectedAvatar; // Store the selected default
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Wrap(
//       children: [
//         // --- Preview Image ---
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Container(
//             width:
//                 MediaQuery.of(context).size.width - 32, // Full width - padding
//             height: 200, // Adjust height as needed
//             decoration: BoxDecoration(
//               border: Border.all(color: Colors.grey[300]!),
//               borderRadius: BorderRadius.circular(8.0),
//             ),
//             child: _selectedImage != null
//                 ? Image.file(File(_selectedImage!.path),
//                     fit: BoxFit.cover) // Fit the image
//                 : (widget.initialImageUrl != null
//                     ? Image.network(widget.initialImageUrl!, fit: BoxFit.cover)
//                     : Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Icon(Icons.person, size: 100, color: Colors.grey),
//                           Text("No Image selected")
//                         ],
//                       ) // Show default avatar
//                 ),
//           ),
//         ),

//         // --- Upload Photo ---
//         ListTile(
//           leading: Icon(Icons.photo_library),
//           title: Text('Choose from Gallery'),
//           onTap: () {
//             _pickImage(ImageSource.gallery); //Pick
//           },
//         ),
//         ListTile(
//           leading: Icon(Icons.camera_alt),
//           title: Text('Take Photo'),
//           onTap: () {
//             _pickImage(ImageSource.camera); // Pick
//           },
//         ),

//         // --- Default Avatars ---
//         ListTile(
//           leading: Icon(Icons.person),
//           title: Text('Choose Default Avatar'),
//           onTap: () {
//             setState(() {
//               // Use setSheetState
//               _selectedImage = null;
//             });
//             _showDefaultAvatarSelection(
//                 context, ref, setState); // Pass setSheetState and ref
//           },
//         ),
//         // --- Update and Cancel Buttons ---
//         Row(
//           mainAxisAlignment: MainAxisAlignment.end,
//           children: [
//             TextButton(
//               onPressed: () {
//                 widget.onCancel?.call(); // Use onCancel.
//                 Navigator.pop(context); // Dismiss without saving changes
//               },
//               child: Text(widget.cancelButtonText), // Use
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 widget.onUpdate?.call();
//                 widget.onImageSelected(
//                     _selectedImage); // Pass back the selected image (can be null)
//                 Navigator.pop(context);
//               },
//               child: Text(widget.updateButtonText), // Use
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
