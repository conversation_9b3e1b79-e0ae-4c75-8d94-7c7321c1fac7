// // lib/features/discover/viewmodels/discover_viewmodel.dart

// import 'dart:async';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:watermelon_draft/core/errors.dart';
// import 'package:watermelon_draft/core/models/user.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:watermelon_draft/core/services/location_service.dart';
// import 'package:watermelon_draft/core/services/user_repository.dart';
// import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
// import 'package:watermelon_draft/features/discover/state/discover_state.dart';

// part 'discover_viewmodel.g.dart';

// @riverpod
// class DiscoverViewModel extends _$DiscoverViewModel {
//   late final LocationService _locationService;
//   late final UserRepository _userRepository;
//   Timer? _mapMoveDebounce; // Timer for debouncing map movements

//   @override
//   FutureOr<DiscoverState> build() async {
//     _locationService = ref.watch(locationServiceProvider);
//     _userRepository = ref.watch(userRepositoryProvider);

//     ref.onDispose(() {
//       // Dispose timer
//       _mapMoveDebounce?.cancel();
//     });

//     // Attempt to get initial location asynchronously
//     final initialLocationResult = await _locationService.getCurrentLocation();

//     // Handle the result of getting initial location
//     return initialLocationResult.fold((failure) {
//       print("Failed to get initial location: $failure");
//       // Re-throw the failure to put the provider in AsyncError state
//       throw failure;
//     }, (locationPoint) {
//       print("Build Success - Initial Location: $locationPoint");
//       // If location found, trigger first user search in background
//       if (locationPoint != null) {
//         // Use Future.microtask to ensure this runs after build completes
//         Future.microtask(
//             () => _fetchUsersForArea(center: locationPoint, radius: 50000));
//       }
//       // Return the initial successful state
//       return DiscoverState(
//         userInitialLocation: locationPoint,
//         currentMapCenter:
//             locationPoint, // Center map initially if location exists
//         // Keep other fields at default
//       );
//     });
//   }

//   // --- Methods for UI Interaction ---

//   // Called when the map view stops moving (with debouncing)
//   void mapPositionChanged(GeoPoint center, double zoom) {
//     // Basic calculation, refine later based on screen size/zoom
//     final radius = _calculateRadiusFromZoom(zoom);

//     // Update state immediately with new center/zoom for responsiveness
//     state = AsyncValue.data(state.value!.copyWith(
//         currentMapCenter: center,
//         currentZoom: zoom,
//         currentSearchRadius: radius // Store calculated radius
//         ));

//     _mapMoveDebounce?.cancel();
//     _mapMoveDebounce = Timer(const Duration(milliseconds: 750), () {
//       // Debounce search
//       _fetchUsersForArea(center: center, radius: radius);
//     });
//   }

//   // Called when a user marker is tapped
//   void selectUserMarker(User user) {
//     state = AsyncValue.data(state.value!.copyWith(
//         selectedUser: user,
//         isBottomSheetExpanded: true // Automatically expand sheet on selection
//         ));
//   }

//   // Called to dismiss the bottom sheet or deselect user
//   void deselectUser() {
//     state = AsyncValue.data(state.value!
//         .copyWith(selectedUser: null, isBottomSheetExpanded: false));
//   }

//   // Called to manually expand/collapse bottom sheet (e.g., drag handle)
//   void setBottomSheetExpanded(bool isExpanded) {
//     state = AsyncValue.data(
//         state.value!.copyWith(isBottomSheetExpanded: isExpanded));
//   }

//   // Called after CitySearchPage returns a result
//   Future<void> centerMapOnLocation(GeoPoint location) async {
//     // Update state to center map
//     state = AsyncValue.data(state.value!.copyWith(
//         currentMapCenter: location,
//         // Optionally adjust zoom based on city search result?
//         // currentZoom: 10.0,
//         selectedUser: null, // Deselect any user
//         isBottomSheetExpanded: false // Collapse sheet
//         ));
//     // Fetch users for the new location
//     final radius = _calculateRadiusFromZoom(state.value!.currentZoom);
//     await _fetchUsersForArea(center: location, radius: radius);
//   }

//   // TODO: Method to apply filters from FilterPage
//   Future<void> applyFiltersAndSearch(
//       {int? minAge, int? maxAge, String? gender}) async {
//     if (state.value?.currentMapCenter == null) return; // Need a center
//     state = AsyncValue.data(
//         state.value!.copyWith(isFetchingUsers: true, fetchUsersError: null));
//     await _fetchUsersForArea(
//       center: state.value!.currentMapCenter!,
//       radius: state.value?.currentSearchRadius ??
//           50000, // Use current radius or default
//       minAge: minAge,
//       maxAge: maxAge,
//       gender: gender,
//     );
//   }

//   // --- Internal Helper Methods ---

//   // Fetches users based on location and current filters
//   Future<void> _fetchUsersForArea({
//     required GeoPoint center,
//     required double radius,
//     int? minAge, // Pass filters through
//     int? maxAge,
//     String? gender,
//   }) async {
//     if (!state.hasValue) return; // Don't fetch if initial state failed

//     // Update state to indicate loading FOR USER FETCH specifically
//     state = AsyncValue.data(
//         state.value!.copyWith(isFetchingUsers: true, fetchUsersError: null));

//     final result = await _userRepository.searchUsersByLocation(
//       latitude: center.latitude,
//       longitude: center.longitude,
//       radiusMeters: radius,
//       minAge: minAge, // Pass filters
//       maxAge: maxAge,
//       gender: gender,
//     );

//     if (!state.hasValue) {
//       return; // Check again in case state changed during await
//     }

//     result.fold(
//       (failure) {
//         state = AsyncValue.data(state.value!.copyWith(
//             isFetchingUsers: false,
//             fetchUsersError: failure.message, // Store specific fetch error
//             nearbyUsers: [] // Clear users on error? Or keep old ones? Clear for now.
//             ));
//       },
//       (users) {
//         state = AsyncValue.data(state.value!.copyWith(
//           nearbyUsers: users,
//           isFetchingUsers: false,
//           fetchUsersError: null,
//           currentSearchRadius:
//               radius, // Update radius used for this successful search
//         ));
//       },
//     );
//   }

//   // Very basic radius calculation - NEEDS REFINEMENT based on map projection/zoom level specifics
//   double _calculateRadiusFromZoom(double zoom) {
//     // This is a placeholder - real calculation is more complex!
//     // Lower zoom = larger area = larger radius
//     // Higher zoom = smaller area = smaller radius
//     // Example: Map zoom 10 might be ~50km radius, zoom 14 might be ~5km
//     if (zoom < 8) return 100000; // 100km
//     if (zoom < 11) return 50000; // 50km
//     if (zoom < 13) return 10000; // 10km
//     if (zoom < 15) return 5000; // 5km
//     return 1000; // 1km for very high zoom
//   }
// }
