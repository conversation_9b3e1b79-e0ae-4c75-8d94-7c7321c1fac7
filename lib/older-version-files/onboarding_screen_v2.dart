// // lib/features/onboarding/screens/onboarding_screen.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:watermelon_draft/core/errors.dart';
// import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
// // import 'package:watermelon_draft/core/ui/watermelon_loading_widget.dart'; // Assuming you have this
// import 'package:watermelon_draft/core/ui/widgets/profile_image_picker.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:beamer/beamer.dart';
// import 'package:watermelon_draft/features/profile/widgets/city_selector_widget.dart';
// import 'package:watermelon_draft/features/profile/widgets/interests_selector_widget.dart';
// import 'package:watermelon_draft/features/profile/widgets/shared_activities_selector_widget.dart';

// class OnboardingScreen extends ConsumerStatefulWidget {
//   const OnboardingScreen({super.key});

//   @override
//   ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
// }

// class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
//   final PageController _pageController = PageController();
//   final _basicInfoFormKey = GlobalKey<FormState>(); // Key for basic info form
//   // --- Controllers ---
//   final _fullNameController = TextEditingController();
//   final _usernameController = TextEditingController();
//   final _cityController = TextEditingController(); // For location page
//   // --- Other Local State ---
//   DateTime? _selectedDate;

//   @override
//   void initState() {
//     super.initState();

//     // Initialize controllers with existing values from ViewModel
//     final initialStateAsync = ref.read(onboardingViewModelProvider);
//     initialStateAsync.whenData((initialState) {
//       // initialState here is guaranteed non-null OnboardingState
//       _fullNameController.text = initialState.fullName ?? '';
//       _usernameController.text = initialState.username ?? '';
//       _selectedDate = initialState.birthdate;
//       _cityController.text = initialState.city ?? '';
//     });
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     _fullNameController.dispose();
//     _usernameController.dispose();
//     _cityController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Listen for side effects (navigation, errors)
//     ref.listen(onboardingViewModelProvider, (previous, next) {
//       // Handle navigation on success or errors with SnackBar
//       next.whenOrNull(
//         // Use whenOrNull if you only care about specific states
//         data: (onboardingState) {
//           // Potentially navigate automatically if a certain state is reached?
//           // Or handle final success/error after viewModel.completeOnboarding() result
//         },
//         error: (error, stackTrace) {
//           if (mounted && error is Failure) {
//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(
//                 content: Text(error.message),
//                 backgroundColor: Colors.red,
//               ),
//             );
//           }
//         },
//       );
//     });

//     // Watch the state to rebuild the UI
//     final stateAsync = ref.watch(onboardingViewModelProvider);
//     final viewModel = ref.read(onboardingViewModelProvider.notifier);

//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Onboarding'),
//         // Optional: Show progress in AppBar?
//         // bottom: stateAsync.isLoading ? PreferredSize(
//         //   preferredSize: Size(double.infinity, 4.0),
//         //   child: LinearProgressIndicator(),
//         // ) : null,
//       ),
//       // Use pattern matching (switch) on the AsyncValue state
//       body: switch (stateAsync) {
//         AsyncData(:final value) => // Data loaded successfully
//           PageView(
//             controller: _pageController,
//             physics: NeverScrollableScrollPhysics(),
//             children: [
//               // Pass the unwrapped state 'value' (which is OnboardingState)
//               // AND the viewModel instance to the helper methods
//               _buildWelcomePage(
//                   context, viewModel), // Welcome might not need state
//               _buildBasicInfoPage(context, viewModel, value),
//               _buildProfilePicturePage(context, viewModel, value),
//               _buildLocationPage(context, viewModel, value),
//               _buildSharedActivitiesPage(context, viewModel, value),
//               _buildMyInterestsPage(context, viewModel, value),
//               _buildSummaryPage(context, viewModel, value),
//             ],
//           ),
//         AsyncError(:final error) => // Error state
//           Center(child: Text('Error loading onboarding: $error')),
//         _ => // Loading or initial state
//           const Center(child: CircularProgressIndicator()),
//       },
//     );
//   }

//   // --- Step 1: Welcome Page ---
//   Widget _buildWelcomePage(
//       BuildContext context, OnboardingViewModel viewModel) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Welcome to Watermelon!', style: TextStyle(fontSize: 24)),
//           SizedBox(height: 20),
//           Text('Connect with others through shared activities.',
//               textAlign: TextAlign.center),
//           SizedBox(height: 40),
//           ElevatedButton(
//             onPressed: () {
//               // viewModel.nextPage(); // Call this in ViewModel
//               _pageController.nextPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut);
//             },
//             child: Text('Get Started'),
//           ),
//         ],
//       ),
//     );
//   }

//   // --- Step 2: Basic Info Page ---
//   Widget _buildBasicInfoPage(BuildContext context,
//       OnboardingViewModel viewModel, OnboardingState state) {
//     return Form(
//       key: _basicInfoFormKey,
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             TextFormField(
//               controller: _fullNameController, // Use controller here.
//               keyboardType: TextInputType.name,
//               decoration: InputDecoration(labelText: 'Full Name'),
//               validator: (value) {
//                 if (value == null || value.isEmpty) {
//                   return 'Please enter your full name';
//                 }
//                 return null;
//               },
//               onChanged: (value) {
//                 viewModel.updateFullName(value); // Update ViewModel
//               },
//             ),
//             TextFormField(
//               controller: _usernameController,
//               decoration: InputDecoration(labelText: 'Username'),
//               validator: (value) {
//                 if (value == null || value.isEmpty) {
//                   return 'Please enter a username';
//                 }
//                 // Add more username validation as needed
//                 return null;
//               },
//               onChanged: (value) {
//                 viewModel.updateUsername(value); // Update ViewModel
//               },
//             ),
//             // Date picker
//             GestureDetector(
//               // Use GestureDetector for tap
//               onTap: () async {
//                 final DateTime? pickedDate = await showDatePicker(
//                     context: context,
//                     initialDate: DateTime.now(), // Set start date
//                     firstDate: DateTime(1900), // Set start range.
//                     lastDate: DateTime.now()); // Set end range.
//                 if (pickedDate != null && pickedDate != _selectedDate) {
//                   // Check if date is picked
//                   setState(() {
//                     _selectedDate =
//                         pickedDate; // Set state, update date to show on UI
//                   });
//                   viewModel.updateBirthdate(
//                       pickedDate); // Update ViewModel with date
//                 }
//               },
//               child: InputDecorator(
//                 decoration: InputDecoration(
//                   labelText: 'Date of Birth',
//                   border: OutlineInputBorder(), // Add a border
//                 ),
//                 child: Text(
//                   _selectedDate == null
//                       ? 'Select Date'
//                       : '${_selectedDate!.toLocal()}'
//                           .split(' ')[0], // Display selected date
//                 ),
//               ),
//             ),
//             // Gender selection (replace with your actual implementation)
//             // You can use Radio buttons, Dropdown, etc.
//             DropdownButtonFormField<String>(
//               value: state.gender,
//               items: ['Male', 'Female', 'Other', 'Prefer not to say']
//                   .map((gender) => DropdownMenuItem(
//                         value: gender,
//                         child: Text(gender),
//                       ))
//                   .toList(),
//               onChanged: (value) {
//                 if (value != null) {
//                   viewModel.updateGender(value); // Update ViewModel
//                 }
//               },
//               decoration: InputDecoration(labelText: 'Gender'),
//               validator: (value) {
//                 // Validation
//                 if (value == null || value.isEmpty) {
//                   return 'Please select your gender';
//                 }
//                 return null;
//               },
//             ),
//             SizedBox(height: 20),
//             ElevatedButton(
//               onPressed: () {
//                 if (_basicInfoFormKey.currentState!.validate()) {
//                   // Validate form
//                   viewModel.nextPage(); // Next
//                   _pageController.nextPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 }
//               },
//               child: Text('Next'),
//             ),
//             ElevatedButton(
//               onPressed: () {
//                 viewModel.previousPage();
//                 _pageController.previousPage(
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut); // Animate
//               },
//               child: Text('Back'),
//             )
//           ],
//         ),
//       ),
//     );
//   }

//   // --- Step 3: Profile Picture Page ---
//   Widget _buildProfilePicturePage(BuildContext context,
//       OnboardingViewModel viewModel, OnboardingState state) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Add a Profile Picture (Required)',
//               style: TextStyle(fontSize: 20)),
//           SizedBox(height: 8),
//           Text(
//             "Choose a photo or select a default avatar.",
//             style: TextStyle(fontSize: 14, color: Colors.grey),
//           ),
//           SizedBox(height: 16),
//           ProfileImagePicker(
//             // Pass initial values from ViewModel state
//             initialImageFile: state.profileImage,
//             initialDefaultAvatarPath: state.defaultAvatar,
//             nameForGeneratedAvatar: state.fullName!,

//             // Connect callbacks to ViewModel methods
//             onImageSelected: (image) {
//               viewModel.updateProfileImage(image);
//             },
//             onDefaultAvatarSelected: (path) {
//               viewModel.updateDefaultAvatar(path);
//             },
//             onGeneratedAvatarSelected: (color) {
//               viewModel.selectGeneratedAvatar(color); // Updates avatarType
//             },

//             updateButtonText: 'Next',
//             cancelButtonText: 'Back',
//             onUpdate: () {
//               // NEXT button action
//               // Validation: Now check the *ViewModel state*
//               if (state.avatarType != null) {
//                 // Simply check if any type is set
//                 viewModel.nextPage();
//                 if (state.currentPage < 5) {
//                   _pageController.nextPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 }
//               } else {
//                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                     content: Text(
//                         'Please select a profile picture option.'))); // More generic message
//               }
//             },
//             onBack: () {
//               // BACK button action
//               viewModel.previousPage();
//               if (state.currentPage > 0) {
//                 _pageController.previousPage(
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               }
//             },
//           ),
//           Text(
//             // Add reminder text
//             "You can change your profile picture later in your profile settings.",
//             style: TextStyle(fontSize: 12, color: Colors.grey),
//             textAlign: TextAlign.center, // Center the text
//           ),
//         ],
//       ),
//     );
//   }

//   // --- Step 4: Location Page ---
//   Widget _buildLocationPage(BuildContext context, OnboardingViewModel viewModel,
//       OnboardingState state) {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text('Set Your Location',
//               style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
//           SizedBox(height: 8),
//           Text(
//             'Watermelon uses your location to help you find connections and activities nearby.',
//             style: TextStyle(fontSize: 14, color: Colors.grey),
//             textAlign: TextAlign.left,
//           ),
//           SizedBox(height: 24),

//           // --- Use the Reusable CitySelectorWidget ---
//           CitySelectorWidget(
//             initialCity: state.city,
//             initialLocation: state.location,
//             initialCountry: state.country,
//             onLocationSet: (location, city, country) {
//               // This callback updates the ViewModel's state
//               // The CitySelectorWidget handles getting location/city/country
//               if (location != null) {
//                 // Prefer GPS location if available, update all fields
//                 viewModel.updateLocationFromPoint(location);
//               } else if (city != null) {
//                 // Fallback to city name if GPS failed or wasn't used
//                 // This method in the VM should also update the country
//                 viewModel.updateLocationFromAddress(city);
//               }
//               // If both are null, the ViewModel state remains unchanged for location/city/country
//             },
//           ),

//           Spacer(), // Push buttons to bottom

//           // --- Navigation Buttons ---
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               TextButton(
//                 onPressed: () {
//                   viewModel.previousPage();
//                   _pageController.previousPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 },
//                 child: Text('Back'),
//               ),
//               ElevatedButton(
//                 // --- Validation for Next Button ---
//                 onPressed: (state.location != null ||
//                         (state.city != null && state.city!.isNotEmpty))
//                     ? () {
//                         // Enabled if location OR city is set in the ViewModel state
//                         viewModel.nextPage();
//                         _pageController.nextPage(
//                             duration: Duration(milliseconds: 300),
//                             curve: Curves.easeInOut);
//                       }
//                     : null, // Disable button if neither location nor city is set
//                 child: Text('Next'),
//               ),
//             ],
//           )
//         ],
//       ),
//     );
//   }

// // --- Step 5: "Shared Activities" Page ---
//   Widget _buildSharedActivitiesPage(BuildContext context,
//       OnboardingViewModel viewModel, OnboardingState state) {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start, // Align content to start
//         children: [
//           Text('Select Activities to Share',
//               style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
//           SizedBox(height: 8),
//           Text(
//             "Select at least one activity you'd like to share with others. You can add up to 5. Drag to reorder.",
//             style: TextStyle(fontSize: 14, color: Colors.grey),
//           ),
//           SizedBox(height: 16),

//           // --- Use the Reusable Widget ---
//           Expanded(
//             // Allow the selector to take available space
//             child: SingleChildScrollView(
//               // Make the content scrollable if needed
//               child: SharedActivitiesSelectorWidget(
//                 initialActivities: state.sharedActivities ??
//                     [], // Pass initial selection from ViewModel
//                 onActivitiesChanged: (updatedActivities) {
//                   // Callback updates the ViewModel
//                   viewModel.updateSharedActivities(updatedActivities);
//                 },
//                 // maxActivities: 5, // Default is 5, explicitly pass if needed
//               ),
//             ),
//           ),
//           SizedBox(height: 16),
//           Text(
//             // Add reminder text
//             "You can add, remove, or change these interests later in your profile settings.",
//             style: TextStyle(fontSize: 12, color: Colors.grey),
//             textAlign: TextAlign.center, // Center the text
//           ),
//           // --- Navigation Buttons ---
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               TextButton(
//                 // Changed to TextButton for Back
//                 onPressed: () {
//                   viewModel.previousPage();
//                   _pageController.previousPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 },
//                 child: Text('Back'),
//               ),
//               ElevatedButton(
//                 // Disable "Next" if no activities are selected
//                 onPressed: (state.sharedActivities?.isNotEmpty ?? false)
//                     ? () {
//                         viewModel.nextPage();
//                         _pageController.nextPage(
//                             duration: Duration(milliseconds: 300),
//                             curve: Curves.easeInOut);
//                       }
//                     : null, // Disable if empty
//                 child: Text('Next'),
//               ),
//             ],
//           )
//         ],
//       ),
//     );
//   }

//   // --- Step 6: My Interests Page ---
//   Widget _buildMyInterestsPage(BuildContext context,
//       OnboardingViewModel viewModel, OnboardingState state) {
//     return Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text('My Interests (Optional)',
//               style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
//           SizedBox(height: 16),
//           Expanded(
//             // Make the selector scrollable if needed
//             child: SingleChildScrollView(
//               // Add SingleChildScrollView
//               child: InterestsSelectorWidget(
//                 initialKeywords: state.myInterests ?? [],
//                 onKeywordsChanged: (updatedKeywords) {
//                   viewModel.updateMyInterests(updatedKeywords);
//                 },
//               ),
//             ),
//           ),
//           SizedBox(height: 8),
//           Text(
//             "You can add, remove, or change these interests later in your profile settings.",
//             style: TextStyle(fontSize: 12, color: Colors.grey),
//             textAlign: TextAlign.center, // Center the text
//           ),

//           // --- Navigation Buttons ---
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               TextButton(
//                 onPressed: () {
//                   viewModel
//                       .nextPage(); // Still go next, just don't save interests
//                   _pageController.nextPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 },
//                 child: Text("Skip for Now"),
//               ),
//               ElevatedButton(
//                 onPressed: () {
//                   viewModel.nextPage();
//                   _pageController.nextPage(
//                       duration: Duration(milliseconds: 300),
//                       curve: Curves.easeInOut);
//                 },
//                 child: Text('Next'),
//               ),
//             ],
//           )
//         ],
//       ),
//     );
//   }

//   // --- Step 7: Summary Page ---
//   Widget _buildSummaryPage(BuildContext context, OnboardingViewModel viewModel,
//       OnboardingState state) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Text('Summary', style: TextStyle(fontSize: 20)),
//           SizedBox(height: 16),
//           // Display a summary of the collected information.
//           // You can use ListTile widgets or a custom layout.
//           // Example:
//           ListTile(
//             title: Text('Full Name'),
//             subtitle: Text(state.fullName ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.previousPage(
//                   // Back to page index 1
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut,
//                 );
//               },
//             ),
//           ),
//           ListTile(
//             title: Text('Username'),
//             subtitle: Text(state.username ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.previousPage(
//                   // Back to page index 1
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut,
//                 );
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Date of Birth"),
//             subtitle: Text(state.birthdate?.toIso8601String() ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 1
//                     1,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Gender"),
//             subtitle: Text(state.gender ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 1
//                     1,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("City"),
//             subtitle: Text(state.city ?? "N/A"),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 3
//                     3,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           ListTile(
//             title: Text("Country"),
//             subtitle: Text(state.country ?? 'N/A'),
//           ),
//           ListTile(
//             title: Text("Looking For"),
//             subtitle: Text(state.sharedActivities?.join(", ") ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 _pageController.animateToPage(
//                     // Back to page index 4
//                     4,
//                     duration: Duration(milliseconds: 300),
//                     curve: Curves.easeInOut);
//               },
//             ),
//           ),
//           // ... display other fields ...
//           ListTile(
//             title: Text("My Interests"),
//             subtitle: Text(state.myInterests?.join(", ") ?? 'N/A'),
//             trailing: IconButton(
//               icon: Icon(Icons.edit),
//               onPressed: () {
//                 // Navigate to a page where user set interest
//               },
//             ),
//           ),
//           SizedBox(height: 20),
//           ElevatedButton(
//             onPressed: () async {
//               // Complete onboarding (save to Supabase, set onboarding_complete flag)
//               final result = await viewModel.completeOnboarding();
//               result.fold((l) {
//                 ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                   content: Text(l.message),
//                   backgroundColor: Colors.red,
//                 ));
//               }, (r) {
//                 // Navigate to home
//                 if (mounted) {
//                   context.beamToNamed('/home'); // Navigate using Beamer
//                 }
//               });
//             },
//             child: Text('Complete Onboarding'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               viewModel.previousPage();
//               _pageController.previousPage(
//                   duration: Duration(milliseconds: 300),
//                   curve: Curves.easeInOut); // Animate
//             },
//             child: Text('Back'),
//           ),
//         ],
//       ),
//     );
//   }
// }