// lib/features/profile/widgets/shared_activities_selector_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reorderables/reorderables.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart'; // Correct model
import 'package:watermelon_draft/core/providers.dart'; // Access repositories
import 'package:collection/collection.dart'; // For groupBy

class SharedActivitiesSelectorWidget extends ConsumerStatefulWidget {
  final List<String>
      initialActivities; // List of initially selected activity IDs
  final Function(List<String> updatedActivities) onActivitiesChanged;
  final int maxActivities;

  const SharedActivitiesSelectorWidget({
    super.key,
    required this.initialActivities,
    required this.onActivitiesChanged,
    this.maxActivities = 5,
  });

  @override
  ConsumerState<SharedActivitiesSelectorWidget> createState() =>
      _SharedActivitiesSelectorWidgetState();
}

class _SharedActivitiesSelectorWidgetState
    extends ConsumerState<SharedActivitiesSelectorWidget> {
  final TextEditingController _activityController = TextEditingController();
  List<SharedActivity> _allActivities = []; // Store all activities from DB
  List<SharedActivity> _filteredSuggestions = []; // Filtered based on input
  List<String> _selectedActivityIds = []; // List of selected activity IDs
  bool _isLoadingActivities = true;

  @override
  void initState() {
    super.initState();
    _selectedActivityIds =
        List.from(widget.initialActivities); // Initialize local state
    _fetchActivities();
  }

  Future<void> _fetchActivities() async {
    final result = await ref
        .read(sharedActivitiesRepositoryProvider)
        .getSharedActivities();
    result.fold(
      (failure) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("Error fetching activities: ${failure.message}")));
          setState(() => _isLoadingActivities = false);
        }
      },
      (activities) {
        if (mounted) {
          setState(() {
            _allActivities = activities;
            _isLoadingActivities = false;
          });
        }
      },
    );
  }

  @override
  void dispose() {
    _activityController.dispose();
    super.dispose();
  }

  void _filterSuggestions(String input) {
    if (input.isEmpty) {
      if (mounted) setState(() => _filteredSuggestions = []);
      return;
    }
    final lowerCaseInput = input.toLowerCase();
    if (mounted) {
      setState(() {
        _filteredSuggestions = _allActivities.where((activity) {
          return activity.activityName.toLowerCase().contains(lowerCaseInput) &&
              !_selectedActivityIds
                  .contains(activity.activityId); // Exclude selected
        }).toList();
      });
    }
  }

  SharedActivity? _findActivity(String activityId) {
    return _allActivities.firstWhereOrNull((a) => a.activityId == activityId);
  }

  void _addActivity(String activityId) {
    if (_selectedActivityIds.length < widget.maxActivities &&
        !_selectedActivityIds.contains(activityId)) {
      if (mounted) {
        setState(() {
          _selectedActivityIds.add(activityId);
          _activityController.clear();
          _filteredSuggestions = [];
        });
        widget.onActivitiesChanged(_selectedActivityIds); // Notify parent
      }
    } else if (_selectedActivityIds.length >= widget.maxActivities) {
      _showMaxActivitiesError();
    }
  }

  void _removeActivity(String activityId) {
    // --- VALIDATION: Prevent removal if only one activity remains ---
    if (_selectedActivityIds.length > 1) {
      if (mounted) {
        setState(() {
          _selectedActivityIds.remove(activityId);
        });
        widget.onActivitiesChanged(_selectedActivityIds); // Notify parent
      }
    } else {
      // Show error message if trying to remove the last activity
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('You must select at least one activity.')),
        );
      }
    }
  }

  void _showMaxActivitiesError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content:
              Text("Maximum ${widget.maxActivities} activities allowed.")));
    }
  }

  // --- Reorder Handler ---
  void _onReorder(int oldIndex, int newIndex) {
    if (mounted) {
      setState(() {
        // ReorderableWrap provides indices within the current list (_selectedActivityIds)
        final String item = _selectedActivityIds.removeAt(oldIndex);
        _selectedActivityIds.insert(newIndex, item);
      });
      widget.onActivitiesChanged(
          _selectedActivityIds); // Notify parent with the new order
    }
  }

  // --- Bottom Sheet Logic ---
  Future<void> _showSharedActivitiesSelectionSheet() async {
    // Use a local copy for the bottom sheet selections
    List<String> localSelectedActivities = List.from(_selectedActivityIds);

    // Group activities by category for the bottom sheet display
    final groupedActivities =
        groupBy(_allActivities, (SharedActivity act) => act.category);

    final List<String>? result = await showModalBottomSheet<List<String>?>(
      context: context,
      isScrollControlled: true, // Important for potentially long lists
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setSheetState) {
            return FractionallySizedBox(
              // Control sheet height
              heightFactor: 0.8, // Example: Use 80% of screen height
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Text(
                      'Select Activities to Share',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                  // --- Selected Chips (Inside Bottom Sheet) ---
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Wrap(
                      spacing: 8.0,
                      runSpacing: 4.0,
                      children: localSelectedActivities.map((activityId) {
                        final activity = _findActivity(activityId);
                        return Chip(
                          label: Text(activity?.activityName ?? 'Unknown'),
                          deleteIcon: Icon(Icons.close),
                          onDeleted: () {
                            // --- Prevent removal if only one left ---
                            if (localSelectedActivities.length > 1) {
                              setSheetState(() {
                                localSelectedActivities.remove(activityId);
                              });
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                  content: Text(
                                      'You must select at least one activity.')));
                            }
                          },
                        );
                      }).toList(),
                    ),
                  ),
                  Divider(height: 20),
                  // --- Category List (with ExpansionTiles) ---
                  Expanded(
                    child: ListView(
                      children: groupedActivities.entries.map((entry) {
                        final categoryName = entry.key;
                        final activities = entry.value;
                        return ExpansionTile(
                          title: Text(categoryName ?? "Other"), // Category name
                          children: activities
                              .map((activity) => CheckboxListTile(
                                    title: Text(activity.activityName),
                                    value: localSelectedActivities
                                        .contains(activity.activityId),
                                    onChanged: (bool? value) {
                                      setSheetState(() {
                                        if (value == true) {
                                          if (localSelectedActivities.length <
                                              widget.maxActivities) {
                                            localSelectedActivities
                                                .add(activity.activityId);
                                          } else {
                                            _showMaxActivitiesError();
                                          }
                                        } else {
                                          // --- Prevent removal if only one left ---
                                          if (localSelectedActivities.length >
                                              1) {
                                            localSelectedActivities
                                                .remove(activity.activityId);
                                          } else {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                                    content: Text(
                                                        'You must select at least one activity.')));
                                          }
                                        }
                                      });
                                    },
                                  ))
                              .toList(),
                        );
                      }).toList(),
                    ),
                  ),
                  // --- Done Button ---
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ElevatedButton(
                      onPressed: localSelectedActivities
                              .isNotEmpty // Enable only if > 0
                          ? () =>
                              Navigator.pop(context, localSelectedActivities)
                          : null, // Disable if empty
                      child: Text('Done'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );

    // Update the main state if the user pressed "Done"
    if (result != null && mounted) {
      setState(() {
        _selectedActivityIds = result;
      });
      widget.onActivitiesChanged(_selectedActivityIds); // Notify parent
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingActivities) {
      return Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select up to ${widget.maxActivities} activities you\'d like to share.',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        SizedBox(height: 16),

          // --- Selected Chips (ReorderableWrap) ---
        ReorderableWrap(
          spacing: 8.0,
          runSpacing: 4.0,
          needsLongPressDraggable: false,
          onReorder: _onReorder, // Pass handler
          children: _selectedActivityIds.map((activityId) {
            // Map over IDs
            final activity =
                _findActivity(activityId); // Find the activity object
            return Chip(
              key: ValueKey(activityId), // Use ID for unique key
              label: Text(activity?.activityName ?? activityId),
              deleteIcon: Icon(Icons.close),
              onDeleted: () => _removeActivity(activityId),
            );
          }).toList(),
        ),
        SizedBox(height: 16),

        // --- Autocomplete Input ---
        TextFormField(
          controller: _activityController,
          decoration: InputDecoration(
            labelText: 'Add Activities',
            hintText: 'Start typing...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: _filterSuggestions,
          onFieldSubmitted: (value) {
            final suggested = _filteredSuggestions
                .firstWhereOrNull((a) => a.activityName == value);
            if (suggested != null) {
              _addActivity(suggested.activityId);
            }
          },
        ),
        // --- Autocomplete Suggestions ---
        if (_filteredSuggestions.isNotEmpty)
          Container(
            constraints: BoxConstraints(maxHeight: 150),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _filteredSuggestions.length,
              itemBuilder: (context, index) {
                final activity = _filteredSuggestions[index];
                return ListTile(
                  title: Text(activity.activityName),
                  onTap: () => _addActivity(activity.activityId),
                );
              },
            ),
          ),

        // --- "See All" Button ---
        SizedBox(height: 8),
        TextButton(
          onPressed: _showSharedActivitiesSelectionSheet,
          child: Text('See all activities list'),
        ),
      ],
    );
  }
}
