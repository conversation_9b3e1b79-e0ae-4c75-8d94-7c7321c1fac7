// // lib/core/routes.dart
// import 'package:beamer/beamer.dart';
// import 'package:flutter/material.dart';
// import 'package:watermelon_draft/features/auth/screens/login_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/signup_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/reset_password_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/welcome_screen.dart';

// // --- Authentication Location ---
// class AuthLocation extends BeamLocation<BeamState> {
//   @override
//   List<String> get pathPatterns =>
//       ['/login', '/signup', '/reset-password', '/welcome'];

//   @override
//   List<BeamPage> buildPages(BuildContext context, BeamState state) => [
//         if (state.uri.pathSegments.contains('welcome'))
//           BeamPage(
//             key: <PERSON><PERSON><PERSON>('welcome'),
//             title: 'Welcome',
//             child: WelcomeScreen(), // Add welcome page
//           ),
//         if (state.uri.pathSegments.contains('login'))
//           BeamPage(
//             key: ValueKey('login'),
//             title: 'Login',
//             child: LoginScreen(),
//           ),
//         if (state.uri.pathSegments.contains('signup'))
//           BeamPage(
//             key: ValueKey('signup'),
//             title: 'Sign Up',
//             child: SignupScreen(), // Assuming you have a SignupScreen
//           ),
//         if (state.uri.pathSegments.contains('reset-password')) // Add reset
//           BeamPage(
//             key: ValueKey('reset-password'),
//             title: 'Reset password',
//             child: ResetPasswordScreen(),
//           ),
//         // ... other auth-related screens ...
//       ];
// }

// final routerDelegate = BeamerDelegate(
//   initialPath: "/welcome", // Start at welcome page.
//   locationBuilder: BeamerLocationBuilder(
//     // Use BeamerLocationBuilder
//     beamLocations: [
//       AuthLocation(), // Add auth location
//       //HomeLocation() Add other locations later
//     ],
//   ),
//   // Add guards if needed, for example to protect route
// //    guards: [
// //     // Guard /profile route, redirect to /login if user is not logged in
// //     BeamGuard(
// //       pathPatterns: ['/profile/*', '/home'], // Add all protected route
// //       check: (context, location) => Supabase.instance.client.auth.currentUser != null,
// //       showPage: BeamPage(key: ValueKey('login'), child: LoginScreen()),
// //     )
// //   ]
// );
