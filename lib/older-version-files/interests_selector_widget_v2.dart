// // lib/features/profile/widgets/interests_selector_widget.dart
// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:collection/collection.dart';
// import 'package:watermelon_draft/core/models/keyword.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:watermelon_draft/widgets/keyword_search_input_widget.dart'; // Reusable input
// import 'package:watermelon_draft/widgets/selected_items_chips_widget.dart'; // Reusable chips
// import 'package:reorderables/reorderables.dart'; // Import for ReorderableWrap (used by SelectedItemsChipsWidget potentially)
// // TODO: Add fuzzywuzzy package import if implementing fuzzy matching
// // import 'package:fuzzywuzzy/fuzzywuzzy.dart' as fuzzy;

// class InterestsSelectorWidget extends ConsumerStatefulWidget {
//   final List<String> initialKeywords;
//   final Function(List<String> updatedKeywords) onKeywordsChanged;
//   final int maxKeywords;
//   final int maxCustomKeywords;

//   const InterestsSelectorWidget({
//     super.key,
//     required this.initialKeywords,
//     required this.onKeywordsChanged,
//     this.maxKeywords = 5,
//     this.maxCustomKeywords = 2,
//   });

//   @override
//   ConsumerState<InterestsSelectorWidget> createState() =>
//       _InterestsSelectorWidgetState();
// }

// class _InterestsSelectorWidgetState
//     extends ConsumerState<InterestsSelectorWidget> {
//   final TextEditingController _customKeywordController =
//       TextEditingController();
//   late List<String> _selectedKeywords;
//   List<Keyword> _allPredefinedKeywords =
//       []; // Store predefined keywords for fuzzy matching

//   @override
//   void initState() {
//     super.initState();
//     _selectedKeywords = List.from(widget.initialKeywords);
//     _loadAllKeywords(); // Load keywords for fuzzy matching
//   }

//   // Load predefined keywords for fuzzy matching custom ones
//   Future<void> _loadAllKeywords() async {
//     // Use read as we likely only need this once for fuzzy matching reference
//     final keywordsAsync = ref.read(keywordsProvider);
//     keywordsAsync.whenData((keywords) {
//       if (mounted) {
//         _allPredefinedKeywords = keywords;
//       }
//     });
//     // Handle error/loading for keywordsProvider if necessary,
//     // though fuzzy matching can potentially proceed without it initially.
//   }

//   @override
//   void dispose() {
//     _customKeywordController.dispose();
//     super.dispose();
//   }

//   // --- Keyword Management Logic ---

//   void _addKeyword(String keywordText, {bool isCustom = false}) {
//     if (keywordText.trim().isEmpty) return;
//     final normalizedKeyword = keywordText.trim().toLowerCase();

//     // Check total limit
//     if (_selectedKeywords.length >= widget.maxKeywords) {
//       _showErrorSnackbar('Maximum ${widget.maxKeywords} interests allowed.');
//       return;
//     }

//     // Check if already added
//     if (_selectedKeywords.any((k) => k.toLowerCase() == normalizedKeyword)) {
//       _showErrorSnackbar('"${keywordText}" already added.');
//       if (isCustom) _customKeywordController.clear();
//       return;
//     }

//     if (isCustom) {
//       // Check custom keyword limit
//       final customCount = _selectedKeywords.where(_isPotentiallyCustom).length;
//       if (customCount >= widget.maxCustomKeywords) {
//         _showErrorSnackbar(
//             'Maximum ${widget.maxCustomKeywords} custom interests allowed.');
//         return;
//       }

//       // --- TODO: Implement Fuzzy Matching ---
//       // bool closeMatchFound = _checkFuzzyMatch(normalizedKeyword);
//       // if (closeMatchFound) {
//       //   _showFuzzyMatchDialog(normalizedKeyword); // Ask user if they want to add anyway
//       //   return; // Don't add yet, wait for dialog confirmation
//       // }
//       // --- End Fuzzy Match Placeholder ---

//       // Add custom keyword if no close match or user confirms
//       if (mounted) {
//         setState(() {
//           _selectedKeywords
//               .add(normalizedKeyword); // Add normalized custom keyword
//           _customKeywordController.clear();
//         });
//         widget.onKeywordsChanged(List.from(_selectedKeywords));
//       }
//     } else {
//       // Add predefined keyword (coming from KeywordSearchInputWidget)
//       if (mounted) {
//         setState(() {
//           _selectedKeywords.add(normalizedKeyword); // Add directly
//         });
//         widget.onKeywordsChanged(List.from(_selectedKeywords));
//       }
//     }
//   }

//   // Helper to check if a keyword *might* be custom (simple check for now)
//   // Assumes predefined keywords don't contain spaces? Refine if needed.
//   bool _isPotentiallyCustom(String keyword) {
//     // This is a placeholder. A better way is needed if predefined keywords can have spaces,
//     // perhaps by checking against the _allPredefinedKeywords list.
//     return keyword.contains(' '); // Basic check for now
//   }

//   // Placeholder for fuzzy matching logic
//   bool _checkFuzzyMatch(String customKeyword) {
//     if (_allPredefinedKeywords.isEmpty)
//       return false; // Cannot check if list isn't loaded

//     print("Fuzzy Check (Placeholder): Checking '$customKeyword'");
//     // TODO: Use fuzzywuzzy or similar package
//     // Example:
//     // final results = fuzzy.extractTop(query: customKeyword, choices: _allPredefinedKeywords.map((k) => k.keywordText).toList(), limit: 1);
//     // if (results.isNotEmpty && results.first.score > 80) { // Threshold score 0-100
//     //    print("Found close match: ${results.first.choice}");
//     //    return true;
//     // }
//     return false;
//   }

//   // Placeholder for fuzzy match suggestion dialog
//   Future<void> _showFuzzyMatchDialog(String customKeyword) async {
//     // TODO: Implement dialog suggesting close matches found via fuzzy logic
//     _showErrorSnackbar(
//         "Fuzzy match dialog not implemented (Keyword: $customKeyword). Added anyway.");
//     // TEMPORARILY add keyword even if match found, until dialog is implemented
//     if (mounted) {
//       setState(() {
//         _selectedKeywords.add(customKeyword);
//         _customKeywordController.clear();
//       });
//       widget.onKeywordsChanged(List.from(_selectedKeywords));
//     }
//   }

//   void _removeKeyword(String keyword) {
//     if (mounted) {
//       setState(() {
//         _selectedKeywords.remove(keyword);
//       });
//       widget.onKeywordsChanged(List.from(_selectedKeywords));
//     }
//   }

//   void _onReorder(int oldIndex, int newIndex) {
//     if (mounted) {
//       setState(() {
//         // Adjust index for items being shifted
//         if (newIndex > oldIndex) {
//           newIndex -= 1;
//         }
//         final String item = _selectedKeywords.removeAt(oldIndex);
//         _selectedKeywords.insert(newIndex, item);
//       });
//       widget.onKeywordsChanged(List.from(_selectedKeywords));
//     }
//   }

//   void _showErrorSnackbar(String message) {
//     if (mounted) {
//       ScaffoldMessenger.of(context)
//           .removeCurrentSnackBar(); // Remove previous snackbars
//       ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//           content: Text(message), duration: const Duration(seconds: 2)));
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Fetch keywords once if needed for mapping IDs in chips (not needed here as we store strings)
//     // final allKeywordsAsync = ref.watch(keywordsProvider);

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Select up to ${widget.maxKeywords} interests (${widget.maxCustomKeywords} custom max).',
//           style: Theme.of(context)
//               .textTheme
//               .bodySmall
//               ?.copyWith(color: Colors.grey[600]),
//         ),
//         const SizedBox(height: 10),

//         // --- Display Selected Chips (Reorderable) ---
//         SelectedItemsChipsWidget(
//           items: _selectedKeywords, // Pass the list of strings
//           onItemDeleted: _removeKeyword, // Pass remove helper
//           noItemsText: 'No interests added yet.',
//           isReorderable: true, // Enable reordering
//           onReorder: _onReorder, // Pass reorder handler
//         ),
//         const SizedBox(height: 16),

//         // --- Input for Predefined Keywords (Autocomplete) ---
//         KeywordSearchInputWidget(
//           onKeywordSelected: (keyword) =>
//               _addKeyword(keyword.keywordText, isCustom: false),
//           currentSelectionNames: _selectedKeywords, // Pass current selections
//         ),
//         const SizedBox(height: 16),

//         // --- Input for Custom Keywords ---
//         TextFormField(
//           controller: _customKeywordController,
//           maxLength: 50, // Limit custom keyword length
//           decoration: InputDecoration(
//               labelText: 'Add Custom Interest',
//               hintText: 'Type a custom interest...',
//               border: const OutlineInputBorder(),
//               counterText: "",
//               suffixIcon: IconButton(
//                 // Use suffix to add
//                 icon: Icon(Icons.add_circle_outline),
//                 tooltip: 'Add Custom Interest',
//                 onPressed: () =>
//                     _addKeyword(_customKeywordController.text, isCustom: true),
//               )),
//           onFieldSubmitted: (value) =>
//               _addKeyword(value, isCustom: true), // Also add on submit
//         ),
//       ],
//     );
//   }
// }
