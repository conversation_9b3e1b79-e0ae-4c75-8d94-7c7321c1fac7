// // lib/features/onboarding/viewmodels/onboarding_viewmodel.dart
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:watermelon_draft/core/providers.dart';
// import 'package:watermelon_draft/core/services/auth_repository.dart';
// import 'package:watermelon_draft/core/services/user_repository.dart';
// import 'package:watermelon_draft/core/services/location_service.dart';
// import 'package:watermelon_draft/core/services/supabase_service.dart';
// import 'package:watermelon_draft/core/models/user.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:watermelon_draft/core/errors.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:uuid/uuid.dart';

// part 'onboard_viewmodel_old.freezed.dart';
// part 'onboard_viewmodel_old.g.dart';

// @riverpod
// class OnboardingViewModel extends _$OnboardingViewModel {
//   late final AuthRepository _authRepository;
//   late final UserRepository _userRepository;
//   late final LocationService _locationService;
//   late final SharedPreferences _prefs;

//   @override
//   FutureOr<OnboardingState> build() async {
//     _authRepository = ref.read(authRepositoryProvider);
//     _userRepository = ref.read(userRepositoryProvider);
//     _locationService =
//         ref.read(locationServiceProvider); // Get LocationService instance
//     _prefs = await ref.read(sharedPreferencesProvider.future);
//     return OnboardingState(); // Initial state
//   }

//   // Methods to update the state (these are just examples, add more as needed)

//   void updateFullName(String fullName) {
//     state = AsyncData(state.value!.copyWith(fullName: fullName));
//   }

//   void updateUsername(String username) {
//     state = AsyncData(state.value!.copyWith(username: username));
//   }

//   void updateBirthdate(DateTime birthdate) {
//     state = AsyncData(state.value!.copyWith(birthdate: birthdate));
//   }

//   void updateGender(String gender) {
//     state = AsyncData(state.value!.copyWith(gender: gender));
//   }

//   void updateCity(String city) {
//     state = AsyncData(state.value!.copyWith(city: city));
//   }

//   Future<void> updateLocationFromPoint(GeoPoint point) async {
//     final country = await _locationService.getCountryFromLocation(point);
//     state = AsyncData(state.value!.copyWith(location: point, country: country));
//   }

//   Future<void> updateLocationFromAddress(String address) async {
//     final country = await _locationService.getCountryFromCity(address);
//     state = AsyncData(state.value!.copyWith(city: address, country: country));
//   }

//   void updateProfileImage(XFile? image) {
//     state = AsyncData(state.value!.copyWith(profileImage: image));
//   }

//   void updateDefaultAvatar(String? avatar) {
//     // Add
//     state = AsyncData(state.value!.copyWith(defaultAvatar: avatar));
//   }

//   void updateLookingFor(List<String> activities) {
//     // For SharedActivities
//     state = AsyncData(
//         state.value!.copyWith(lookingFor: activities)); // Use looking for
//   }

//   void updateMyInterests(List<String> keywords) {
//     state = AsyncData(state.value!.copyWith(myInterests: keywords));
//   }

//   // Method to handle next page navigation (you can implement this logic here or in the UI)
//   void nextPage() {
//     state = AsyncData(
//         state.value!.copyWith(currentPage: state.value!.currentPage + 1));
//   }

//   void previousPage() {
//     state = AsyncData(
//         state.value!.copyWith(currentPage: state.value!.currentPage - 1));
//   }

//   Future<Either<Failure, Unit>> completeOnboarding() async {
//     // --- VALIDATION (Ensure all required fields are filled) ---
//     final validationResult = validate();
//     if (validationResult.isLeft()) {
//       return validationResult; // Return the error if validation fails
//     }
//     // --- Get Current User ---
//     final currentUser = await _authRepository.currentUser(); //get current user
//     if (currentUser == null) {
//       return left(AuthFailure("User is not login"));
//     }
//     // --- Upload image if needed, and set avatar type ---
//     String? profilePictureUrl;
//     String? avatarType;

//     if (state.value!.profileImage != null) {
//       final uploadResult = await _userRepository.uploadProfilePicture(
//           currentUser.id, state.value!.profileImage!);
//       if (uploadResult.isLeft()) {
//         return left(uploadResult
//             .getLeft()
//             .getOrElse((_) => DatabaseFailure("Failed to upload image.")));
//       }
//       profilePictureUrl = uploadResult.getRight().getOrNull();
//       avatarType = "uploaded";
//     } else if (state.value!.defaultAvatar != null) {
//       profilePictureUrl = state.value!.defaultAvatar;
//       avatarType = "default";
//     } else {
//       final name = state.value!.fullName ?? state.value!.username;
//       avatarType = "generated"; // generate
//     }

//     // Calculate age (you might have a helper function for this)
//     int? age;
//     if (state.value!.birthdate != null) {
//       final now = DateTime.now();
//       age = now.year - state.value!.birthdate!.year;
//       if (now.month < state.value!.birthdate!.month ||
//           (now.month == state.value!.birthdate!.month &&
//               now.day < state.value!.birthdate!.day)) {
//         age--;
//       }
//     }

//     // --- Create/Update User object ---
//     final updatedUser = User(
//         userId: currentUser.id, // Keep the existing ID
//         username: state.value!.username!,
//         email: currentUser.email!, // Keep existing email
//         fullName: state.value!.fullName!,
//         age: age,
//         gender: state.value!.gender!,
//         city: state.value!.city,
//         lookingFor: state.value!.lookingFor, // Save selected.
//         profilePictureUrl: profilePictureUrl,
//         myInterests: state.value!.myInterests,
//         onboardingComplete: true,
//         location: state.value!.location,
//         avatarType: avatarType // Save avatar type
//         );
//     // --- Save to Supabase ---
//     final result =
//         await _userRepository.updateUser(updatedUser); //update to database.
//     if (result.isRight()) {
//       // Set onboarding complete flag using shared preference.
//       await _prefs.setBool(
//           'onboarding_complete_${currentUser.id}', true); // Use user id
//     }
//     return result; //Success or failure
//   }

//   Either<Failure, Unit> validate() {
//     final state = this.state.value!; // To avoid reslove this.state
//     if (state.fullName == null || state.fullName!.isEmpty) {
//       return left(ValidationError('Please enter your full name.'));
//     }
//     if (state.username == null || state.username!.isEmpty) {
//       return left(ValidationError('Please enter a username.'));
//     }
//     if (state.birthdate == null) {
//       return left(ValidationError('Please enter your birthdate.'));
//     }
//     if (state.gender == null || state.gender!.isEmpty) {
//       return left(ValidationError('Please select your gender.'));
//     }
//     if (state.city == null || state.city!.isEmpty) {
//       return left(ValidationError("Please enter your city."));
//     }
//     if (state.lookingFor == null || state.lookingFor!.isEmpty) {
//       return left(ValidationError(
//           'Please select at least one "Looking For" activity.'));
//     }
//     // Note: profileImage is handled differently, as it can be a default avatar.
//     // The check for profileImage happens when creating the User object.

//     return right(unit); // All fields are valid
//   }
// }

// @freezed
// abstract class OnboardingState with _$OnboardingState {
//   factory OnboardingState(
//       {@Default(0) int currentPage,
//       String? fullName,
//       String? username,
//       DateTime? birthdate,
//       String? gender,
//       String? city,
//       GeoPoint? location,
//       XFile? profileImage, // Add to state
//       String? defaultAvatar,
//       List<String>? lookingFor, // For "Looking For" selections
//       List<String>? myInterests, // For "My Interests" selections
//       String? country}) = _OnboardingState;

//   OnboardingState._();
// }
