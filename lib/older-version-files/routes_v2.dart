// // lib/core/routes.dart
// import 'package:beamer/beamer.dart';
// import 'package:flutter/material.dart';
// import 'package:watermelon_draft/features/auth/screens/login_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/signup_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/reset_password_screen.dart';
// import 'package:watermelon_draft/features/auth/screens/welcome_screen.dart'; // Import

// // --- Define Beamer Locations (if you're using them) ---

// class AuthLocation extends BeamLocation<BeamState> {
//   @override
//   List<String> get pathPatterns =>
//       ['/login', '/signup', '/reset-password', '/welcome']; // Add

//   @override
//   List<BeamPage> buildPages(BuildContext context, BeamState state) => [
//         if (state.uri.pathSegments.contains('welcome'))
//           BeamPage(
//             key: <PERSON><PERSON><PERSON>('welcome'),
//             title: 'Welcome',
//             child: WelcomeScreen(), // Add welcome page
//           ),
//         if (state.uri.pathSegments.contains('login'))
//           BeamPage(
//             key: ValueKey('login'),
//             title: 'Login',
//             child: LoginScreen(),
//           ),
//         if (state.uri.pathSegments.contains('signup'))
//           BeamPage(
//             key: ValueKey('signup'),
//             title: 'Sign Up',
//             child: SignupScreen(), // Assuming you have a SignupScreen
//           ),
//         if (state.uri.pathSegments.contains('reset-password')) // Add reset
//           BeamPage(
//             key: ValueKey('reset-password'),
//             title: 'Reset password',
//             child: ResetPasswordScreen(),
//           ),
//       ];
// }

// // --- Beamer Delegate ---
// final routerDelegate = BeamerDelegate(
//   locationBuilder: RoutesLocationBuilder(
//     // Or BeamerLocationBuilder
//     routes: {
//       '*': (context, state, data) {
//         return Scaffold(
//           body: Center(
//             child: Text('404: Page not found'),
//           ),
//         );
//       },
//       '/': (context, state, data) => const Scaffold(
//             body: Center(
//               child: Text("Landing Page"),
//             ),
//           ), // Add landing page
//       '/login': (context, state, data) => LoginScreen(), // Add login screen
//       '/profile/:userId': (context, state, data) {
//         // Add Profile Screen
//         final userId = state.pathParameters['userId']!;
//         return ProfileScreen(userId: userId);
//       },
//       '/welcome': (context, state, data) =>
//           WelcomeScreen() // Add welcome screen
//     },
//   ),
// // ... (guards, etc.) ...
// );

// class ProfileScreen {
//   final String userId;

//   ProfileScreen({required this.userId});
// }