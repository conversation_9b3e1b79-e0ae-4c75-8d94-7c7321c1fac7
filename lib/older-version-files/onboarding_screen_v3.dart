// lib/features/onboarding/screens/onboarding_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/widgets/profile_image_picker.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/widgets/city_selector_widget.dart';
import 'package:watermelon_draft/features/profile/widgets/interests_selector_widget.dart';
import 'package:watermelon_draft/features/profile/widgets/shared_activities_selector_widget.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
import 'package:watermelon_draft/widgets/full_name_input_widget.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  // --- Form keys ---
  final _nameUsernameFormKey = GlobalKey<FormState>();
  // --- Controllers ---
  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _cityController = TextEditingController();
  // --- Other Local State ---
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing values from ViewModel
    final initialStateAsync = ref.read(onboardingViewModelProvider);
    initialStateAsync.whenData((initialState) {
      // initialState here is guaranteed non-null OnboardingState
      _fullNameController.text = initialState.fullName ?? '';
      _usernameController.text = initialState.username ?? '';
      _selectedDate = initialState.birthdate;
      _cityController.text = initialState.city ?? '';
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fullNameController.dispose();
    _usernameController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen for side effects (navigation, errors)
    ref.listen(onboardingViewModelProvider, (previous, next) {
      // Handle navigation on success or errors with SnackBar
      next.whenOrNull(
        // Use whenOrNull if you only care about specific states
        data: (onboardingState) {
          // Potentially navigate automatically if a certain state is reached?
          // Or handle final success/error after viewModel.completeOnboarding() result
        },
        error: (error, stackTrace) {
          if (mounted && error is Failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      );
    });

    // Watch the state to rebuild the UI
    final stateAsync = ref.watch(onboardingViewModelProvider);
    final viewModel = ref.read(onboardingViewModelProvider.notifier);

    // Get current page safely, default to 0 if state is not data yet
    final currentPage =
        stateAsync.maybeWhen(data: (data) => data.currentPage, orElse: () => 0);
    final totalPages = 8; // WELCOME + 6 STEPS + SUMMARY = 8 pages (0-7)

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Onboarding (Step ${currentPage + 1} of $totalPages)',
        ), // Dynamic Title
        bottom: PreferredSize(
          // Use PreferredSize for LinearProgressIndicator in AppBar bottom
          preferredSize: Size(
              double.infinity, 4.0), // Standard height for progress indicator
          child: stateAsync
                  .isLoading // Show indicator only during async loading (optional)
              ? LinearProgressIndicator(value: null) // Indeterminate indicator
              : LinearProgressIndicator(
                  value: (currentPage + 1) / totalPages, // Calculate progress
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor),
                ),
        ),
      ),
      // Use pattern matching (switch) on the AsyncValue state
      body: switch (stateAsync) {
        AsyncData(:final value) => // Data loaded successfully
          PageView(
            controller: _pageController,
            physics: NeverScrollableScrollPhysics(),
            children: [
              // Pass the unwrapped state 'value' (which is OnboardingState)
              // AND the viewModel instance to the helper methods
              _buildWelcomePage(context, viewModel), // Page 0
              _buildNameUsernamePage(context, viewModel, value),
              _buildDobGenderPage(context, viewModel, value),
              _buildProfilePicturePage(context, viewModel, value),
              _buildLocationPage(context, viewModel, value),
              _buildSharedActivitiesPage(context, viewModel, value),
              _buildMyInterestsPage(context, viewModel, value),
              _buildSummaryPage(context, viewModel, value),
            ],
          ),
        AsyncError(:final error) => Center(child: Text('Error: $error')),
        _ => const Center(child: CircularProgressIndicator()),
      },
    );
  }

  // --- Page 1: Welcome Page ---
  Widget _buildWelcomePage(
      BuildContext context, OnboardingViewModel viewModel) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Welcome to Watermelon!', style: TextStyle(fontSize: 24)),
          SizedBox(height: 20),
          Text('Connect with others through shared activities.',
              textAlign: TextAlign.center),
          SizedBox(height: 40),
          ElevatedButton(
            onPressed: () {
              // viewModel.nextPage(); // Call this in ViewModel
              _pageController.nextPage(
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut);
            },
            child: Text('Get Started'),
          ),
        ],
      ),
    );
  }

  // --- Page 2: Full name and Username Page ---
  Widget _buildNameUsernamePage(BuildContext context,
      OnboardingViewModel viewModel, OnboardingState state) {
    return Form(
      key:
          _nameUsernameFormKey, // Use the key defined in _OnboardingScreenState
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('About You',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),

            // --- Full Name Widget ---
            FullNameInputWidget(
              controller:
                  _fullNameController, // Pass controller from state class
              onChanged: viewModel.updateFullName, // Pass update method
              // No need for validator here unless adding extra rules
            ),
            SizedBox(height: 16),

            // --- Username TextFormField (Standard) ---
            TextFormField(
              // Use standard TextFormField
              controller: _usernameController,
              decoration: InputDecoration(
                labelText: 'Username',
                hintText: 'Choose a unique username',
                prefixIcon: Icon(Icons.alternate_email),
                border: OutlineInputBorder(),
                counterText: "",
                // --- Suffix Icon for Loading/Status ---
                suffixIcon: state.isUsernameChecking
                    ? Container(
                        padding: EdgeInsets.all(12.0),
                        child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2)))
                    : (state.username != null &&
                            state.username!.length >= 3 &&
                            !state.username!.contains(' ') &&
                            RegExp(r'^[a-zA-Z0-9_]+$')
                                .hasMatch(state.username!) &&
                            state.usernameError == null)
                        ? Icon(Icons.check_circle,
                            color: Colors
                                .green) // Show check if valid and available
                        : (state.usernameError != null &&
                                !state
                                    .isUsernameChecking) // Check !isUsernameChecking here too
                            ? Icon(Icons.error,
                                color: Colors
                                    .red) // Show error if taken/failed check
                            : null,
                // --- Error Text from ViewModel ---
                errorText:
                    state.usernameError, // Display errors from state here
              ),
              maxLength: 20,
              keyboardType: TextInputType.text,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_]')),
              ],
              autocorrect: false,
              validator: (value) {
                // Basic format validation ONLY
                if (value == null || value.isEmpty) {
                  return 'Please enter a username';
                }
                if (value.length < 3) {
                  return 'Username must be at least 3 characters';
                }
                if (value.length > 20) {
                  // Consistent with maxLength
                  return 'Username cannot exceed 20 characters';
                }
                if (value.contains(' ')) {
                  return 'Username cannot contain spaces';
                }
                if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
                  return 'Only letters, numbers, and underscores allowed';
                }
                // NOTE: Availability error is handled by errorText, not validator
                return null;
              },
              onChanged:
                  viewModel.updateUsername, // Triggers debounced check in VM
            ),

            // --- Display Loading/Status Text Indicator ---
            Padding(
              // Add some padding
              padding: const EdgeInsets.only(top: 8.0),
              child: SizedBox(
                // Use SizedBox to reserve space even when empty
                height: 20, // Adjust height as needed
                child: state.isUsernameChecking
                    ? Row(
                        // Show loading indicator and text
                        children: [
                          SizedBox(
                              width: 16, // Smaller indicator
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2)),
                          SizedBox(width: 8),
                          Text("Checking availability...",
                              style:
                                  TextStyle(fontSize: 12, color: Colors.grey)),
                        ],
                      )
                    : (state.username !=
                                null && // Check basic format validity again for showing success
                            state.username!.length >= 3 &&
                            !state.username!.contains(' ') &&
                            RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(
                                state.username!) && // Check valid characters
                            state.usernameError ==
                                null) // AND no error from VM check
                        ? Row(
                            // Show success message
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 8),
                              Text("Username available",
                                  style: TextStyle(
                                      color: Colors.green, fontSize: 12)),
                            ],
                          )
                        : SizedBox
                            .shrink(), // Show nothing if initial/invalid format/not checking/no error
              ),
            ),

            Spacer(), // Push buttons to bottom
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    // Navigate back to Welcome (Index 0)
                    viewModel.previousPage();
                    _pageController.previousPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut);
                  },
                  child: Text('Back'),
                ),
                ElevatedButton(
                  // Disable Next if username is empty, too short, contains space, checking, or taken
                  onPressed: state
                              .isUsernameChecking || // Disable while checking
                          state.usernameError !=
                              null || // Disable if error (taken/check failed)
                          state.username == null ||
                          state.username!.isEmpty || // Disable if empty
                          state.username!.length < 3 || // Disable if too short
                          state.username!
                              .contains(' ') || // Disable if contains space
                          state.fullName == null ||
                          state.fullName!
                              .isEmpty // Disable if full name is empty
                      ? null
                      : () {
                          // Only need to validate the format locally, availability is in state
                          if (_nameUsernameFormKey.currentState!.validate()) {
                            // Final validation just in case
                            viewModel.nextPage();
                            _pageController.nextPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut);
                          }
                        },
                  child: Text('Next'),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  // --- Page 3: Age and Gender Page ---
  Widget _buildDobGenderPage(BuildContext context,
      OnboardingViewModel viewModel, OnboardingState state) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center, // Center content
              children: [
                Text(
                  'Date of birth. We only use your birthdate to calculate your age and will not display your date of birth.',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),

                // --- Date Picker ---
                GestureDetector(
                  onTap: () async {
                    final DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate ?? DateTime.now(),
                        firstDate: DateTime(1900),
                        lastDate: DateTime.now());
                    if (pickedDate != null && pickedDate != _selectedDate) {
                      setState(() {
                        _selectedDate = pickedDate;
                      }); // Update local display
                      viewModel.updateBirthdate(pickedDate); // Update ViewModel
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'Date of Birth',
                      border: OutlineInputBorder(),
                    ),
                    child: Text(
                      _selectedDate == null
                          ? 'Select Date'
                          : '${_selectedDate!.toLocal()}'.split(' ')[0],
                    ),
                  ),
                ),
                SizedBox(height: 24),

                // --- Gender Selection ---
                Text('Gender', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                Column(
                  // Wrap RadioListTiles in a Column
                  children: <Widget>[
                    RadioListTile<String>(
                      title: const Text('Male'),
                      value: 'Male',
                      groupValue: state
                          .gender, // The currently selected value from ViewModel
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel
                              .updateGender(value); // Update ViewModel state
                        }
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Female'),
                      value: 'Female',
                      groupValue: state.gender,
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel.updateGender(value);
                        }
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Other'),
                      value: 'Other',
                      groupValue: state.gender,
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel.updateGender(value);
                        }
                      },
                    ),
                    // RadioListTile<String>(
                    //   title: const Text('Prefer not to say'),
                    //   value: 'Prefer not to say',
                    //   groupValue: state.gender,
                    //   onChanged: (String? value) {
                    //     if (value != null) {
                    //       viewModel.updateGender(value);
                    //     }
                    //   },
                    // ),
                  ],
                ),
                // Spacer(), // Push buttons to bottom
              ],
            ),
          ),
        ),
        // --- Navigation Buttons ---
        // Spacer(), // Push buttons to bottom
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    // Navigate back to Name/Username (Index 1)
                    viewModel.previousPage();
                    _pageController.previousPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut);
                  },
                  child: Text('Back'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Validate *both* fields on this page
                    bool dateSelected = _selectedDate != null;
                    bool genderSelected =
                        state.gender != null && state.gender!.isNotEmpty;

                    if (!dateSelected) {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text("Please select your birthdate.")));
                      return; // Stop if date is missing
                    }
                    if (!genderSelected) {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text("Please select your gender.")));
                      return; // Stop if gender is missing
                    }

                    // If both are selected, proceed
                    viewModel.nextPage();
                    _pageController.nextPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut);
                  },
                  child: Text('Next'),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  // --- Page 4: Profile Picture Page ---
  Widget _buildProfilePicturePage(BuildContext context,
      OnboardingViewModel viewModel, OnboardingState state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Add a Profile Picture (Required)',
              style: TextStyle(fontSize: 20)),
          SizedBox(height: 8),
          Text(
            "Choose a photo or select a default avatar.",
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          SizedBox(height: 16),
          ProfileImagePicker(
            // Pass initial values from ViewModel state
            initialImageFile: state.profileImage,
            initialDefaultAvatarPath: state.defaultAvatar,
            nameForGeneratedAvatar: state.fullName ?? '?',

            // Connect callbacks to ViewModel methods
            onImageSelected: (image) {
              viewModel.updateProfileImage(image);
            },
            onDefaultAvatarSelected: (path) {
              viewModel.updateDefaultAvatar(path);
            },
            onGeneratedAvatarSelected: (color) {
              viewModel.selectGeneratedAvatar(color); // Updates avatarType
            },
            onGeneratedAvatarColorRegenerated: (newColor) {
              viewModel.updateGeneratedAvatarColor(newColor); // Call VM method
            },

            updateButtonText: 'Next',
            cancelButtonText: 'Back',
            onUpdate: () {
              // NEXT button action
              // Validation: Now check the *ViewModel state*
              if (state.avatarType != null) {
                // Simply check if any type is set
                viewModel.nextPage();
                if (state.currentPage < 5) {
                  _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text(
                        'Please select a profile picture option.'))); // More generic message
              }
            },
            onBack: () {
              // BACK button action
              viewModel.previousPage();
              if (state.currentPage > 0) {
                _pageController.previousPage(
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut);
              }
            },
          ),
          Text(
            // Add reminder text
            "You can change your profile picture later in your profile settings.",
            style: TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center, // Center the text
          ),
        ],
      ),
    );
  }

  // --- Page 5: Location Page ---
  Widget _buildLocationPage(BuildContext context, OnboardingViewModel viewModel,
      OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Set Your Location',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text(
            'Watermelon uses your location to help you find connections and activities nearby.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.left,
          ),
          SizedBox(height: 24),

          // --- Use the Reusable CitySelectorWidget ---
          CitySelectorWidget(
            initialCity: state.city,
            initialLocation: state.location,
            initialCountry: state.country,
            onLocationSet: (location, city, country) {
              // This callback updates the ViewModel's state
              // The CitySelectorWidget handles getting location/city/country
              if (location != null) {
                // Prefer GPS location if available, update all fields
                viewModel.updateLocationFromPoint(location);
              } else if (city != null) {
                // Fallback to city name if GPS failed or wasn't used
                // This method in the VM should also update the country
                viewModel.updateLocationFromAddress(city);
              }
              // If both are null, the ViewModel state remains unchanged for location/city/country
            },
          ),

          Spacer(), // Push buttons to bottom

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage();
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                // --- Validation for Next Button ---
                onPressed: (state.location != null ||
                        (state.city != null && state.city!.isNotEmpty))
                    ? () {
                        // Enabled if location OR city is set in the ViewModel state
                        viewModel.nextPage();
                        _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut);
                      }
                    : null, // Disable button if neither location nor city is set
                child: Text('Next'),
              ),
            ],
          )
        ],
      ),
    );
  }

// --- Page 6: Shared Activities Page ---
  Widget _buildSharedActivitiesPage(BuildContext context,
      OnboardingViewModel viewModel, OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // Align content to start
        children: [
          Text('Select Activities to Share',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text(
            "Select at least one activity you'd like to share with others. You can add up to 5. Drag to reorder.",
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          SizedBox(height: 16),

          // --- Use the Reusable Widget ---
          Expanded(
            // Allow the selector to take available space
            child: SingleChildScrollView(
              // Make the content scrollable if needed
              child: SharedActivitiesSelectorWidget(
                initialActivities: state.sharedActivities ??
                    [], // Pass initial selection from ViewModel
                onActivitiesChanged: (updatedActivities) {
                  // Callback updates the ViewModel
                  viewModel.updateSharedActivities(updatedActivities);
                },
                // maxActivities: 5, // Default is 5, explicitly pass if needed
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            // Add reminder text
            "You can add, remove, or change these activities to share later in your profile settings.",
            style: TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center, // Center the text
          ),

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                // Changed to TextButton for Back
                onPressed: () {
                  viewModel.previousPage(); // Goes back to Location (Index 4)
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                // Disable "Next" if no activities are selected
                onPressed: (state.sharedActivities?.isNotEmpty ?? false)
                    ? () {
                        viewModel
                            .nextPage(); // Goes forward to My Interests (Index 6)
                        _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut);
                      }
                    : null, // Disable if empty
                child: Text('Next'),
              ),
            ],
          )
        ],
      ),
    );
  }

  // --- Page 7: My Interests Page ---
  Widget _buildMyInterestsPage(BuildContext context,
      OnboardingViewModel viewModel, OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('My Interests',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            'Select some interests to help others connect with you. You can always change these later in your profile settings.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          Expanded(
            // Make the selector scrollable if needed
            child: SingleChildScrollView(
              child: InterestsSelectorWidget(
                initialKeywords: state.myInterests ?? [],
                onKeywordsChanged: (updatedKeywords) {
                  viewModel.updateMyInterests(updatedKeywords);
                },
              ),
            ),
          ),
          const SizedBox(height: 24),

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage(); // Go back to Shared Activities
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: const Text('Back'),
              ),
              TextButton(
                onPressed: () {
                  viewModel.nextPage(); // Go next to SummaryPage
                  _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text("Skip for now"),
              ),
              ElevatedButton(
                onPressed: () {
                  viewModel.nextPage(); // Go next to SummaryPage
                  _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Next'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // --- Page 8: Summary Page ---
  Widget _buildSummaryPage(BuildContext context, OnboardingViewModel viewModel,
      OnboardingState state) {
    return Padding(
      // Added Padding
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // Align content left
        children: [
          Text('Summary',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          SizedBox(height: 16),

          // --- Profile Picture Display ---
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: InkWell(
              // Make it clickable
              onTap: () {
                // Navigate back to Profile Picture Page (Index 3)
                _pageController.animateToPage(
                  3,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Container(
                height: 150, // Adjust height as needed
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200], // Background color
                  borderRadius: BorderRadius.circular(12), // Rounded corners
                ),
                child: Stack(
                  // Use Stack to overlay edit icon
                  alignment: Alignment.center,
                  children: [
                    // --- Display Correct Avatar ---
                    if (state.avatarType == 'uploaded' &&
                        state.profileImage != null)
                      ClipRRect(
                        // Clip the image to match container border radius
                        borderRadius: BorderRadius.circular(12),
                        child: Image.file(
                          File(state.profileImage!.path),
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: 150,
                        ),
                      )
                    else if (state.avatarType == 'default' &&
                        state.defaultAvatar != null)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          state.defaultAvatar!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: 150,
                        ),
                      )
                    else if (state.avatarType == 'generated') // Check type
                      generateAvatar(
                        state.fullName ?? '?', // Use name from state
                        radius: 60,
                        color:
                            state.generatedAvatarColor, // Use the STORED color
                      )
                    else // Fallback if somehow state is inconsistent
                      Icon(Icons.person, size: 80, color: Colors.grey),

                    // --- Edit Icon Overlay ---
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: .5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.edit, color: Colors.white, size: 18),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 16),

          // --- Rest of the Summary Items (Scrollable) ---
          Expanded(
            // Make the list scrollable
            child: ListView(
              // Use ListView instead of just Column
              children: [
                ListTile(
                  title: Text('Full Name'),
                  subtitle: Text(state.fullName ?? 'N/A'),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(
                          1, // Go to Name/Username Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text('Username'),
                  subtitle: Text(state.username ?? "N/A"),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(
                          1, // Go to Name/Username Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Date of Birth"),
                  subtitle: Text(state.birthdate != null
                      ? '${state.birthdate!.toLocal()}'.split(' ')[0]
                      : 'N/A'), // Use null check
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(2, // Go to DOB/Gender Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Gender"),
                  subtitle: Text(state.gender ?? "N/A"),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(2, // Go to DOB/Gender Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Profile Picture"),
                  // You might want to display a small avatar preview here too
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(
                          3, // Go to Profile Picture Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Current City"),
                  subtitle: Text(state.city ?? "N/A"),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(4, // Go to Location Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Shared Activities"), // Changed label
                  subtitle: Text(state.sharedActivities?.join(", ") ?? 'N/A'),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(
                          5, // Go to Shared Activities Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
                ListTile(
                  title: Text("My Interests"),
                  subtitle: Text(state.myInterests?.join(", ") ??
                      'None Added'), // Updated placeholder
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      _pageController.animateToPage(
                          6, // Go to My Interests Page
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 20),
          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage(); // Go back to My Interests (Index 6)
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Complete onboarding
                  final result = await viewModel.completeOnboarding();
                  result.fold((l) {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(l.message),
                      backgroundColor: Colors.red,
                    ));
                  }, (r) {
                    // Navigate to home
                    if (mounted) {
                      context.beamToReplacementNamed(
                          '/home'); // Navigate using Beamer
                    }
                  });
                },
                child: Text('Complete Onboarding'),
              ),
            ],
          )
        ],
      ),
    );
  }
}
