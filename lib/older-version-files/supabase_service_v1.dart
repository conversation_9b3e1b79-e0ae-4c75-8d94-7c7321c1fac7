// lib/core/services/supabase_service.dart

import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:uuid/uuid.dart';
import 'dart:io'; // For File
import 'package:image_picker/image_picker.dart'; // For image uploads
import 'package:watermelon_draft/core/constants/constants.dart';
import 'package:watermelon_draft/core/models/friendship.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/models/event.dart';
import 'package:watermelon_draft/core/models/message.dart'; // Import Message model
import 'package:watermelon_draft/core/models/chat_room.dart';
import 'package:watermelon_draft/core/models/chat_rooms_users.dart';
import 'package:watermelon_draft/core/models/attendee.dart';
import 'package:watermelon_draft/core/models/interested_user.dart';
import 'package:watermelon_draft/core/models/wishlist_item.dart';
import 'package:watermelon_draft/core/models/question.dart'; // Import Question
import 'package:watermelon_draft/core/models/answer.dart'; // Import Answer
import 'package:watermelon_draft/core/models/keyword.dart'; // Import Keyword
import 'package:watermelon_draft/core/models/question_keyword.dart';
import 'package:watermelon_draft/core/models/notification.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/models/category.dart';
import 'package:watermelon_draft/core/models/saved_user.dart';

class SupabaseService {
  final supabase.SupabaseClient _supabase = supabase.Supabase.instance.client;

  supabase.SupabaseClient get client => _supabase; // Expose SupabaseClient

  // --------------------------------------------------------------------------
  // Authentication
  // --------------------------------------------------------------------------
  Future<supabase.AuthResponse> signUp({
    required String email,
    required String password,
    // String? emailRedirectTo,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        // emailRedirectTo: emailRedirectTo,
      );
      return response;
    } catch (error) {
      rethrow; // Or handle the error as appropriate for your app
    }
  }

  Future<supabase.AuthResponse> signIn(
      {required String email, required String password}) async {
    try {
      final response = await _supabase.auth
          .signInWithPassword(email: email, password: password);
      return response;
    } catch (error) {
      rethrow;
    }
  }

  supabase.User? get currentUser {
    // Now a getter, returns nullable User directly
    try {
      // Directly return the value from the Supabase client's auth state
      return _supabase.auth.currentUser;
    } catch (e) {
      // Although unlikely for a getter, catch potential rare internal errors
      print("Error accessing Supabase auth currentUser: $e");
      return null;
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (error) {
      rethrow;
    }
  }

  Future<void> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: redirectTo,
      );
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // User
  // --------------------------------------------------------------------------
  Future<void> createUser(User user) async {
    try {
      final response =
          await _supabase.from('Users').insert(user.toJson()).select();
      // No need to check for error
    } catch (error) {
      rethrow; // Let the repository handle the error
    }
  }

  Future<dynamic> getUser(String userId) async {
    try {
      final response = await _supabase
          .from('Users')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();
      if (response == null) {
        return null; // Or throw an exception
      }
      return response; //Return data
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> isUsernameTaken(String username) async {
    try {
      final response = await _supabase
          .from('Users')
          .select('username')
          .eq('username', username)
          .maybeSingle(); // Use maybeSingle

      return response != null; // If data is returned, the username is taken
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateUser(User user) async {
    try {
      await _supabase
          .from('Users')
          .update(user.toJson()) // Convert to JSON
          .eq('user_id', user.userId);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateUserPartial(
      String userId, Map<String, dynamic> updateData) async {
    try {
      final response = await _supabase
          .from('Users')
          .update(updateData)
          .eq('user_id', userId);
    } catch (error) {
      rethrow;
    }
  }

  Future<String?> uploadProfilePicture(String userId, XFile imageFile) async {
    final fileExt = imageFile.name.split('.').last;
    final fileName = '${Uuid().v4()}.$fileExt';
    final filePath = '$userId/$fileName';

    // --- Upload ---
    try {
      await _supabase.storage.from('user-images').upload(
            // Bucket name changed
            filePath,
            File(imageFile.path), // Use File
            fileOptions:
                const supabase.FileOptions(cacheControl: '3600', upsert: false),
          );
    } catch (error) {
      rethrow; // Or return a Left(Failure)
    }

    // --- Get Public URL ---
    final imageUrl = _supabase.storage.from("user-images").getPublicUrl(
        filePath); // Bucket name changed, and correct way to get url
    return imageUrl;
  }

  // --------------------------------------------------------------------------
  // Friendships
  // --------------------------------------------------------------------------
  Future<List<String>> getFriendIds(String userId) async {
    final List<String> friendIds = [];
    try {
      // Find friendships where userA is the given user
      final response1 = await _supabase
          .from('Friendships')
          .select('user_b_id')
          .eq('user_a_id', userId)
          .eq('status_a_to_b', 'accepted');

      final List<dynamic> friendsAsA = response1;
      friendIds.addAll(friendsAsA.map((data) => data['user_b_id'] as String));

      // Find friendships where userB is the given user
      final response2 = await _supabase
          .from('Friendships')
          .select('user_a_id')
          .eq('user_b_id', userId)
          .eq('status_b_to_a', 'accepted');

      final List<dynamic> friendsAsB = response2;
      friendIds.addAll(friendsAsB.map((data) => data['user_a_id'] as String));
    } catch (error) {
      rethrow;
    }
    return friendIds;
  }

  Future<bool> areUsersFriends(String userId1, String userId2) async {
    try {
      // Check if a friendship exists where userA = userId1 and userB = userId2, AND status is 'accepted'
      final response1 = await _supabase
          .from('Friendships')
          .select('friendship_id')
          .eq('user_a_id', userId1)
          .eq('user_b_id', userId2)
          .eq('status_a_to_b', 'accepted')
          .eq('status_b_to_a',
              'accepted') // Ensure both statuses are 'accepted'
          .maybeSingle(); // Use maybeSingle since it should be at most one

      // Check if a friendship exists where userA = userId2 and userB = userId1, AND status is 'accepted'
      final response2 = await _supabase
          .from('Friendships')
          .select('friendship_id')
          .eq('user_a_id', userId2)
          .eq('user_b_id', userId1)
          .eq('status_a_to_b',
              'accepted') // Ensure both statuses are 'accepted'
          .eq('status_b_to_a', 'accepted')
          .maybeSingle(); // Use maybeSingle since it should be at most one
      // Return true if *either* response has data (meaning a friendship exists)
      return response1 != null || response2 != null;
    } catch (error) {
      rethrow;
    }
  }

  Future<Friendship?> getFriendship(String userAId, String userBId) async {
    try {
      final response = await _supabase
          .from('Friendships')
          .select('*')
          .or('and(user_a_id.eq.$userAId,user_b_id.eq.$userBId),and(user_a_id.eq.$userBId,user_b_id.eq.$userAId)')
          .maybeSingle();

      if (response == null) return null;
      return Friendship.fromJson(response); // Convert to Friendship object
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateFriendship(Friendship friendship) async {
    try {
      final response = await _supabase
          .from('Friendships')
          .update(friendship.toJson())
          .eq('friendship_id', friendship.friendshipId);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteFriendship(String friendshipId) async {
    try {
      final response = await _supabase
          .from('Friendships')
          .delete()
          .eq('friendship_id', friendshipId);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> insertFriendship(Friendship friendship) async {
    try {
      final response =
          await _supabase.from('Friendships').insert(friendship.toJson());
    } catch (error) {
      rethrow;
    }
  }

  Future<String?> getFriendshipStatus(
      String currentUserId, String profileUserId) async {
    // Check if currentUserId blocked profileUserId
    try {
      final response1 = await _supabase
          .from('Friendships')
          .select('status_a_to_b')
          .eq('user_a_id', currentUserId)
          .eq('user_b_id', profileUserId)
          .maybeSingle();

      if (response1 != null) {
        final statusAToB = response1['status_a_to_b'] as String?;
        if (statusAToB == 'blocked') {
          return 'blocked'; // current user blocked profile user
        }
      }

      // Check if profileUserId blocked currentUserId
      final response2 = await _supabase
          .from('Friendships')
          .select('status_b_to_a')
          .eq('user_b_id', currentUserId)
          .eq('user_a_id', profileUserId)
          .maybeSingle();

      if (response2 != null) {
        final statusBToA = response2['status_b_to_a'] as String?;
        if (statusBToA == 'blocked') {
          return 'blocked'; //profile user blocked current user.
        }
      }

      // Check friendship status in the (currentUser, profileUser) direction
      final response3 = await _supabase
          .from('Friendships')
          .select('status_a_to_b, status_b_to_a') // Select both statuses
          .eq('user_a_id', currentUserId)
          .eq('user_b_id', profileUserId)
          .maybeSingle(); // Expecting at most one row

      if (response3 != null) {
        final statusAToB = response3['status_a_to_b'] as String?;
        final statusBToA = response3['status_b_to_a'] as String?;

        if (statusAToB == 'accepted' && statusBToA == 'accepted') {
          return 'friends';
        } else if (statusAToB == 'pending') {
          return 'pending'; // Current user sent request
        } else if (statusBToA == 'pending') {
          return 'received'; // Current user received request
        }
      }

      // Check friendship status in the (profileUser, currentUser) direction
      final response4 = await _supabase
          .from('Friendships')
          .select('status_a_to_b, status_b_to_a')
          .eq('user_a_id', profileUserId) // Reverse the IDs
          .eq('user_b_id', currentUserId)
          .maybeSingle();

      if (response4 != null) {
        final statusAToB = response4['status_a_to_b'] as String?;
        final statusBToA = response4['status_b_to_a'] as String?;

        if (statusAToB == 'accepted' && statusBToA == 'accepted') {
          return 'friends';
        } else if (statusAToB == 'pending') {
          return 'received'; // Current user *received* the request (reversed)
        } else if (statusBToA == 'pending') {
          return 'pending'; // Current user *sent* the request (reversed)
        }
      }
      return 'none'; // No friendship relationship
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getUsers(List<String> userIds) async {
    try {
      final response = await _supabase
          .from('Users')
          .select('*')
          .inFilter('user_id', userIds);
      return response;
    } catch (error) {
      rethrow;
    }
  }

// --------------------------------------------------------------------------
// Blocked Users
// --------------------------------------------------------------------------
  Future<List<dynamic>> getBlockedUsers(String currentUserId) async {
    try {
      final List<String> blockedUserIds = [];
      // Find friendships where userA blocked userB
      final response1 = await _supabase
          .from('Friendships')
          .select('user_b_id')
          .eq('user_a_id', currentUserId)
          .eq('status_a_to_b', 'blocked');

      final List<dynamic> blockedAsA = response1;
      blockedUserIds
          .addAll(blockedAsA.map((data) => data['user_b_id'] as String));

      // Find friendships where userB blocked userA
      final response2 = await _supabase
          .from('Friendships')
          .select('user_a_id')
          .eq('user_b_id', currentUserId)
          .eq('status_b_to_a', 'blocked');

      final List<dynamic> blockedAsB = response2;
      blockedUserIds
          .addAll(blockedAsB.map((data) => data['user_a_id'] as String));

      // Now fetch user details for those IDs
      if (blockedUserIds.isNotEmpty) {
        final usersResponse = await _supabase
            .from('Users')
            .select('*')
            .inFilter('user_id',
                blockedUserIds.toSet().toList()); // Remove duplicates and fetch

        return usersResponse;
      } else {
        return []; // No blocked users
      }
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Hidden Users
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getHiddenUsers(String hiderUserId) async {
    try {
      final response = await _supabase
          .from('HiddenUsers')
          .select('hidden_user_id') // Select only the hidden_user_id
          .eq('hider_user_id', hiderUserId);

      final hiddenUserIds = (response as List)
          .map((item) => item['hidden_user_id'] as String)
          .toList();

      // Now, fetch the full User objects for those IDs
      if (hiddenUserIds.isNotEmpty) {
        final userResponse = await _supabase.from('Users').select('*').inFilter(
            'user_id', hiddenUserIds); // Efficiently fetch all users at once

        return userResponse;
      } else {
        return []; // Return an empty list if no saved users
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> isUserHidden(String currentUserId, String userId) async {
    try {
      final response = await _supabase
          .from('HiddenUsers')
          .select('hidden_user_id')
          .eq('hider_user_id', currentUserId)
          .eq('hidden_user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> hideUser(String hiderUserId, String hiddenUserId) async {
    try {
      final response = await _supabase.from('HiddenUsers').insert({
        'hider_user_id': hiderUserId,
        'hidden_user_id': hiddenUserId,
      });
    } catch (error) {
      rethrow;
    }
  }

  Future<void> unhideUser(String currentUserId, String hiddenUserId) async {
    try {
      final response = await _supabase
          .from('HiddenUsers')
          .delete()
          .eq('hider_user_id', currentUserId)
          .eq('hidden_user_id', hiddenUserId);
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Events
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getEventsByLocation({
    required double latitude,
    required double longitude,
    required double radiusMeters,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = _supabase
          .from('Events')
          .select(
              'event_id, event_name, creator_id, category_id, event_date, place_name, place_location, event_description, capacity, image_url, created_at, status, UserVisibility!inner(*)') // Join with UserVisibility
          .eq('status', 'published')
          .eq('UserVisibility.is_blocked_by_me', false) // Use the view
          .eq('UserVisibility.is_hidden_by_me', false)
          .filter(
              'ST_DWithin(place_location, ST_SetSRID(ST_MakePoint($longitude, $latitude), 4326)::geography, $radiusMeters)',
              'eq',
              true);

      // Apply optional date filtering
      if (startDate != null && endDate != null) {
        query = query
            .gte('event_date', startDate.toIso8601String())
            .lte('event_date', endDate.toIso8601String());
      } else if (startDate != null) {
        query = query
            .gte(
                'event_date',
                DateTime(startDate.year, startDate.month, startDate.day)
                    .toIso8601String()) // event_date >= startOfDay
            .lte(
                'event_date',
                DateTime(startDate.year, startDate.month, startDate.day, 23, 59,
                        59)
                    .toIso8601String());
      }

      // Get current user ID
      final currentUserId = _supabase.auth.currentUser!.id;
      // Exclude current user
      query = query.neq('creator_id',
          currentUserId); // Exclude events created by the current user

      final response = await query;
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getEventById(String eventId) async {
    try {
      final response = await _supabase
          .from('Events')
          .select('*')
          .eq('event_id', eventId)
          .maybeSingle();
      if (response == null) {
        return null;
      }
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<int> getAttendeeCount(String eventId) async {
    try {
      final response = await _supabase
          .from('Attendees')
          .select('user_id')
          .eq('event_id', eventId)
          .count(supabase.CountOption.exact);

      return response.count;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getAttendees(String eventId) async {
    try {
      final response = await _supabase
          .from('Attendees')
          .select(
              'user_id') // Select only the user IDs from the Attendees table
          .eq('event_id', eventId);

      final List<dynamic> attendeeData = response; // Directly use the response
      final List<String> userIds =
          attendeeData.map((data) => data['user_id'] as String).toList();

      if (userIds.isNotEmpty) {
        // Fetch user details from the UserVisibility VIEW,
        // which automatically excludes blocked and hidden users.
        final userDetailsResponse = await _supabase
            .from(
                'UserVisibility') // Use UserVisibility view instead of Users table
            .select(
                'user_id, username, full_name, age, gender, city, shared_activities, my_interests, location, profile_picture_url') // Select only necessary fields
            .inFilter(
                'user_id', userIds); // Efficiently fetch all users at once

        return userDetailsResponse; // Directly use result
      } else {
        return []; // No attendees
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> insertAttendee(Attendee attendee) async {
    try {
      await _supabase.from('Attendees').insert(attendee.toJson());
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateAttendee(Attendee attendee) async {
    try {
      await _supabase
          .from('Attendees')
          .update(attendee.toJson())
          .eq('attendee_id', attendee.attendeeId);
    } catch (error) {
      rethrow;
    }
  }

  Future<bool> isUserAttendingEvent(String userId, String eventId) async {
    try {
      final response = await _supabase
          .from('Attendees')
          .select()
          .eq('user_id', userId)
          .eq('event_id', eventId)
          .maybeSingle();

      return response != null; // Returns true/false
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> createEvent(Event event) async {
    try {
      final response =
          await _supabase.from('Events').insert(event.toJson()).select();

      return response; // Directly return
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateEvent(Event event) async {
    try {
      await _supabase
          .from('Events')
          .update(event.toJson()) // Convert to JSON
          .eq('event_id', event.eventId);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteEvent(String eventId) async {
    try {
      await _supabase.from('Events').delete().eq('event_id', eventId);
    } catch (error) {
      rethrow;
    }
  }

  Future<String?> uploadEventImage(String eventId, XFile imageFile) async {
    final fileExt = imageFile.name.split('.').last;
    final fileName = '${Uuid().v4()}.$fileExt';
    final filePath = '$eventId/$fileName';

    // --- Upload ---
    try {
      await _supabase.storage.from('event-images').upload(
            filePath,
            File(imageFile.path),
            fileOptions:
                const supabase.FileOptions(cacheControl: '3600', upsert: false),
          );
    } catch (error) {
      rethrow; // Or return
    }

    // --- Get Public URL ---
    final urlResponse =
        _supabase.storage.from("event-images").getPublicUrl(filePath);

    return urlResponse;
  }

  Future<List<dynamic>> getMyEvents(String userId, {String? status}) async {
    var query = _supabase
        .from('Events')
        .select(
            '*, Attendees!inner(*)') // Select from Events and include Attendees
        .eq('Attendees.user_id', userId);

    if (status != null) {
      query = query.eq('status', status); // Filter by the status column
    }
    try {
      final response = await query;
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getHostedEvents(String userId) async {
    try {
      final response = await _supabase
          .from('Events')
          .select('*')
          .eq('creator_id', userId)
          .neq('status', 'draft'); // Not draft.

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getAttendingEvents(String userId) async {
    try {
      final response = await _supabase.from('Events').select('*').filter(
          'event_id',
          'in',
          '(${_supabase.from("Attendees").select("event_id").eq("user_id", userId)})'); // Corrected
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getDraftEvents(String userId) async {
    try {
      final response = await _supabase
          .from('Events')
          .select('*')
          .eq('creator_id', userId)
          .eq('status', 'draft');
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getPastEvents(String userId) async {
    try {
      final now = DateTime.now();
      // Get events created by the user
      final createdEventsResponse = await _supabase
          .from('Events')
          .select('event_id, event_date')
          .eq('creator_id', userId)
          .lt('event_date', now.toIso8601String()); // 'Less than' now

      // Get events the user is attending
      final attendingEventsResponse = await _supabase
          .from('Attendees')
          .select(
              'Events!inner(event_id, event_date)') // Join with Events table
          .eq('user_id', userId)
          .lt('Events.event_date', now.toIso8601String());

      final createdEvents = createdEventsResponse;
      final attendingEvents =
          attendingEventsResponse.map((e) => e['Events']).toList();

      // Combine and remove duplicates. Since event id is unique, two list should not have overlap
      final List<dynamic> allEvents = [...createdEvents, ...attendingEvents];

      final List<String> allEventIds = [];
      final List<dynamic> allPastEvents = []; // Store the final list of event
      for (var event in allEvents) {
        if (!allEventIds.contains(event['event_id'])) {
          // Check if already added.
          allEventIds.add(event['event_id']); // Add
          allPastEvents.add(event);
        }
      }
      return allPastEvents;
    } catch (error) {
      rethrow;
    }
  }

  Future<int> getUnreadEventNotificationCountForUserEvents(
      String userId) async {
    final response = await _supabase.rpc(
        'get_unread_event_notification_count_for_user',
        params: {'user_id': userId});

    return response;
  }

  Future<List<dynamic>> getMyEventsWithNotificationCounts(String userId) async {
    try {
      final response = await _supabase
          .rpc('get_my_events_with_last_activity', params: {'user_id': userId});
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // FOR getMyEventsWithNotificationCounts METHOD related to .rpc() method
  //
  // INCLUDE THIS SQL FUNCTION IN SUPABASE DATABASE
  // CREATE OR REPLACE FUNCTION get_unread_event_notification_count_for_user(user_id uuid)
  // RETURNS integer AS $$
  // DECLARE
  //     unread_count integer;
  // BEGIN
  //     SELECT COUNT(n.notification_id)
  //     INTO unread_count
  //     FROM UserNotifications n
  //     JOIN Events e ON n.event_id = e.event_id  -- Join with Events table
  //     LEFT JOIN Attendees a ON e.event_id = a.event_id AND a.user_id = get_unread_event_notification_count_for_user.user_id -- join with attendee
  //     WHERE (e.creator_id = get_unread_event_notification_count_for_user.user_id OR a.user_id = get_unread_event_notification_count_for_user.user_id) -- User is creator OR attendee
  //     AND n.is_read = FALSE
  //     AND n.event_id IS NOT NULL; -- Only event notifications

  //     RETURN unread_count;
  // END;
  // $$ LANGUAGE plpgsql;

  Future<void> joinEvent(String userId, String eventId) async {
    try {
      // Check if the user is already attending.  We don't want duplicate entries.
      final existingAttendee = await getAttendee(
          userId, eventId); //getAttendee method from previous response.

      if (existingAttendee == null) {
        //Not attending
        // User is not already attending, insert a new record.
        // final response =
        await _supabase.from('Attendees').insert({
          'attendee_id': const Uuid().v4(), // Generate a new UUID
          'user_id': userId,
          'event_id': eventId,
        });
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> leaveEvent(String userId, String eventId) async {
    try {
      final response = await _supabase
          .from('Attendees')
          .delete()
          .eq('user_id', userId)
          .eq('event_id', eventId);
    } catch (error) {
      rethrow;
    }
  }

  // HELPER METHOD
  Future<Attendee?> getAttendee(String userId, String eventId) async {
    try {
      final response = await _supabase
          .from('Attendees')
          .select('*')
          .eq('user_id', userId)
          .eq('event_id', eventId)
          .maybeSingle();

      if (response == null) return null;
      return Attendee.fromJson(response);
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Wishlists
  // --------------------------------------------------------------------------
  Future<dynamic> createWishlistItem(WishlistItem item) async {
    try {
      final response =
          await _supabase.from('WishlistItems').insert(item.toJson()).select();

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getWishlistItemsByCreator(String creatorId) async {
    try {
      final response = await _supabase
          .from('WishlistItems')
          .select('*')
          .eq('creator_id', creatorId)
          .neq('status', 'draft'); // Exclude draft items

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getWishlistItemsInterestedByUser(String userId) async {
    try {
      final response = await _supabase.from("WishlistItems").select("*").filter(
          'wishlist_item_id',
          'in',
          '(${_supabase.from("InterestedUsers").select("wishlist_item_id").eq("user_id", userId)})'); // Corrected
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getWishlistItemsLetsGoByUser(String userId) async {
    try {
      final response = await _supabase.from("WishlistItems").select("*").filter(
          'wishlist_item_id',
          'in',
          '(${_supabase.from("InterestedUsers").select("wishlist_item_id").eq("user_id", userId).eq("status", "lets_go")})'); // Added status filter
      return response;
    } catch (error) {
      rethrow;
    }
  }

// OLD METHOD WITHOUT GENERAL_DATE CHECK
// Future<List<dynamic>> getPastWishlistItems(String userId) async {
//     try {
//       final now = DateTime.now();
//       // Fetch wishlist items that the user created
//       final createdItemsResponse = await _supabase
//           .from('WishlistItems')
//           .select('wishlist_item_id, event_date, general_date')
//           .eq('creator_id', userId)
//           .lt('event_date', now.toIso8601String()); // Before now

//       // Fetch wishlist items that the user is interested in
//       final interestedItemsResponse = await _supabase
//           .from('InterestedUsers')
//           .select(
//               'WishlistItems!inner(wishlist_item_id, event_date,general_date)') // Join
//           .eq('user_id', userId)
//           .lt('WishlistItems.event_date', now.toIso8601String());

//       final createdItems = createdItemsResponse;
//       final interestedItems =
//           interestedItemsResponse.map((e) => e['WishlistItems']).toList();
//       // Combine and remove duplicates, and the check general_date field
//       //  final createdItems = createdItemsResponse.data ?? [];
//       // final interestedItems = interestedItemsResponse.data?.map((e) => e['WishlistItems']).toList() ?? [];
//       final List<dynamic> allWishList = [...createdItems, ...interestedItems];
//       final List<String> allWishListIds = [];
//       final List<dynamic> allPastWishList = [];
//       for (var wishlistItem in allWishList) {
//         if (!allWishListIds.contains(wishlistItem['wishlist_item_id'])) {
//           if (wishlistItem["event_date"] != null ||
//               wishlistItem["general_date"] != null) {
//             // check date and general_date
//             allWishListIds.add(wishlistItem['wishlist_item_id']);
//             allPastWishList.add(wishlistItem);
//           }
//         }
//       }

//       return allPastWishList;
//     } catch (error) {
//       rethrow;
//     }
//   }

  Future<List<dynamic>> getPastWishlistItems(String userId) async {
    try {
      final now = DateTime.now();
      // Fetch wishlist items that the user created
      final createdItemsResponse = await _supabase
          .from('WishlistItems')
          .select('wishlist_item_id, event_date, general_date')
          .eq('creator_id', userId)
          .lt('event_date', now.toIso8601String()); // Before now

      // Fetch wishlist items that the user is interested in
      final interestedItemsResponse = await _supabase
          .from('InterestedUsers')
          .select(
              'WishlistItems!inner(wishlist_item_id, event_date,general_date)') // Join
          .eq('user_id', userId)
          .lt('WishlistItems.event_date', now.toIso8601String());

      final createdItems = createdItemsResponse;
      final interestedItems =
          interestedItemsResponse.map((e) => e['WishlistItems']).toList();

      // Combine and remove duplicates, and the check general_date field
      final List<dynamic> allWishList = [...createdItems, ...interestedItems];
      final pastWishlistItems = allWishList.where((item) {
        // Check if event_date is in the past
        if (item['event_date'] != null) {
          return true; // Already checked in the query
        }

        // Check general_date
        final generalDate = item['general_date'];
        if (generalDate != null) {
          // Implement logic to check if the general date is in the past.
          // This is more complex and depends on how you parse the general date strings.
          // For the MVP, you might skip this, or provide a very basic check.
          // Example (VERY basic - only checks for "past year"):
          if (generalDate.toLowerCase().contains("past")) return true;
          if (generalDate.toLowerCase().contains("last year")) return true;

          // This needs to be MUCH more robust for a real app.
          return false; // if no past.
        }

        return false; // No date at all
      }).toList();
      final List<dynamic> result = [];
      final List<String> ids = [];
      for (final item in pastWishlistItems) {
        // Remove dublicate
        if (!ids.contains(item["wishlist_item_id"])) {
          ids.add(item["wishlist_item_id"]);
          result.add(item);
        }
      }
      return result;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getWishlistItemById(String itemId) async {
    try {
      final response = await _supabase
          .from('WishlistItems')
          .select('*')
          .eq('wishlist_item_id', itemId)
          .maybeSingle();
      if (response == null) {
        return null;
      }
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateWishlistItem(WishlistItem wishlistItem) async {
    try {
      await _supabase
          .from('WishlistItems')
          .update(wishlistItem.toJson()) // Convert the updated object to JSON
          .eq('wishlist_item_id',
              wishlistItem.wishlistItemId); // Update the specific item
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteWishlistItem(String wishlistItemId) async {
    try {
      await _supabase
          .from('WishlistItems')
          .delete()
          .eq('wishlist_item_id', wishlistItemId);
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // InterestedUser
  // --------------------------------------------------------------------------
  Future<InterestedUser?> checkInterestedUser(
      String userId, String wishlistItemId) async {
    try {
      final response = await _supabase
          .from('InterestedUsers')
          .select('*')
          .eq('user_id', userId)
          .eq('wishlist_item_id', wishlistItemId)
          .maybeSingle(); // Important: Use maybeSingle()

      if (response != null) {
        return InterestedUser.fromJson(response); // Convert to object
      } else {
        return null; // No matching entry
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> insertInterestedUser(InterestedUser interestedUser) async {
    try {
      final response = await _supabase
          .from('InterestedUsers')
          .insert(interestedUser.toJson());
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateInterestedUser(InterestedUser interestedUser) async {
    try {
      final response = await _supabase
          .from('InterestedUsers')
          .update(interestedUser.toJson())
          .eq('interested_user_id',
              interestedUser.interestedUserId); // Use the correct ID
    } catch (error) {
      rethrow;
    }
  }

  Future<void> removeInterestedUser(
      String userId, String wishlistItemId) async {
    try {
      final response = await _supabase
          .from('InterestedUsers')
          .delete()
          .eq('user_id', userId)
          .eq('wishlist_item_id', wishlistItemId);
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getInterestedUsers(String wishlistItemId) async {
    try {
      final response = await _supabase
          .from('InterestedUsers')
          .select('*')
          .eq('wishlist_item_id', wishlistItemId);
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Shared Activities
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getSharedActivities() async {
    try {
      final response = await _supabase
          .from('SharedActivities')
          .select('activity_name')
          .order('sort_order'); // Assuming you have a sort_order

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getSharedActivityById(String activityId) async {
    try {
      final response = await _supabase
          .from('SharedActivities')
          .select('*')
          .eq('activity_id', activityId)
          .maybeSingle();
      if (response == null) {
        return null; // Or throw an exception
      }
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Categories
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getCategories() async {
    try {
      final response =
          await _supabase.from('Categories').select('*').order('sort_order');

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getCategoryById(String categoryId) async {
    try {
      final response = await _supabase
          .from('Categories')
          .select('*')
          .eq('category_id', categoryId)
          .maybeSingle();
      if (response == null) {
        return null;
      }
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Chat
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getChatRoomsForUser(String userId) async {
    try {
      final response = await _supabase
          .from('ChatRooms')
          .select(
              '*, ChatRooms_Users!inner(*)') // Select from ChatRooms and perform an INNER JOIN
          .eq('ChatRooms_Users.user_id',
              userId); // Filter by the user_id in the join table
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getLastMessageForChatRoom(String chatRoomId) async {
    try {
      final response = await _supabase
          .from('Messages')
          .select('*')
          .eq('chat_room_id', chatRoomId)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<int> getUnreadMessageCount(String chatRoomId, String userId) async {
    try {
      final response = await _supabase.rpc('get_unread_count',
          params: {'user_id': userId, 'chat_room_id': chatRoomId});
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> getChatRoomUser(String chatRoomId, String userId) async {
    try {
      final response = await _supabase
          .from("ChatRooms_Users")
          .select("*")
          .eq("chat_room_id", chatRoomId)
          .eq("user_id", userId)
          .maybeSingle();
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<ChatRoom?> getOneOnOneChatRoom(String userId1, String userId2) async {
    try {
      final response = await _supabase
          .from('ChatRooms')
          .select('*, ChatRooms_Users!inner(*)') // Include join table data
          .isFilter('event_id', null) // event_id must be null
          .isFilter('wishlist_item_id', null) // wishlist_item_id must be null
          .maybeSingle();

      if (response == null) return null;

      // Check if both users are in this chat room
      final List<dynamic> chatRoomUsers = response['ChatRooms_Users'] ?? [];
      bool user1Found = false;
      bool user2Found = false;

      for (final user in chatRoomUsers) {
        if (user['user_id'] == userId1) {
          user1Found = true;
        } else if (user['user_id'] == userId2) {
          user2Found = true;
        }
      }

      if (user1Found && user2Found) {
        return ChatRoom.fromJson(response); // Convert and return.
      } else {
        return null; // Not a 1:1 chat room
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> createChatRoom(ChatRoom chatRoom) async {
    try {
      final response =
          await _supabase.from('ChatRooms').insert(chatRoom.toJson()).select();
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateChatRoomUser(ChatRoomsUsers updatedChatRoomUser) async {
    try {
      await _supabase
          .from('ChatRooms_Users')
          .update(updatedChatRoomUser.toJson())
          .eq('chat_rooms_users_id', updatedChatRoomUser.chatRoomsUsersId);
    } catch (error) {
      rethrow;
    }
  }

  Future<dynamic> insertMessage(Message message) async {
    try {
      final response =
          await _supabase.from('Messages').insert(message.toJson()).select();

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<String?> getOtherUserIdInChatRoom(
      String chatRoomId, String currentUserId) async {
    try {
      final response = await _supabase
          .from('ChatRooms_Users')
          .select('user_id')
          .eq('chat_room_id', chatRoomId)
          .neq('user_id',
              currentUserId) // Find the user who is NOT the current user
          .maybeSingle(); //

      if (response == null) return null;
      return response['user_id'] as String?; // Return other user id
    } catch (error) {
      rethrow;
    }
  }

  Future<void> addUserToChatRoom(ChatRoomsUsers chatRoomsUsers) async {
    try {
      await _supabase
          .from('ChatRooms_Users')
          .insert(chatRoomsUsers.toJson()); // Insert
    } catch (error) {
      rethrow;
    }
  }

  Future<void> removeUserFromChatRoom(String chatRoomId, String userId) async {
    try {
      await _supabase
          .from('ChatRooms_Users')
          .delete()
          .eq('chat_room_id', chatRoomId)
          .eq('user_id', userId);
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Notifications
  // --------------------------------------------------------------------------
  Future<void> createNotification(Map<String, dynamic> notification) async {
    try {
      final response =
          await _supabase.from('UserNotifications').insert(notification);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final response = await _supabase
          .from('UserNotifications')
          .update({'is_read': true}).eq('notification_id', notificationId);
    } catch (error) {
      rethrow;
    }
  }

  Future<void> markAllNotificationsAsRead(String userId) async {
    try {
      final response = await _supabase
          .from('UserNotifications')
          .update({'is_read': true}).eq('user_id', userId);
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getUnreadNotifications(String userId) async {
    try {
      final response = await _supabase
          .from("UserNotifications")
          .select("*")
          .eq("user_id", userId)
          .eq("is_read", false);
      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<int> getTotalUnreadNotificationCount(String userId) async {
    try {
      final response = await _supabase
          .from('UserNotifications')
          .select(
              'notification_id') // Select something, even though we only need the count
          .eq('user_id', userId)
          .eq('is_read', false)
          .count(supabase.CountOption.exact);

      return response.count;
    } catch (error) {
      rethrow;
    }
  }

  // IMPLEMENTED IN THE EVENTS SECTION
  // Future<int> getUnreadEventNotificationCountForUserEvents(
  //     String userId) async {}

  Future<List<dynamic>> getNotificationsByUserAndEvent(
      String userId, String eventId) async {
    try {
      final response = await _supabase
          .from('UserNotifications')
          .select('*')
          .eq('user_id', userId)
          .eq('event_id', eventId) // Use event_id here
          .order('created_at', ascending: false);

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getNotificationsForUser(String userId) async {
    try {
      final response = await _supabase
          .from('UserNotifications')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false); // Newest first

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _supabase
          .from('UserNotifications')
          .delete()
          .eq('notification_id', notificationId);
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Keywords
  // --------------------------------------------------------------------------
  Future<List<dynamic>> getAllKeywords() async {
    try {
      final response = await _supabase.from('Keywords').select('*');
      return response;
    } catch (error) {
      rethrow;
    }
  }

  // --------------------------------------------------------------------------
  // Saved User
  // --------------------------------------------------------------------------
  Future<bool> isUserSaved(String currentUserId, String userId) async {
    try {
      final response = await _supabase
          .from('SavedUsers')
          .select('save_id')
          .eq('saver_user_id', currentUserId)
          .eq('saved_user_id', userId)
          .maybeSingle();

      return response != null; // Returns true if found, false otherwise.
    } catch (error) {
      rethrow;
    }
  }

  Future<void> saveUser(SavedUser savedUser) async {
    try {
      final response =
          await _supabase.from('SavedUsers').insert(savedUser.toJson());
    } catch (error) {
      rethrow;
    }
  }

  Future<void> unsaveUser(String saverUserId, String savedUserId) async {
    try {
      final response = await _supabase
          .from('SavedUsers')
          .delete()
          .eq('saver_user_id', saverUserId)
          .eq('saved_user_id', savedUserId);
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> getSavedUsers(String saverUserId) async {
    try {
      final response = await _supabase
          .from('SavedUsers')
          .select('saved_user_id') // Select only the saved_user_id
          .eq('saver_user_id', saverUserId);

      final savedUserIds = (response as List)
          .map((item) => item['saved_user_id'] as String)
          .toList();

      // Now, fetch the full User objects for those IDs
      if (savedUserIds.isNotEmpty) {
        final userResponse = await _supabase.from('Users').select('*').inFilter(
            'user_id', savedUserIds); // Efficiently fetch all users at once

        return userResponse;
      } else {
        return []; // Return an empty list if no saved users
      }
    } catch (error) {
      rethrow;
    }
  }

// --------------------------------------------------------------------------
// Discover (Search)
// --------------------------------------------------------------------------
  Future<List<dynamic>> searchUsersByLocation({
    required double latitude,
    required double longitude,
    required double radiusMeters,
    int? minAge,
    int? maxAge,
    String? gender,
    int limit = 20, // Add limit
    // int offset = 0, // For pagination later
  }) async {
    try {
      var query = _supabase
          .from('UserVisibility') // Use view
          .select(
              'user_id, username, full_name, age, gender, city, shared_activities, my_interests, location, profile_picture_url') // Only select necessary fields
          .eq('is_blocked_by_me', false) // Add filter here
          .eq('is_hidden_by_me', false) // Add filter here
          .filter(
              'ST_DWithin(location, ST_SetSRID(ST_MakePoint($longitude, $latitude), 4326)::geography, $radiusMeters)',
              'eq',
              true); // Use filter for distance

      if (minAge != null && maxAge != null) {
        query = query.gte('age', minAge).lte('age', maxAge);
      }
      if (gender != null && gender != 'Any') {
        query = query.eq('gender', gender);
      }

      // Exclude the current user
      query = query.neq('user_id', _supabase.auth.currentUser!.id);

      final response = await query.limit(limit);

      return response;
    } catch (error) {
      rethrow;
    }
  }

  Future<List<dynamic>> searchUsersByName(String query, {int limit = 7}) async {
    try {
      String searchQuery = query.trim();
      if (searchQuery.isEmpty) return [];

      final currentAuthUserId = _supabase.auth.currentUser?.id;
      if (currentAuthUserId == null) {
        return []; // Should not happen for an authenticated action
      }

      // Start with a base query using the UserVisibility view
      var supabaseQuery = _supabase
          .from('UserVisibility')
          .select(
              'user_id, username, full_name, age, gender, city, country, shared_activities, my_interests, location, profile_picture_url, avatar_type, generated_avatar_color')
          .eq('is_blocked_by_me', false)
          .eq('is_hidden_by_me', false)
          .neq('user_id',
              currentAuthUserId); // Exclude the current user from results

      // Apply name/username filter
      if (searchQuery.startsWith('@')) {
        // Handle @ prefix for username-specific search
        supabaseQuery =
            supabaseQuery.ilike('username', '%${searchQuery.substring(1)}%');
      } else {
        // Search in both username and full_name fields
        supabaseQuery = supabaseQuery
            .or('username.ilike.%$searchQuery%,full_name.ilike.%$searchQuery%');
      }

      // Exclude the current user from results
      if (_supabase.auth.currentUser != null) {
        supabaseQuery =
            supabaseQuery.neq('user_id', _supabase.auth.currentUser!.id);
      }

      // Limit results and execute the query
      final response = await supabaseQuery.limit(limit);
      return response as List<dynamic>;
    } catch (error) {
      print("Error in SupabaseService.searchUsersByName: $error");
      rethrow;
    }
  }

  Future<List<dynamic>> searchUsersByInterests({
    GeoPoint? currentUserLocation,
    String? currentUserCountry,
    int? minAge,
    int? maxAge,
    String? gender,
    String? distanceFilter, // 'local', 'regional', 'national', 'global'
    List<String>? sharedActivities,
    List<String>? keywords,
    int limit = 20, // Make limit configurable
    // int offset = 0, // For pagination later
  }) async {
    try {
      // Start with a base query using the UserVisibility view
      // UserVisibility is a database view that automatically filters out blocked and hidden users
      // This simplifies our query and ensures consistent privacy rules
      var supabaseQuery = _supabase
          .from(
              'UserVisibility') // Use view instead of direct Users table access
          .select(
              'user_id, username, full_name, age, gender, city, country, shared_activities, my_interests, location, profile_picture_url, avatar_type, generated_avatar_color')
          .eq('is_blocked_by_me', false)
          .eq('is_hidden_by_me', false); // Moved .limit() to the end

      // --- Exclude current user from interest search ---
      final currentAuthUserId = _supabase.auth.currentUser?.id;
      if (currentAuthUserId != null) {
        supabaseQuery = supabaseQuery.neq('user_id', currentAuthUserId);
      }

      // --- Apply age filtering if min and max age are provided ---
      if (minAge != null && maxAge != null) {
        // Direct filtering on the age column
        supabaseQuery = supabaseQuery.gte('age', minAge).lte('age', maxAge);
      }

      // --- Apply gender filtering if specified and not set to "Any" ---
      if (gender != null && gender != 'Any') {
        // Handle 'Any' by not applying filter
        supabaseQuery = supabaseQuery.eq('gender', gender);
      }

      // --- Apply distance-based filtering based on the distanceFilter parameter ---
      if (distanceFilter != null) {
        bool useCountryFilter = distanceFilter == 'national';
        double? radiusMeters;

        switch (distanceFilter) {
          case 'local':
            radiusMeters =
                Constants.localRadiusMeters; // Use constant e.g., 40000.0
            break;
          case 'regional':
            radiusMeters =
                Constants.regionalRadiusMeters; // Use constant e.g., 300000.0
            break;
          case 'global':
            // No radius needed for global search
            radiusMeters = null;
            break;
          case 'national':
            // For national search, we'll filter by country instead of radius
            radiusMeters = null;
            break;
        }

        // Apply the appropriate filter based on the distance type
        if (useCountryFilter) {
          // For national searches, filter by country if available
          if (currentUserCountry != null) {
            supabaseQuery = supabaseQuery.eq('country', currentUserCountry);
          } else {
            print(
                "WARN: National distance filter selected but current user country is unknown. Skipping distance filter.");
            // Optionally return empty list or throw? For now, proceed without distance filter.
          }
        } else if (radiusMeters != null) {
          // For local/regional searches, use geographic distance if location available
          if (currentUserLocation != null) {
            // Use the nearby_users RPC function for geographic filtering
            // This is more efficient than doing the calculation in the query
            final response = await _supabase.rpc('nearby_users', params: {
              'lat': currentUserLocation.latitude,
              'lon': currentUserLocation.longitude,
              'radius': radiusMeters,
            });
            return response as List<dynamic>;
            // **NOTE:** This requires creating a `nearby_users` function in Supabase, see example below.
          } else {
            print(
                "WARN: Local/Regional distance filter selected but current user location is unknown. Skipping distance filter.");
          }
        }
      }

      // --- (OLD) Distance Filtering (for Interest-Based Search) ---
      // final currentUserId = _supabase.auth.currentUser!.id;
      // final userResult = await getUser(currentUserId);
      // if (userResult == null) {
      //   throw Exception("User not found"); //Should not happen
      // }
      // final user = User.fromJson(userResult);

      // if (distanceFilter != null) {
      //   // --- Handle null country BEFORE the switch ---
      //   if (distanceFilter == "national") {
      //     if (user.country != null) {
      //       supabaseQuery =
      //           supabaseQuery.eq('country', user.country!); // Now safe!
      //     } else {
      //       return []; // Or throw Exception, as before
      //     }
      //   } else {
      //     // Handle Local, Regional, and Global
      //     double? radiusMeters;
      //     switch (distanceFilter) {
      //       case 'local':
      //         radiusMeters = 50 * 1000; // 50 km
      //         break;
      //       case 'regional':
      //         radiusMeters = 500 * 1000; // 500 km
      //         break;
      //       case 'global':
      //       default: // No need to check, if national, we already return result
      //         radiusMeters = double.infinity; // No distance limit
      //     }

      //     if (radiusMeters != double.infinity) {
      //       if (user.location != null) {
      //         supabaseQuery = supabaseQuery.filter(
      //             'ST_DWithin(location, ST_SetSRID(ST_MakePoint(${user.location!.longitude}, ${user.location!.latitude}), 4326)::geography, $radiusMeters)',
      //             'eq',
      //             true);
      //       } else {
      //         return [];¬¬¬
      //       }
      //     }
      //   }
      // }

      // --- Apply shared activities filtering ---
      if (sharedActivities != null && sharedActivities.isNotEmpty) {
        // supabaseQuery = supabaseQuery.filter('shared_activities', 'cs',
        //     '{${sharedActivities.join(',')}}'); // Corrected

        // For each activity, check if it's contained in the shared_activities array
        for (final activity in sharedActivities) {
          // Use the containedBy operator to check if the activity is in the array
          supabaseQuery =
              supabaseQuery.contains('shared_activities', [activity]);
        }
      }

      // --- Apply interest keywords filtering ---
      if (keywords != null && keywords.isNotEmpty) {
        // Build a list of conditions to check if any keyword is in the interests
        final keywordConditions =
            keywords.map((keyword) => 'my_interests.ilike.%$keyword%').toList();
        // Use OR to match any of the keywords
        supabaseQuery = supabaseQuery.or(keywordConditions.join(','));
      }

      // --- Exclude the current user from results ---
      if (_supabase.auth.currentUser != null) {
        supabaseQuery =
            supabaseQuery.neq('user_id', _supabase.auth.currentUser!.id);
      }

      // --- Limit results and execute the query ---
      final response =
          await supabaseQuery.limit(limit); // Moved to the end here

      print(
          "Executing Search Query: ${supabaseQuery.toString()}"); // Log the built query (optional)

      return response as List<dynamic>;
    } catch (error) {
      print("Error in SupabaseService.searchUsers: $error"); // Log error here
      rethrow; // Let the repository handle the error
    }
  }
}
