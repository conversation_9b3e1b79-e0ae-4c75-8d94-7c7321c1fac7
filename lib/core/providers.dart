// lib/core/providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/models/keyword.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:watermelon_draft/core/services/auth_repository.dart';
import 'package:watermelon_draft/core/services/user_repository.dart';
import 'package:watermelon_draft/core/services/keywords_repository.dart';
import 'package:watermelon_draft/core/services/notification_repository.dart';
import 'package:watermelon_draft/core/services/location_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:watermelon_draft/core/services/event_repository.dart';
import 'package:watermelon_draft/core/services/wishlist_repository.dart';
import 'package:watermelon_draft/core/services/chat_repository.dart';
import 'package:watermelon_draft/core/services/shared_activities_repository.dart';

part 'providers.g.dart';

// --- Supabase Service Provider ---
@riverpod
SupabaseService supabaseService(Ref ref) {
  return SupabaseService();
}

// --- AuthRepository Provider ---
@riverpod
AuthRepository authRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return AuthRepository(supabaseService);
}

// --- UserRepository Provider ---
@riverpod
UserRepository userRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return UserRepository(supabaseService);
}

// --- LocationService Provider ---
@riverpod
LocationService locationService(Ref ref) {
  return LocationService();
}

// --- SharedPreferences Provider --- (Asynchronously loaded)
@Riverpod(keepAlive: true)
Future<SharedPreferences> sharedPreferences(Ref ref) async {
  return await SharedPreferences.getInstance();
}

// --- EventRepository Provider ---
@riverpod
EventRepository eventRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return EventRepository(supabaseService);
}

// --- WishlistRepository Provider ---
@riverpod
WishlistRepository wishlistRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return WishlistRepository(supabaseService);
}

// --- ChatRepository Provider ---
@riverpod
ChatRepository chatRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return ChatRepository(supabaseService);
}

// --- NotificationRepository Provider ---
@riverpod
NotificationRepository notificationRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return NotificationRepository(supabaseService);
}

// --- SharedActivitiesRepository Provider ---
@riverpod
SharedActivitiesRepository sharedActivitiesRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return SharedActivitiesRepository(supabaseService);
}

// --- KeywordsRepository Provider ---
@riverpod
KeywordsRepository keywordsRepository(Ref ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return KeywordsRepository(supabaseService);
}

// --- Shared Activities Provider ---
// Provider to fetch all shared activities (cached)
@Riverpod(keepAlive: true) // Keep the list cached
Future<List<SharedActivity>> sharedActivities(Ref ref) async {
  final repository = ref.watch(sharedActivitiesRepositoryProvider);
  final result = await repository.getSharedActivities();
  // Throw error on failure to put provider in AsyncError state
  return result.fold(
    (failure) => throw failure,
    (activities) => activities,
  );
}

// --- Keyword Provider ---
@Riverpod(keepAlive: true) // Keep keywords cached
Future<List<Keyword>> keywords(Ref ref) async {
  final repository =
      ref.watch(keywordsRepositoryProvider);
  final result = await repository.getAllKeywords();
  return result.fold(
    (l) => throw l,
    (r) => r, // Return list of keywords
  );
}
