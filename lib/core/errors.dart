// lib/core/errors.dart

// Custom error base class
abstract class Failure {
  final String message;
  Failure(this.message);

  @override
  String toString() => message;
}

// --- Specific Failures ---
class AuthFailure extends Failure {
  AuthFailure(super.message);
}

class DatabaseFailure extends Failure {
  DatabaseFailure(super.message);
}

class LocationFailure extends Failure {
  LocationFailure(super.message);
}

class ValidationError extends Failure {
  ValidationError(super.message);
}

class BlockedUserFailure extends Failure {
  BlockedUserFailure(super.message);
}

class PostgresqlFailure extends Failure {
  final String code; // Store the PostgREST error code
  PostgresqlFailure(this.code, super.message);

  @override
  String toString() =>
      'PostgreSQL Error ($code): $message'; // Provide a more informative string
}

class AppLogicFailure extends Failure {
  AppLogicFailure(super.message);
}

// --- Location Specific Failures ---
class LocationServiceDisabledFailure extends Failure {
  LocationServiceDisabledFailure() : super('Location services are disabled.');
}

class LocationPermissionDeniedFailure extends Failure {
  final bool isPermanentlyDenied; // Store if denial is permanent
  LocationPermissionDeniedFailure({this.isPermanentlyDenied = false})
      : super(isPermanentlyDenied
            ? 'Location permission permanently denied. Please enable in settings.'
            : 'Location permission denied.');
}

class GeocodingFailure extends Failure {
  GeocodingFailure(String message) : super('Geocoding failed: $message');
}

class LocationTimeoutFailure extends Failure {
  LocationTimeoutFailure() : super('Getting location timed out.');
}
