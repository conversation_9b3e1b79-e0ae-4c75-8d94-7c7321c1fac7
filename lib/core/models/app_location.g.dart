// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_location.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppLocation _$AppLocationFromJson(Map<String, dynamic> json) => _AppLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      source: $enumDecodeNullable(_$AppLocationSourceEnumMap, json['source']) ??
          AppLocationSource.unknown,
    );

Map<String, dynamic> _$AppLocationToJson(_AppLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'accuracy': instance.accuracy,
      'timestamp': instance.timestamp?.toIso8601String(),
      'source': _$AppLocationSourceEnumMap[instance.source]!,
    };

const _$AppLocationSourceEnumMap = {
  AppLocationSource.unknown: 'unknown',
  AppLocationSource.gps: 'gps',
  AppLocationSource.geocoding: 'geocoding',
  AppLocationSource.userInput: 'userInput',
  AppLocationSource.mapbox: 'mapbox',
  AppLocationSource.osm: 'osm',
};
