import 'package:freezed_annotation/freezed_annotation.dart';

part 'friendship.freezed.dart';
part 'friendship.g.dart';

@freezed
abstract class Friendship with _$Friendship {
  const factory Friendship({
    required String friendshipId,
    required String userAId,
    required String userBId,
    required String statusAtoB, // 'pending', 'accepted', 'blocked', 'none'
    required String statusBtoA, // 'pending', 'accepted', 'blocked', 'none'
    DateTime? requestSentAt,
    required DateTime createdAt,
  }) = _Friendship;

  factory Friendship.fromJson(Map<String, dynamic> json) =>
      _$FriendshipFromJson(json);
}
