// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_User _$UserFromJson(Map<String, dynamic> json) => _User(
      userId: json['user_id'] as String,
      email: json['email'] as String,
      username: json['username'] as String?,
      fullName: json['full_name'] as String?,
      age: (json['age'] as num?)?.toInt(),
      gender: json['gender'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      sharedActivities: (json['shared_activities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      myInterests: (json['my_interests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      location: AppLocationConverters.appLocationFromJson(
          json['location'] as Map<String, dynamic>?),
      profilePictureUrl: json['profile_picture_url'] as String?,
      avatarType: json['avatar_type'] as String?,
      generatedAvatarColor: json['generated_avatar_color'] as String?,
      onboardingComplete: json['onboarding_complete'] as bool? ?? false,
      discoverable: json['discoverable'] as bool? ?? true,
      locationUpdatedAt: json['location_updated_at'] == null
          ? null
          : DateTime.parse(json['location_updated_at'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$UserToJson(_User instance) => <String, dynamic>{
      'user_id': instance.userId,
      'email': instance.email,
      'username': instance.username,
      'full_name': instance.fullName,
      'age': instance.age,
      'gender': instance.gender,
      'city': instance.city,
      'country': instance.country,
      'shared_activities': instance.sharedActivities,
      'my_interests': instance.myInterests,
      'location': AppLocationConverters.appLocationToJson(instance.location),
      'profile_picture_url': instance.profilePictureUrl,
      'avatar_type': instance.avatarType,
      'generated_avatar_color': instance.generatedAvatarColor,
      'onboarding_complete': instance.onboardingComplete,
      'discoverable': instance.discoverable,
      'location_updated_at': instance.locationUpdatedAt?.toIso8601String(),
      if (instance.createdAt?.toIso8601String() case final value?)
        'created_at': value,
    };
