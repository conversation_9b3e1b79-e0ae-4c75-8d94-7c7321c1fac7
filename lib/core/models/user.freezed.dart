// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$User {
  @JsonKey(name: 'user_id')
  String get userId;
  String get email; // Email is known at creation
  String? get username; // Nullable until set in onboarding
  @JsonKey(name: 'full_name')
  String? get fullName;
  int? get age;
  String? get gender;
  String? get city;
  String? get country; // Field non-nullable, defaults to [] if key missing
  @JsonKey(name: 'shared_activities', defaultValue: [])
  List<String> get sharedActivities; // Use activity IDs
// Field non-nullable, defaults to []
  @JsonKey(name: 'my_interests', defaultValue: [])
  List<String>? get myInterests;
  @JsonKey(name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
  GeoPoint? get location;
  @JsonKey(name: 'profile_picture_url')
  String? get profilePictureUrl;
  @JsonKey(name: 'avatar_type')
  String? get avatarType;
  @JsonKey(name: 'generated_avatar_color')
  String? get generatedAvatarColor; // Hex string
  @JsonKey(name: 'onboarding_complete', defaultValue: false)
  bool get onboardingComplete;
  @JsonKey(name: 'discoverable', defaultValue: true)
  bool get discoverable;
  @JsonKey(name: 'location_updated_at')
  DateTime? get locationUpdatedAt; // Omit if null when serializing
  @JsonKey(name: 'created_at', includeIfNull: false)
  DateTime? get createdAt;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserCopyWith<User> get copyWith =>
      _$UserCopyWithImpl<User>(this as User, _$identity);

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is User &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country) &&
            const DeepCollectionEquality()
                .equals(other.sharedActivities, sharedActivities) &&
            const DeepCollectionEquality()
                .equals(other.myInterests, myInterests) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.avatarType, avatarType) ||
                other.avatarType == avatarType) &&
            (identical(other.generatedAvatarColor, generatedAvatarColor) ||
                other.generatedAvatarColor == generatedAvatarColor) &&
            (identical(other.onboardingComplete, onboardingComplete) ||
                other.onboardingComplete == onboardingComplete) &&
            (identical(other.discoverable, discoverable) ||
                other.discoverable == discoverable) &&
            (identical(other.locationUpdatedAt, locationUpdatedAt) ||
                other.locationUpdatedAt == locationUpdatedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      email,
      username,
      fullName,
      age,
      gender,
      city,
      country,
      const DeepCollectionEquality().hash(sharedActivities),
      const DeepCollectionEquality().hash(myInterests),
      location,
      profilePictureUrl,
      avatarType,
      generatedAvatarColor,
      onboardingComplete,
      discoverable,
      locationUpdatedAt,
      createdAt);

  @override
  String toString() {
    return 'User(userId: $userId, email: $email, username: $username, fullName: $fullName, age: $age, gender: $gender, city: $city, country: $country, sharedActivities: $sharedActivities, myInterests: $myInterests, location: $location, profilePictureUrl: $profilePictureUrl, avatarType: $avatarType, generatedAvatarColor: $generatedAvatarColor, onboardingComplete: $onboardingComplete, discoverable: $discoverable, locationUpdatedAt: $locationUpdatedAt, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) _then) =
      _$UserCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') String userId,
      String email,
      String? username,
      @JsonKey(name: 'full_name') String? fullName,
      int? age,
      String? gender,
      String? city,
      String? country,
      @JsonKey(name: 'shared_activities', defaultValue: [])
      List<String> sharedActivities,
      @JsonKey(name: 'my_interests', defaultValue: [])
      List<String>? myInterests,
      @JsonKey(
          name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
      GeoPoint? location,
      @JsonKey(name: 'profile_picture_url') String? profilePictureUrl,
      @JsonKey(name: 'avatar_type') String? avatarType,
      @JsonKey(name: 'generated_avatar_color') String? generatedAvatarColor,
      @JsonKey(name: 'onboarding_complete', defaultValue: false)
      bool onboardingComplete,
      @JsonKey(name: 'discoverable', defaultValue: true) bool discoverable,
      @JsonKey(name: 'location_updated_at') DateTime? locationUpdatedAt,
      @JsonKey(name: 'created_at', includeIfNull: false) DateTime? createdAt});
}

/// @nodoc
class _$UserCopyWithImpl<$Res> implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._self, this._then);

  final User _self;
  final $Res Function(User) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? email = null,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? age = freezed,
    Object? gender = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? sharedActivities = null,
    Object? myInterests = freezed,
    Object? location = freezed,
    Object? profilePictureUrl = freezed,
    Object? avatarType = freezed,
    Object? generatedAvatarColor = freezed,
    Object? onboardingComplete = null,
    Object? discoverable = null,
    Object? locationUpdatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_self.copyWith(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedActivities: null == sharedActivities
          ? _self.sharedActivities
          : sharedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      myInterests: freezed == myInterests
          ? _self.myInterests
          : myInterests // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _self.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarType: freezed == avatarType
          ? _self.avatarType
          : avatarType // ignore: cast_nullable_to_non_nullable
              as String?,
      generatedAvatarColor: freezed == generatedAvatarColor
          ? _self.generatedAvatarColor
          : generatedAvatarColor // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingComplete: null == onboardingComplete
          ? _self.onboardingComplete
          : onboardingComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      discoverable: null == discoverable
          ? _self.discoverable
          : discoverable // ignore: cast_nullable_to_non_nullable
              as bool,
      locationUpdatedAt: freezed == locationUpdatedAt
          ? _self.locationUpdatedAt
          : locationUpdatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _User implements User {
  const _User(
      {@JsonKey(name: 'user_id') required this.userId,
      required this.email,
      required this.username,
      @JsonKey(name: 'full_name') this.fullName,
      this.age,
      this.gender,
      this.city,
      this.country,
      @JsonKey(name: 'shared_activities', defaultValue: [])
      required final List<String> sharedActivities,
      @JsonKey(name: 'my_interests', defaultValue: [])
      final List<String>? myInterests,
      @JsonKey(
          name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
      this.location,
      @JsonKey(name: 'profile_picture_url') this.profilePictureUrl,
      @JsonKey(name: 'avatar_type') this.avatarType,
      @JsonKey(name: 'generated_avatar_color') this.generatedAvatarColor,
      @JsonKey(name: 'onboarding_complete', defaultValue: false)
      required this.onboardingComplete,
      @JsonKey(name: 'discoverable', defaultValue: true)
      required this.discoverable,
      @JsonKey(name: 'location_updated_at') this.locationUpdatedAt,
      @JsonKey(name: 'created_at', includeIfNull: false) this.createdAt})
      : _sharedActivities = sharedActivities,
        _myInterests = myInterests;
  factory _User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  @override
  @JsonKey(name: 'user_id')
  final String userId;
  @override
  final String email;
// Email is known at creation
  @override
  final String? username;
// Nullable until set in onboarding
  @override
  @JsonKey(name: 'full_name')
  final String? fullName;
  @override
  final int? age;
  @override
  final String? gender;
  @override
  final String? city;
  @override
  final String? country;
// Field non-nullable, defaults to [] if key missing
  final List<String> _sharedActivities;
// Field non-nullable, defaults to [] if key missing
  @override
  @JsonKey(name: 'shared_activities', defaultValue: [])
  List<String> get sharedActivities {
    if (_sharedActivities is EqualUnmodifiableListView)
      return _sharedActivities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sharedActivities);
  }

// Use activity IDs
// Field non-nullable, defaults to []
  final List<String>? _myInterests;
// Use activity IDs
// Field non-nullable, defaults to []
  @override
  @JsonKey(name: 'my_interests', defaultValue: [])
  List<String>? get myInterests {
    final value = _myInterests;
    if (value == null) return null;
    if (_myInterests is EqualUnmodifiableListView) return _myInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
  final GeoPoint? location;
  @override
  @JsonKey(name: 'profile_picture_url')
  final String? profilePictureUrl;
  @override
  @JsonKey(name: 'avatar_type')
  final String? avatarType;
  @override
  @JsonKey(name: 'generated_avatar_color')
  final String? generatedAvatarColor;
// Hex string
  @override
  @JsonKey(name: 'onboarding_complete', defaultValue: false)
  final bool onboardingComplete;
  @override
  @JsonKey(name: 'discoverable', defaultValue: true)
  final bool discoverable;
  @override
  @JsonKey(name: 'location_updated_at')
  final DateTime? locationUpdatedAt;
// Omit if null when serializing
  @override
  @JsonKey(name: 'created_at', includeIfNull: false)
  final DateTime? createdAt;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserCopyWith<_User> get copyWith =>
      __$UserCopyWithImpl<_User>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _User &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country) &&
            const DeepCollectionEquality()
                .equals(other._sharedActivities, _sharedActivities) &&
            const DeepCollectionEquality()
                .equals(other._myInterests, _myInterests) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.avatarType, avatarType) ||
                other.avatarType == avatarType) &&
            (identical(other.generatedAvatarColor, generatedAvatarColor) ||
                other.generatedAvatarColor == generatedAvatarColor) &&
            (identical(other.onboardingComplete, onboardingComplete) ||
                other.onboardingComplete == onboardingComplete) &&
            (identical(other.discoverable, discoverable) ||
                other.discoverable == discoverable) &&
            (identical(other.locationUpdatedAt, locationUpdatedAt) ||
                other.locationUpdatedAt == locationUpdatedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      email,
      username,
      fullName,
      age,
      gender,
      city,
      country,
      const DeepCollectionEquality().hash(_sharedActivities),
      const DeepCollectionEquality().hash(_myInterests),
      location,
      profilePictureUrl,
      avatarType,
      generatedAvatarColor,
      onboardingComplete,
      discoverable,
      locationUpdatedAt,
      createdAt);

  @override
  String toString() {
    return 'User(userId: $userId, email: $email, username: $username, fullName: $fullName, age: $age, gender: $gender, city: $city, country: $country, sharedActivities: $sharedActivities, myInterests: $myInterests, location: $location, profilePictureUrl: $profilePictureUrl, avatarType: $avatarType, generatedAvatarColor: $generatedAvatarColor, onboardingComplete: $onboardingComplete, discoverable: $discoverable, locationUpdatedAt: $locationUpdatedAt, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$UserCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$UserCopyWith(_User value, $Res Function(_User) _then) =
      __$UserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') String userId,
      String email,
      String? username,
      @JsonKey(name: 'full_name') String? fullName,
      int? age,
      String? gender,
      String? city,
      String? country,
      @JsonKey(name: 'shared_activities', defaultValue: [])
      List<String> sharedActivities,
      @JsonKey(name: 'my_interests', defaultValue: [])
      List<String>? myInterests,
      @JsonKey(
          name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
      GeoPoint? location,
      @JsonKey(name: 'profile_picture_url') String? profilePictureUrl,
      @JsonKey(name: 'avatar_type') String? avatarType,
      @JsonKey(name: 'generated_avatar_color') String? generatedAvatarColor,
      @JsonKey(name: 'onboarding_complete', defaultValue: false)
      bool onboardingComplete,
      @JsonKey(name: 'discoverable', defaultValue: true) bool discoverable,
      @JsonKey(name: 'location_updated_at') DateTime? locationUpdatedAt,
      @JsonKey(name: 'created_at', includeIfNull: false) DateTime? createdAt});
}

/// @nodoc
class __$UserCopyWithImpl<$Res> implements _$UserCopyWith<$Res> {
  __$UserCopyWithImpl(this._self, this._then);

  final _User _self;
  final $Res Function(_User) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? email = null,
    Object? username = freezed,
    Object? fullName = freezed,
    Object? age = freezed,
    Object? gender = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? sharedActivities = null,
    Object? myInterests = freezed,
    Object? location = freezed,
    Object? profilePictureUrl = freezed,
    Object? avatarType = freezed,
    Object? generatedAvatarColor = freezed,
    Object? onboardingComplete = null,
    Object? discoverable = null,
    Object? locationUpdatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_User(
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedActivities: null == sharedActivities
          ? _self._sharedActivities
          : sharedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      myInterests: freezed == myInterests
          ? _self._myInterests
          : myInterests // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _self.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarType: freezed == avatarType
          ? _self.avatarType
          : avatarType // ignore: cast_nullable_to_non_nullable
              as String?,
      generatedAvatarColor: freezed == generatedAvatarColor
          ? _self.generatedAvatarColor
          : generatedAvatarColor // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingComplete: null == onboardingComplete
          ? _self.onboardingComplete
          : onboardingComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      discoverable: null == discoverable
          ? _self.discoverable
          : discoverable // ignore: cast_nullable_to_non_nullable
              as bool,
      locationUpdatedAt: freezed == locationUpdatedAt
          ? _self.locationUpdatedAt
          : locationUpdatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
