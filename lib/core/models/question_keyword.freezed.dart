// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'question_keyword.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuestionKeyword {
  String get questionKeywordId; // Primary Key
  String get questionId;
  String get keywordId;

  /// Create a copy of QuestionKeyword
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuestionKeywordCopyWith<QuestionKeyword> get copyWith =>
      _$QuestionKeywordCopyWithImpl<QuestionKeyword>(
          this as QuestionKeyword, _$identity);

  /// Serializes this QuestionKeyword to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuestionKeyword &&
            (identical(other.questionKeywordId, questionKeywordId) ||
                other.questionKeywordId == questionKeywordId) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            (identical(other.keywordId, keywordId) ||
                other.keywordId == keywordId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, questionKeywordId, questionId, keywordId);

  @override
  String toString() {
    return 'QuestionKeyword(questionKeywordId: $questionKeywordId, questionId: $questionId, keywordId: $keywordId)';
  }
}

/// @nodoc
abstract mixin class $QuestionKeywordCopyWith<$Res> {
  factory $QuestionKeywordCopyWith(
          QuestionKeyword value, $Res Function(QuestionKeyword) _then) =
      _$QuestionKeywordCopyWithImpl;
  @useResult
  $Res call({String questionKeywordId, String questionId, String keywordId});
}

/// @nodoc
class _$QuestionKeywordCopyWithImpl<$Res>
    implements $QuestionKeywordCopyWith<$Res> {
  _$QuestionKeywordCopyWithImpl(this._self, this._then);

  final QuestionKeyword _self;
  final $Res Function(QuestionKeyword) _then;

  /// Create a copy of QuestionKeyword
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? questionKeywordId = null,
    Object? questionId = null,
    Object? keywordId = null,
  }) {
    return _then(_self.copyWith(
      questionKeywordId: null == questionKeywordId
          ? _self.questionKeywordId
          : questionKeywordId // ignore: cast_nullable_to_non_nullable
              as String,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      keywordId: null == keywordId
          ? _self.keywordId
          : keywordId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuestionKeyword implements QuestionKeyword {
  const _QuestionKeyword(
      {required this.questionKeywordId,
      required this.questionId,
      required this.keywordId});
  factory _QuestionKeyword.fromJson(Map<String, dynamic> json) =>
      _$QuestionKeywordFromJson(json);

  @override
  final String questionKeywordId;
// Primary Key
  @override
  final String questionId;
  @override
  final String keywordId;

  /// Create a copy of QuestionKeyword
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuestionKeywordCopyWith<_QuestionKeyword> get copyWith =>
      __$QuestionKeywordCopyWithImpl<_QuestionKeyword>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuestionKeywordToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuestionKeyword &&
            (identical(other.questionKeywordId, questionKeywordId) ||
                other.questionKeywordId == questionKeywordId) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            (identical(other.keywordId, keywordId) ||
                other.keywordId == keywordId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, questionKeywordId, questionId, keywordId);

  @override
  String toString() {
    return 'QuestionKeyword(questionKeywordId: $questionKeywordId, questionId: $questionId, keywordId: $keywordId)';
  }
}

/// @nodoc
abstract mixin class _$QuestionKeywordCopyWith<$Res>
    implements $QuestionKeywordCopyWith<$Res> {
  factory _$QuestionKeywordCopyWith(
          _QuestionKeyword value, $Res Function(_QuestionKeyword) _then) =
      __$QuestionKeywordCopyWithImpl;
  @override
  @useResult
  $Res call({String questionKeywordId, String questionId, String keywordId});
}

/// @nodoc
class __$QuestionKeywordCopyWithImpl<$Res>
    implements _$QuestionKeywordCopyWith<$Res> {
  __$QuestionKeywordCopyWithImpl(this._self, this._then);

  final _QuestionKeyword _self;
  final $Res Function(_QuestionKeyword) _then;

  /// Create a copy of QuestionKeyword
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? questionKeywordId = null,
    Object? questionId = null,
    Object? keywordId = null,
  }) {
    return _then(_QuestionKeyword(
      questionKeywordId: null == questionKeywordId
          ? _self.questionKeywordId
          : questionKeywordId // ignore: cast_nullable_to_non_nullable
              as String,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      keywordId: null == keywordId
          ? _self.keywordId
          : keywordId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
