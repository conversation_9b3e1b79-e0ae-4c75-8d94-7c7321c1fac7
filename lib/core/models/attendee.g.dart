// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Attendee _$Attendee<PERSON>rom<PERSON>son(Map<String, dynamic> json) => _Attendee(
      attendeeId: json['attendeeId'] as String,
      userId: json['userId'] as String,
      eventId: json['eventId'] as String,
      joinCount: (json['joinCount'] as num?)?.toInt() ?? 1,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AttendeeToJson(_Attendee instance) => <String, dynamic>{
      'attendeeId': instance.attendeeId,
      'userId': instance.userId,
      'eventId': instance.eventId,
      'joinCount': instance.joinCount,
      'createdAt': instance.createdAt?.toIso8601String(),
    };
