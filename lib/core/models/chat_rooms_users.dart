import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_rooms_users.freezed.dart';
part 'chat_rooms_users.g.dart';

@freezed
abstract class ChatRoomsUsers with _$ChatRoomsUsers {
  const factory ChatRoomsUsers({
    required String chatRoomsUsersId, // Primary Key
    required String chatRoomId,
    required String userId,
    DateTime? lastReadAt, // Add this
  }) = _ChatRoomsUsers;

  factory ChatRoomsUsers.fromJson(Map<String, dynamic> json) =>
      _$ChatRoomsUsersFromJson(json);
}
