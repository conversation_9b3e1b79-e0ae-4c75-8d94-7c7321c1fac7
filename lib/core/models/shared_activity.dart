// lib/core/models/shared_activity.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shared_activity.freezed.dart';
part 'shared_activity.g.dart';

@freezed
abstract class SharedActivity with _$SharedActivity {
  const factory SharedActivity({
    @J<PERSON><PERSON>ey(name: 'activity_id') required String activityId,
    @JsonKey(name: 'activity_name') required String activityName,
    @JsonKey(name: 'sort_order') int? sortOrder,
    // 'category' matches DB column name, so @JsonKey is optional if no other config needed
    String? category,
   @<PERSON><PERSON><PERSON>ey(name: 'created_at') DateTime? createdAt,
  }) = _SharedActivity;

  factory SharedActivity.fromJson(Map<String, dynamic> json) =>
      _$SharedActivityFromJson(json);
}
