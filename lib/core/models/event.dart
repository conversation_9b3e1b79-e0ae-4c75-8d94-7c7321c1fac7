import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:watermelon_draft/core/models/app_location.dart';
import 'package:watermelon_draft/core/utils/json_converters.dart';

part 'event.freezed.dart';
part 'event.g.dart';

@freezed
abstract class Event with _$Event {
  const factory Event({
    required String eventId,
    required String eventName,
    required String creatorId,
    String? categoryId,
    DateTime? eventDate, // Combined date and time
    String? placeName,

    /// Location of the event using AppLocation model
    @JsonKey(
      name: 'place_location',
      fromJson: appLocationFromJson,
      toJson: appLocationToJson,
    )
    AppLocation? placeLocation,
    String? eventDescription,
    int? capacity,
    String? imageUrl,
    String? chatRoomId,
    required DateTime createdAt,
    @Default('draft') String status, //draft/published
  }) = _Event;

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);
}
