import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'event.freezed.dart';
part 'event.g.dart';

@freezed
abstract class Event with _$Event {
  const factory Event({
    required String eventId,
    required String eventName,
    required String creatorId,
    String? categoryId,
    DateTime? eventDate, // Combined date and time
    String? placeName,
    @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
    GeoPoint? placeLocation, // Corrected
    String? eventDescription,
    int? capacity,
    String? imageUrl,
    String? chatRoomId,
    required DateTime createdAt,
    @Default('draft') String status, //draft/published
  }) = _Event;

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);
}

// Helper functions for GeoPoint conversion (outside the class)
GeoPoint? _geoPointFromJson(Map<String, dynamic>? json) {
  if (json == null) {
    return null;
  }
  return GeoPoint(
    latitude: (json['coordinates'][1] as num).toDouble(),
    longitude: (json['coordinates'][0] as num).toDouble(),
  );
}

Map<String, dynamic>? _geoPointToJson(GeoPoint? geoPoint) {
  if (geoPoint == null) return null;
  return {
    'type': 'Point', // This is the GeoJSON format
    'coordinates': [
      geoPoint.longitude,
      geoPoint.latitude
    ], // Note: Longitude first!
    'crs': {
      'type': 'name',
      'properties': {'name': 'urn:ogc:def:crs:EPSG::4326'}
    }, // Specify SRID 4326
  };
}
