import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_keyword.freezed.dart';
part 'question_keyword.g.dart';

@freezed
abstract class QuestionKeyword with _$QuestionKeyword {
  const factory QuestionKeyword({
    required String questionKeywordId, // Primary Key
    required String questionId,
    required String keywordId,
  }) = _QuestionKeyword;

  factory QuestionKeyword.fromJson(Map<String, dynamic> json) =>
      _$QuestionKeywordFromJson(json);
}
