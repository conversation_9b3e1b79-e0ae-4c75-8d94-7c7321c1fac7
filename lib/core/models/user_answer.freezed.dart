// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_answer.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserAnswer {
  Question get question;
  Answer? get answer;

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserAnswerCopyWith<UserAnswer> get copyWith =>
      _$UserAnswerCopyWithImpl<UserAnswer>(this as UserAnswer, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserAnswer &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.answer, answer) || other.answer == answer));
  }

  @override
  int get hashCode => Object.hash(runtimeType, question, answer);

  @override
  String toString() {
    return 'UserAnswer(question: $question, answer: $answer)';
  }
}

/// @nodoc
abstract mixin class $UserAnswerCopyWith<$Res> {
  factory $UserAnswerCopyWith(
          UserAnswer value, $Res Function(UserAnswer) _then) =
      _$UserAnswerCopyWithImpl;
  @useResult
  $Res call({Question question, Answer? answer});

  $QuestionCopyWith<$Res> get question;
  $AnswerCopyWith<$Res>? get answer;
}

/// @nodoc
class _$UserAnswerCopyWithImpl<$Res> implements $UserAnswerCopyWith<$Res> {
  _$UserAnswerCopyWithImpl(this._self, this._then);

  final UserAnswer _self;
  final $Res Function(UserAnswer) _then;

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answer = freezed,
  }) {
    return _then(_self.copyWith(
      question: null == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as Question,
      answer: freezed == answer
          ? _self.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as Answer?,
    ));
  }

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<$Res> get question {
    return $QuestionCopyWith<$Res>(_self.question, (value) {
      return _then(_self.copyWith(question: value));
    });
  }

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AnswerCopyWith<$Res>? get answer {
    if (_self.answer == null) {
      return null;
    }

    return $AnswerCopyWith<$Res>(_self.answer!, (value) {
      return _then(_self.copyWith(answer: value));
    });
  }
}

/// @nodoc

class _UserAnswer implements UserAnswer {
  const _UserAnswer({required this.question, this.answer});

  @override
  final Question question;
  @override
  final Answer? answer;

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserAnswerCopyWith<_UserAnswer> get copyWith =>
      __$UserAnswerCopyWithImpl<_UserAnswer>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserAnswer &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.answer, answer) || other.answer == answer));
  }

  @override
  int get hashCode => Object.hash(runtimeType, question, answer);

  @override
  String toString() {
    return 'UserAnswer(question: $question, answer: $answer)';
  }
}

/// @nodoc
abstract mixin class _$UserAnswerCopyWith<$Res>
    implements $UserAnswerCopyWith<$Res> {
  factory _$UserAnswerCopyWith(
          _UserAnswer value, $Res Function(_UserAnswer) _then) =
      __$UserAnswerCopyWithImpl;
  @override
  @useResult
  $Res call({Question question, Answer? answer});

  @override
  $QuestionCopyWith<$Res> get question;
  @override
  $AnswerCopyWith<$Res>? get answer;
}

/// @nodoc
class __$UserAnswerCopyWithImpl<$Res> implements _$UserAnswerCopyWith<$Res> {
  __$UserAnswerCopyWithImpl(this._self, this._then);

  final _UserAnswer _self;
  final $Res Function(_UserAnswer) _then;

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? question = null,
    Object? answer = freezed,
  }) {
    return _then(_UserAnswer(
      question: null == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as Question,
      answer: freezed == answer
          ? _self.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as Answer?,
    ));
  }

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<$Res> get question {
    return $QuestionCopyWith<$Res>(_self.question, (value) {
      return _then(_self.copyWith(question: value));
    });
  }

  /// Create a copy of UserAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AnswerCopyWith<$Res>? get answer {
    if (_self.answer == null) {
      return null;
    }

    return $AnswerCopyWith<$Res>(_self.answer!, (value) {
      return _then(_self.copyWith(answer: value));
    });
  }
}

// dart format on
