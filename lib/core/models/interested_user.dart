import 'package:freezed_annotation/freezed_annotation.dart';
part 'interested_user.freezed.dart';
part 'interested_user.g.dart';

@freezed
abstract class InterestedUser with _$InterestedUser {
  const factory InterestedUser(
      {required String interestedUserId,
      required String userId,
      required String wishlistItemId,
      String? status, // 'interested', 'lets_go', 'going'
      required DateTime createdAt}) = _InterestedUser;
  factory InterestedUser.fromJson(Map<String, dynamic> json) =>
      _$InterestedUserFromJson(json);
}
