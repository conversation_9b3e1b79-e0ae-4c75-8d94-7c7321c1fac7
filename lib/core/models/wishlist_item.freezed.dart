// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wishlist_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WishlistItem {
  String get wishlistItemId;
  String get creatorId;
  String get itemName;
  String? get itemDescription;
  String? get itemUrl;
  String? get categoryId; // Now holds the category ID
  DateTime? get eventDate; // Optional (mutually exclusive with generalDate)
  String? get generalDate; // Optional (mutually exclusive with eventDate)
  String? get placeName;
  @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
  GeoPoint? get placeLocation; // Corrected
  DateTime get createdAt;
  String get status;
  String? get chatRoomId;

  /// Create a copy of WishlistItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WishlistItemCopyWith<WishlistItem> get copyWith =>
      _$WishlistItemCopyWithImpl<WishlistItem>(
          this as WishlistItem, _$identity);

  /// Serializes this WishlistItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WishlistItem &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.itemName, itemName) ||
                other.itemName == itemName) &&
            (identical(other.itemDescription, itemDescription) ||
                other.itemDescription == itemDescription) &&
            (identical(other.itemUrl, itemUrl) || other.itemUrl == itemUrl) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            (identical(other.generalDate, generalDate) ||
                other.generalDate == generalDate) &&
            (identical(other.placeName, placeName) ||
                other.placeName == placeName) &&
            (identical(other.placeLocation, placeLocation) ||
                other.placeLocation == placeLocation) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      wishlistItemId,
      creatorId,
      itemName,
      itemDescription,
      itemUrl,
      categoryId,
      eventDate,
      generalDate,
      placeName,
      placeLocation,
      createdAt,
      status,
      chatRoomId);

  @override
  String toString() {
    return 'WishlistItem(wishlistItemId: $wishlistItemId, creatorId: $creatorId, itemName: $itemName, itemDescription: $itemDescription, itemUrl: $itemUrl, categoryId: $categoryId, eventDate: $eventDate, generalDate: $generalDate, placeName: $placeName, placeLocation: $placeLocation, createdAt: $createdAt, status: $status, chatRoomId: $chatRoomId)';
  }
}

/// @nodoc
abstract mixin class $WishlistItemCopyWith<$Res> {
  factory $WishlistItemCopyWith(
          WishlistItem value, $Res Function(WishlistItem) _then) =
      _$WishlistItemCopyWithImpl;
  @useResult
  $Res call(
      {String wishlistItemId,
      String creatorId,
      String itemName,
      String? itemDescription,
      String? itemUrl,
      String? categoryId,
      DateTime? eventDate,
      String? generalDate,
      String? placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      GeoPoint? placeLocation,
      DateTime createdAt,
      String status,
      String? chatRoomId});
}

/// @nodoc
class _$WishlistItemCopyWithImpl<$Res> implements $WishlistItemCopyWith<$Res> {
  _$WishlistItemCopyWithImpl(this._self, this._then);

  final WishlistItem _self;
  final $Res Function(WishlistItem) _then;

  /// Create a copy of WishlistItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wishlistItemId = null,
    Object? creatorId = null,
    Object? itemName = null,
    Object? itemDescription = freezed,
    Object? itemUrl = freezed,
    Object? categoryId = freezed,
    Object? eventDate = freezed,
    Object? generalDate = freezed,
    Object? placeName = freezed,
    Object? placeLocation = freezed,
    Object? createdAt = null,
    Object? status = null,
    Object? chatRoomId = freezed,
  }) {
    return _then(_self.copyWith(
      wishlistItemId: null == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      itemName: null == itemName
          ? _self.itemName
          : itemName // ignore: cast_nullable_to_non_nullable
              as String,
      itemDescription: freezed == itemDescription
          ? _self.itemDescription
          : itemDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      itemUrl: freezed == itemUrl
          ? _self.itemUrl
          : itemUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventDate: freezed == eventDate
          ? _self.eventDate
          : eventDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      generalDate: freezed == generalDate
          ? _self.generalDate
          : generalDate // ignore: cast_nullable_to_non_nullable
              as String?,
      placeName: freezed == placeName
          ? _self.placeName
          : placeName // ignore: cast_nullable_to_non_nullable
              as String?,
      placeLocation: freezed == placeLocation
          ? _self.placeLocation
          : placeLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _WishlistItem implements WishlistItem {
  const _WishlistItem(
      {required this.wishlistItemId,
      required this.creatorId,
      required this.itemName,
      this.itemDescription,
      this.itemUrl,
      this.categoryId,
      this.eventDate,
      this.generalDate,
      this.placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      this.placeLocation,
      required this.createdAt,
      this.status = 'draft',
      this.chatRoomId});
  factory _WishlistItem.fromJson(Map<String, dynamic> json) =>
      _$WishlistItemFromJson(json);

  @override
  final String wishlistItemId;
  @override
  final String creatorId;
  @override
  final String itemName;
  @override
  final String? itemDescription;
  @override
  final String? itemUrl;
  @override
  final String? categoryId;
// Now holds the category ID
  @override
  final DateTime? eventDate;
// Optional (mutually exclusive with generalDate)
  @override
  final String? generalDate;
// Optional (mutually exclusive with eventDate)
  @override
  final String? placeName;
  @override
  @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
  final GeoPoint? placeLocation;
// Corrected
  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final String status;
  @override
  final String? chatRoomId;

  /// Create a copy of WishlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WishlistItemCopyWith<_WishlistItem> get copyWith =>
      __$WishlistItemCopyWithImpl<_WishlistItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WishlistItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WishlistItem &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.itemName, itemName) ||
                other.itemName == itemName) &&
            (identical(other.itemDescription, itemDescription) ||
                other.itemDescription == itemDescription) &&
            (identical(other.itemUrl, itemUrl) || other.itemUrl == itemUrl) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            (identical(other.generalDate, generalDate) ||
                other.generalDate == generalDate) &&
            (identical(other.placeName, placeName) ||
                other.placeName == placeName) &&
            (identical(other.placeLocation, placeLocation) ||
                other.placeLocation == placeLocation) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      wishlistItemId,
      creatorId,
      itemName,
      itemDescription,
      itemUrl,
      categoryId,
      eventDate,
      generalDate,
      placeName,
      placeLocation,
      createdAt,
      status,
      chatRoomId);

  @override
  String toString() {
    return 'WishlistItem(wishlistItemId: $wishlistItemId, creatorId: $creatorId, itemName: $itemName, itemDescription: $itemDescription, itemUrl: $itemUrl, categoryId: $categoryId, eventDate: $eventDate, generalDate: $generalDate, placeName: $placeName, placeLocation: $placeLocation, createdAt: $createdAt, status: $status, chatRoomId: $chatRoomId)';
  }
}

/// @nodoc
abstract mixin class _$WishlistItemCopyWith<$Res>
    implements $WishlistItemCopyWith<$Res> {
  factory _$WishlistItemCopyWith(
          _WishlistItem value, $Res Function(_WishlistItem) _then) =
      __$WishlistItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String wishlistItemId,
      String creatorId,
      String itemName,
      String? itemDescription,
      String? itemUrl,
      String? categoryId,
      DateTime? eventDate,
      String? generalDate,
      String? placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      GeoPoint? placeLocation,
      DateTime createdAt,
      String status,
      String? chatRoomId});
}

/// @nodoc
class __$WishlistItemCopyWithImpl<$Res>
    implements _$WishlistItemCopyWith<$Res> {
  __$WishlistItemCopyWithImpl(this._self, this._then);

  final _WishlistItem _self;
  final $Res Function(_WishlistItem) _then;

  /// Create a copy of WishlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? wishlistItemId = null,
    Object? creatorId = null,
    Object? itemName = null,
    Object? itemDescription = freezed,
    Object? itemUrl = freezed,
    Object? categoryId = freezed,
    Object? eventDate = freezed,
    Object? generalDate = freezed,
    Object? placeName = freezed,
    Object? placeLocation = freezed,
    Object? createdAt = null,
    Object? status = null,
    Object? chatRoomId = freezed,
  }) {
    return _then(_WishlistItem(
      wishlistItemId: null == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      itemName: null == itemName
          ? _self.itemName
          : itemName // ignore: cast_nullable_to_non_nullable
              as String,
      itemDescription: freezed == itemDescription
          ? _self.itemDescription
          : itemDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      itemUrl: freezed == itemUrl
          ? _self.itemUrl
          : itemUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventDate: freezed == eventDate
          ? _self.eventDate
          : eventDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      generalDate: freezed == generalDate
          ? _self.generalDate
          : generalDate // ignore: cast_nullable_to_non_nullable
              as String?,
      placeName: freezed == placeName
          ? _self.placeName
          : placeName // ignore: cast_nullable_to_non_nullable
              as String?,
      placeLocation: freezed == placeLocation
          ? _self.placeLocation
          : placeLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
