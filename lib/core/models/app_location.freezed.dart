// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_location.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppLocation {
  /// Latitude in decimal degrees (-90 to 90)
  double get latitude;

  /// Longitude in decimal degrees (-180 to 180)
  double get longitude;

  /// Optional accuracy in meters (for GPS-derived locations)
  double? get accuracy;

  /// Optional timestamp when location was captured
  DateTime? get timestamp;

  /// Optional source of the location data
  AppLocationSource get source;

  /// Create a copy of AppLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AppLocationCopyWith<AppLocation> get copyWith =>
      _$AppLocationCopyWithImpl<AppLocation>(this as AppLocation, _$identity);

  /// Serializes this AppLocation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppLocation &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.source, source) || other.source == source));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, latitude, longitude, accuracy, timestamp, source);

  @override
  String toString() {
    return 'AppLocation(latitude: $latitude, longitude: $longitude, accuracy: $accuracy, timestamp: $timestamp, source: $source)';
  }
}

/// @nodoc
abstract mixin class $AppLocationCopyWith<$Res> {
  factory $AppLocationCopyWith(
          AppLocation value, $Res Function(AppLocation) _then) =
      _$AppLocationCopyWithImpl;
  @useResult
  $Res call(
      {double latitude,
      double longitude,
      double? accuracy,
      DateTime? timestamp,
      AppLocationSource source});
}

/// @nodoc
class _$AppLocationCopyWithImpl<$Res> implements $AppLocationCopyWith<$Res> {
  _$AppLocationCopyWithImpl(this._self, this._then);

  final AppLocation _self;
  final $Res Function(AppLocation) _then;

  /// Create a copy of AppLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? accuracy = freezed,
    Object? timestamp = freezed,
    Object? source = null,
  }) {
    return _then(_self.copyWith(
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: freezed == accuracy
          ? _self.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
      timestamp: freezed == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      source: null == source
          ? _self.source
          : source // ignore: cast_nullable_to_non_nullable
              as AppLocationSource,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AppLocation implements AppLocation {
  const _AppLocation(
      {required this.latitude,
      required this.longitude,
      this.accuracy,
      this.timestamp,
      this.source = AppLocationSource.unknown});
  factory _AppLocation.fromJson(Map<String, dynamic> json) =>
      _$AppLocationFromJson(json);

  /// Latitude in decimal degrees (-90 to 90)
  @override
  final double latitude;

  /// Longitude in decimal degrees (-180 to 180)
  @override
  final double longitude;

  /// Optional accuracy in meters (for GPS-derived locations)
  @override
  final double? accuracy;

  /// Optional timestamp when location was captured
  @override
  final DateTime? timestamp;

  /// Optional source of the location data
  @override
  @JsonKey()
  final AppLocationSource source;

  /// Create a copy of AppLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AppLocationCopyWith<_AppLocation> get copyWith =>
      __$AppLocationCopyWithImpl<_AppLocation>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AppLocationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AppLocation &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.accuracy, accuracy) ||
                other.accuracy == accuracy) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.source, source) || other.source == source));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, latitude, longitude, accuracy, timestamp, source);

  @override
  String toString() {
    return 'AppLocation(latitude: $latitude, longitude: $longitude, accuracy: $accuracy, timestamp: $timestamp, source: $source)';
  }
}

/// @nodoc
abstract mixin class _$AppLocationCopyWith<$Res>
    implements $AppLocationCopyWith<$Res> {
  factory _$AppLocationCopyWith(
          _AppLocation value, $Res Function(_AppLocation) _then) =
      __$AppLocationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {double latitude,
      double longitude,
      double? accuracy,
      DateTime? timestamp,
      AppLocationSource source});
}

/// @nodoc
class __$AppLocationCopyWithImpl<$Res> implements _$AppLocationCopyWith<$Res> {
  __$AppLocationCopyWithImpl(this._self, this._then);

  final _AppLocation _self;
  final $Res Function(_AppLocation) _then;

  /// Create a copy of AppLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? accuracy = freezed,
    Object? timestamp = freezed,
    Object? source = null,
  }) {
    return _then(_AppLocation(
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      accuracy: freezed == accuracy
          ? _self.accuracy
          : accuracy // ignore: cast_nullable_to_non_nullable
              as double?,
      timestamp: freezed == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      source: null == source
          ? _self.source
          : source // ignore: cast_nullable_to_non_nullable
              as AppLocationSource,
    ));
  }
}

// dart format on
