// lib/core/models/keyword.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'keyword.freezed.dart';
part 'keyword.g.dart';

@freezed
abstract class Keyword with _$Keyword {
  const factory Keyword({
    @J<PERSON><PERSON>ey(name: 'keyword_id') required String keywordId,
    @J<PERSON><PERSON>ey(name: 'keyword_text') required String keywordText,
    String?
        category, // Assumes DB column is 'category' or handled by json_serializable default
    @<PERSON>son<PERSON>ey(name: 'usage_count', defaultValue: 0) required int usageCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
  }) = _Keyword;

  factory Keyword.fromJson(Map<String, dynamic> json) =>
      _$KeywordFromJson(json);
}
