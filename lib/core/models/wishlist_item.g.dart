// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wishlist_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WishlistItem _$WishlistItemFromJson(Map<String, dynamic> json) =>
    _WishlistItem(
      wishlistItemId: json['wishlistItemId'] as String,
      creatorId: json['creatorId'] as String,
      itemName: json['itemName'] as String,
      itemDescription: json['itemDescription'] as String?,
      itemUrl: json['itemUrl'] as String?,
      categoryId: json['categoryId'] as String?,
      eventDate: json['eventDate'] == null
          ? null
          : DateTime.parse(json['eventDate'] as String),
      generalDate: json['generalDate'] as String?,
      placeName: json['placeName'] as String?,
      placeLocation:
          _geoPointFromJson(json['placeLocation'] as Map<String, dynamic>?),
      createdAt: DateTime.parse(json['createdAt'] as String),
      status: json['status'] as String? ?? 'draft',
      chatRoomId: json['chatRoomId'] as String?,
    );

Map<String, dynamic> _$WishlistItemToJson(_WishlistItem instance) =>
    <String, dynamic>{
      'wishlistItemId': instance.wishlistItemId,
      'creatorId': instance.creatorId,
      'itemName': instance.itemName,
      'itemDescription': instance.itemDescription,
      'itemUrl': instance.itemUrl,
      'categoryId': instance.categoryId,
      'eventDate': instance.eventDate?.toIso8601String(),
      'generalDate': instance.generalDate,
      'placeName': instance.placeName,
      'placeLocation': _geoPointToJson(instance.placeLocation),
      'createdAt': instance.createdAt.toIso8601String(),
      'status': instance.status,
      'chatRoomId': instance.chatRoomId,
    };
