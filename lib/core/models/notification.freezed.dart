// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserNotification {
  String get notificationId;
  String get userId; // User receiving the notification
  String? get wishlistItemId; // Could be null
  String? get eventId; // Could be null
  String? get chatRoomId;
  String
      get notificationType; // 'new_wave', 'friend_request', 'new_message', 'new_message_nonfriend', 'event_update', etc.
  String get content; // The message to display
  bool get isRead;
  String?
      get relatedUserId; // The user who TRIGGERED the notification (e.g., sender of wave)
  DateTime get createdAt;

  /// Create a copy of UserNotification
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserNotificationCopyWith<UserNotification> get copyWith =>
      _$UserNotificationCopyWithImpl<UserNotification>(
          this as UserNotification, _$identity);

  /// Serializes this UserNotification to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserNotification &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.notificationType, notificationType) ||
                other.notificationType == notificationType) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.relatedUserId, relatedUserId) ||
                other.relatedUserId == relatedUserId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationId,
      userId,
      wishlistItemId,
      eventId,
      chatRoomId,
      notificationType,
      content,
      isRead,
      relatedUserId,
      createdAt);

  @override
  String toString() {
    return 'UserNotification(notificationId: $notificationId, userId: $userId, wishlistItemId: $wishlistItemId, eventId: $eventId, chatRoomId: $chatRoomId, notificationType: $notificationType, content: $content, isRead: $isRead, relatedUserId: $relatedUserId, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $UserNotificationCopyWith<$Res> {
  factory $UserNotificationCopyWith(
          UserNotification value, $Res Function(UserNotification) _then) =
      _$UserNotificationCopyWithImpl;
  @useResult
  $Res call(
      {String notificationId,
      String userId,
      String? wishlistItemId,
      String? eventId,
      String? chatRoomId,
      String notificationType,
      String content,
      bool isRead,
      String? relatedUserId,
      DateTime createdAt});
}

/// @nodoc
class _$UserNotificationCopyWithImpl<$Res>
    implements $UserNotificationCopyWith<$Res> {
  _$UserNotificationCopyWithImpl(this._self, this._then);

  final UserNotification _self;
  final $Res Function(UserNotification) _then;

  /// Create a copy of UserNotification
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationId = null,
    Object? userId = null,
    Object? wishlistItemId = freezed,
    Object? eventId = freezed,
    Object? chatRoomId = freezed,
    Object? notificationType = null,
    Object? content = null,
    Object? isRead = null,
    Object? relatedUserId = freezed,
    Object? createdAt = null,
  }) {
    return _then(_self.copyWith(
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      wishlistItemId: freezed == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventId: freezed == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String?,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationType: null == notificationType
          ? _self.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      isRead: null == isRead
          ? _self.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
      relatedUserId: freezed == relatedUserId
          ? _self.relatedUserId
          : relatedUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UserNotification implements UserNotification {
  const _UserNotification(
      {required this.notificationId,
      required this.userId,
      this.wishlistItemId,
      this.eventId,
      this.chatRoomId,
      required this.notificationType,
      required this.content,
      required this.isRead,
      this.relatedUserId,
      required this.createdAt});
  factory _UserNotification.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationFromJson(json);

  @override
  final String notificationId;
  @override
  final String userId;
// User receiving the notification
  @override
  final String? wishlistItemId;
// Could be null
  @override
  final String? eventId;
// Could be null
  @override
  final String? chatRoomId;
  @override
  final String notificationType;
// 'new_wave', 'friend_request', 'new_message', 'new_message_nonfriend', 'event_update', etc.
  @override
  final String content;
// The message to display
  @override
  final bool isRead;
  @override
  final String? relatedUserId;
// The user who TRIGGERED the notification (e.g., sender of wave)
  @override
  final DateTime createdAt;

  /// Create a copy of UserNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserNotificationCopyWith<_UserNotification> get copyWith =>
      __$UserNotificationCopyWithImpl<_UserNotification>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserNotificationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserNotification &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.notificationType, notificationType) ||
                other.notificationType == notificationType) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.relatedUserId, relatedUserId) ||
                other.relatedUserId == relatedUserId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationId,
      userId,
      wishlistItemId,
      eventId,
      chatRoomId,
      notificationType,
      content,
      isRead,
      relatedUserId,
      createdAt);

  @override
  String toString() {
    return 'UserNotification(notificationId: $notificationId, userId: $userId, wishlistItemId: $wishlistItemId, eventId: $eventId, chatRoomId: $chatRoomId, notificationType: $notificationType, content: $content, isRead: $isRead, relatedUserId: $relatedUserId, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$UserNotificationCopyWith<$Res>
    implements $UserNotificationCopyWith<$Res> {
  factory _$UserNotificationCopyWith(
          _UserNotification value, $Res Function(_UserNotification) _then) =
      __$UserNotificationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String notificationId,
      String userId,
      String? wishlistItemId,
      String? eventId,
      String? chatRoomId,
      String notificationType,
      String content,
      bool isRead,
      String? relatedUserId,
      DateTime createdAt});
}

/// @nodoc
class __$UserNotificationCopyWithImpl<$Res>
    implements _$UserNotificationCopyWith<$Res> {
  __$UserNotificationCopyWithImpl(this._self, this._then);

  final _UserNotification _self;
  final $Res Function(_UserNotification) _then;

  /// Create a copy of UserNotification
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? notificationId = null,
    Object? userId = null,
    Object? wishlistItemId = freezed,
    Object? eventId = freezed,
    Object? chatRoomId = freezed,
    Object? notificationType = null,
    Object? content = null,
    Object? isRead = null,
    Object? relatedUserId = freezed,
    Object? createdAt = null,
  }) {
    return _then(_UserNotification(
      notificationId: null == notificationId
          ? _self.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      wishlistItemId: freezed == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventId: freezed == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String?,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationType: null == notificationType
          ? _self.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      isRead: null == isRead
          ? _self.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
      relatedUserId: freezed == relatedUserId
          ? _self.relatedUserId
          : relatedUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

// dart format on
