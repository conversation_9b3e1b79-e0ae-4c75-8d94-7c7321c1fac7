import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_room.freezed.dart';
part 'chat_room.g.dart';

@freezed
abstract class ChatRoom with _$ChatRoom {
  const factory ChatRoom(
      {required String chatRoomId,
      String? eventId, //Optional
      String? wishlistItemId, // Optional
      required DateTime createdAt,
      String? creatorId // Add creatorId
      }) = _ChatRoom;

  factory ChatRoom.fromJson(Map<String, dynamic> json) =>
      _$ChatRoomFromJson(json);
}
