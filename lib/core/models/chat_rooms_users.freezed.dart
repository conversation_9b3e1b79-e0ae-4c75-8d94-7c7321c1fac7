// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_rooms_users.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatRoomsUsers {
  String get chatRoomsUsersId; // Primary Key
  String get chatRoomId;
  String get userId;
  DateTime? get lastReadAt;

  /// Create a copy of ChatRoomsUsers
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChatRoomsUsersCopyWith<ChatRoomsUsers> get copyWith =>
      _$ChatRoomsUsersCopyWithImpl<ChatRoomsUsers>(
          this as ChatRoomsUsers, _$identity);

  /// Serializes this ChatRoomsUsers to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChatRoomsUsers &&
            (identical(other.chatRoomsUsersId, chatRoomsUsersId) ||
                other.chatRoomsUsersId == chatRoomsUsersId) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.lastReadAt, lastReadAt) ||
                other.lastReadAt == lastReadAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, chatRoomsUsersId, chatRoomId, userId, lastReadAt);

  @override
  String toString() {
    return 'ChatRoomsUsers(chatRoomsUsersId: $chatRoomsUsersId, chatRoomId: $chatRoomId, userId: $userId, lastReadAt: $lastReadAt)';
  }
}

/// @nodoc
abstract mixin class $ChatRoomsUsersCopyWith<$Res> {
  factory $ChatRoomsUsersCopyWith(
          ChatRoomsUsers value, $Res Function(ChatRoomsUsers) _then) =
      _$ChatRoomsUsersCopyWithImpl;
  @useResult
  $Res call(
      {String chatRoomsUsersId,
      String chatRoomId,
      String userId,
      DateTime? lastReadAt});
}

/// @nodoc
class _$ChatRoomsUsersCopyWithImpl<$Res>
    implements $ChatRoomsUsersCopyWith<$Res> {
  _$ChatRoomsUsersCopyWithImpl(this._self, this._then);

  final ChatRoomsUsers _self;
  final $Res Function(ChatRoomsUsers) _then;

  /// Create a copy of ChatRoomsUsers
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chatRoomsUsersId = null,
    Object? chatRoomId = null,
    Object? userId = null,
    Object? lastReadAt = freezed,
  }) {
    return _then(_self.copyWith(
      chatRoomsUsersId: null == chatRoomsUsersId
          ? _self.chatRoomsUsersId
          : chatRoomsUsersId // ignore: cast_nullable_to_non_nullable
              as String,
      chatRoomId: null == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      lastReadAt: freezed == lastReadAt
          ? _self.lastReadAt
          : lastReadAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ChatRoomsUsers implements ChatRoomsUsers {
  const _ChatRoomsUsers(
      {required this.chatRoomsUsersId,
      required this.chatRoomId,
      required this.userId,
      this.lastReadAt});
  factory _ChatRoomsUsers.fromJson(Map<String, dynamic> json) =>
      _$ChatRoomsUsersFromJson(json);

  @override
  final String chatRoomsUsersId;
// Primary Key
  @override
  final String chatRoomId;
  @override
  final String userId;
  @override
  final DateTime? lastReadAt;

  /// Create a copy of ChatRoomsUsers
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ChatRoomsUsersCopyWith<_ChatRoomsUsers> get copyWith =>
      __$ChatRoomsUsersCopyWithImpl<_ChatRoomsUsers>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ChatRoomsUsersToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ChatRoomsUsers &&
            (identical(other.chatRoomsUsersId, chatRoomsUsersId) ||
                other.chatRoomsUsersId == chatRoomsUsersId) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.lastReadAt, lastReadAt) ||
                other.lastReadAt == lastReadAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, chatRoomsUsersId, chatRoomId, userId, lastReadAt);

  @override
  String toString() {
    return 'ChatRoomsUsers(chatRoomsUsersId: $chatRoomsUsersId, chatRoomId: $chatRoomId, userId: $userId, lastReadAt: $lastReadAt)';
  }
}

/// @nodoc
abstract mixin class _$ChatRoomsUsersCopyWith<$Res>
    implements $ChatRoomsUsersCopyWith<$Res> {
  factory _$ChatRoomsUsersCopyWith(
          _ChatRoomsUsers value, $Res Function(_ChatRoomsUsers) _then) =
      __$ChatRoomsUsersCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String chatRoomsUsersId,
      String chatRoomId,
      String userId,
      DateTime? lastReadAt});
}

/// @nodoc
class __$ChatRoomsUsersCopyWithImpl<$Res>
    implements _$ChatRoomsUsersCopyWith<$Res> {
  __$ChatRoomsUsersCopyWithImpl(this._self, this._then);

  final _ChatRoomsUsers _self;
  final $Res Function(_ChatRoomsUsers) _then;

  /// Create a copy of ChatRoomsUsers
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? chatRoomsUsersId = null,
    Object? chatRoomId = null,
    Object? userId = null,
    Object? lastReadAt = freezed,
  }) {
    return _then(_ChatRoomsUsers(
      chatRoomsUsersId: null == chatRoomsUsersId
          ? _self.chatRoomsUsersId
          : chatRoomsUsersId // ignore: cast_nullable_to_non_nullable
              as String,
      chatRoomId: null == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      lastReadAt: freezed == lastReadAt
          ? _self.lastReadAt
          : lastReadAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
