// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Event {
  String get eventId;
  String get eventName;
  String get creatorId;
  String? get categoryId;
  DateTime? get eventDate; // Combined date and time
  String? get placeName;
  @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
  GeoPoint? get placeLocation; // Corrected
  String? get eventDescription;
  int? get capacity;
  String? get imageUrl;
  String? get chatRoomId;
  DateTime get createdAt;
  String get status;

  /// Create a copy of Event
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EventCopyWith<Event> get copyWith =>
      _$EventCopyWithImpl<Event>(this as Event, _$identity);

  /// Serializes this Event to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Event &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            (identical(other.placeName, placeName) ||
                other.placeName == placeName) &&
            (identical(other.placeLocation, placeLocation) ||
                other.placeLocation == placeLocation) &&
            (identical(other.eventDescription, eventDescription) ||
                other.eventDescription == eventDescription) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      eventId,
      eventName,
      creatorId,
      categoryId,
      eventDate,
      placeName,
      placeLocation,
      eventDescription,
      capacity,
      imageUrl,
      chatRoomId,
      createdAt,
      status);

  @override
  String toString() {
    return 'Event(eventId: $eventId, eventName: $eventName, creatorId: $creatorId, categoryId: $categoryId, eventDate: $eventDate, placeName: $placeName, placeLocation: $placeLocation, eventDescription: $eventDescription, capacity: $capacity, imageUrl: $imageUrl, chatRoomId: $chatRoomId, createdAt: $createdAt, status: $status)';
  }
}

/// @nodoc
abstract mixin class $EventCopyWith<$Res> {
  factory $EventCopyWith(Event value, $Res Function(Event) _then) =
      _$EventCopyWithImpl;
  @useResult
  $Res call(
      {String eventId,
      String eventName,
      String creatorId,
      String? categoryId,
      DateTime? eventDate,
      String? placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      GeoPoint? placeLocation,
      String? eventDescription,
      int? capacity,
      String? imageUrl,
      String? chatRoomId,
      DateTime createdAt,
      String status});
}

/// @nodoc
class _$EventCopyWithImpl<$Res> implements $EventCopyWith<$Res> {
  _$EventCopyWithImpl(this._self, this._then);

  final Event _self;
  final $Res Function(Event) _then;

  /// Create a copy of Event
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventId = null,
    Object? eventName = null,
    Object? creatorId = null,
    Object? categoryId = freezed,
    Object? eventDate = freezed,
    Object? placeName = freezed,
    Object? placeLocation = freezed,
    Object? eventDescription = freezed,
    Object? capacity = freezed,
    Object? imageUrl = freezed,
    Object? chatRoomId = freezed,
    Object? createdAt = null,
    Object? status = null,
  }) {
    return _then(_self.copyWith(
      eventId: null == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      eventName: null == eventName
          ? _self.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventDate: freezed == eventDate
          ? _self.eventDate
          : eventDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      placeName: freezed == placeName
          ? _self.placeName
          : placeName // ignore: cast_nullable_to_non_nullable
              as String?,
      placeLocation: freezed == placeLocation
          ? _self.placeLocation
          : placeLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      eventDescription: freezed == eventDescription
          ? _self.eventDescription
          : eventDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      capacity: freezed == capacity
          ? _self.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Event implements Event {
  const _Event(
      {required this.eventId,
      required this.eventName,
      required this.creatorId,
      this.categoryId,
      this.eventDate,
      this.placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      this.placeLocation,
      this.eventDescription,
      this.capacity,
      this.imageUrl,
      this.chatRoomId,
      required this.createdAt,
      this.status = 'draft'});
  factory _Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);

  @override
  final String eventId;
  @override
  final String eventName;
  @override
  final String creatorId;
  @override
  final String? categoryId;
  @override
  final DateTime? eventDate;
// Combined date and time
  @override
  final String? placeName;
  @override
  @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
  final GeoPoint? placeLocation;
// Corrected
  @override
  final String? eventDescription;
  @override
  final int? capacity;
  @override
  final String? imageUrl;
  @override
  final String? chatRoomId;
  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final String status;

  /// Create a copy of Event
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EventCopyWith<_Event> get copyWith =>
      __$EventCopyWithImpl<_Event>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EventToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Event &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.eventDate, eventDate) ||
                other.eventDate == eventDate) &&
            (identical(other.placeName, placeName) ||
                other.placeName == placeName) &&
            (identical(other.placeLocation, placeLocation) ||
                other.placeLocation == placeLocation) &&
            (identical(other.eventDescription, eventDescription) ||
                other.eventDescription == eventDescription) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.chatRoomId, chatRoomId) ||
                other.chatRoomId == chatRoomId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      eventId,
      eventName,
      creatorId,
      categoryId,
      eventDate,
      placeName,
      placeLocation,
      eventDescription,
      capacity,
      imageUrl,
      chatRoomId,
      createdAt,
      status);

  @override
  String toString() {
    return 'Event(eventId: $eventId, eventName: $eventName, creatorId: $creatorId, categoryId: $categoryId, eventDate: $eventDate, placeName: $placeName, placeLocation: $placeLocation, eventDescription: $eventDescription, capacity: $capacity, imageUrl: $imageUrl, chatRoomId: $chatRoomId, createdAt: $createdAt, status: $status)';
  }
}

/// @nodoc
abstract mixin class _$EventCopyWith<$Res> implements $EventCopyWith<$Res> {
  factory _$EventCopyWith(_Event value, $Res Function(_Event) _then) =
      __$EventCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String eventId,
      String eventName,
      String creatorId,
      String? categoryId,
      DateTime? eventDate,
      String? placeName,
      @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
      GeoPoint? placeLocation,
      String? eventDescription,
      int? capacity,
      String? imageUrl,
      String? chatRoomId,
      DateTime createdAt,
      String status});
}

/// @nodoc
class __$EventCopyWithImpl<$Res> implements _$EventCopyWith<$Res> {
  __$EventCopyWithImpl(this._self, this._then);

  final _Event _self;
  final $Res Function(_Event) _then;

  /// Create a copy of Event
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? eventId = null,
    Object? eventName = null,
    Object? creatorId = null,
    Object? categoryId = freezed,
    Object? eventDate = freezed,
    Object? placeName = freezed,
    Object? placeLocation = freezed,
    Object? eventDescription = freezed,
    Object? capacity = freezed,
    Object? imageUrl = freezed,
    Object? chatRoomId = freezed,
    Object? createdAt = null,
    Object? status = null,
  }) {
    return _then(_Event(
      eventId: null == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      eventName: null == eventName
          ? _self.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: freezed == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      eventDate: freezed == eventDate
          ? _self.eventDate
          : eventDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      placeName: freezed == placeName
          ? _self.placeName
          : placeName // ignore: cast_nullable_to_non_nullable
              as String?,
      placeLocation: freezed == placeLocation
          ? _self.placeLocation
          : placeLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      eventDescription: freezed == eventDescription
          ? _self.eventDescription
          : eventDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      capacity: freezed == capacity
          ? _self.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      chatRoomId: freezed == chatRoomId
          ? _self.chatRoomId
          : chatRoomId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
