// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'saved_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SavedUser {
  String get savedUserId; // Primary key
  String get saverUserId; // User who saved
  String get savedUser; // User being saved
  DateTime get savedAt;

  /// Create a copy of SavedUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SavedUserCopyWith<SavedUser> get copyWith =>
      _$SavedUserCopyWithImpl<SavedUser>(this as SavedUser, _$identity);

  /// Serializes this SavedUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SavedUser &&
            (identical(other.savedUserId, savedUserId) ||
                other.savedUserId == savedUserId) &&
            (identical(other.saverUserId, saverUserId) ||
                other.saverUserId == saverUserId) &&
            (identical(other.savedUser, savedUser) ||
                other.savedUser == savedUser) &&
            (identical(other.savedAt, savedAt) || other.savedAt == savedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, savedUserId, saverUserId, savedUser, savedAt);

  @override
  String toString() {
    return 'SavedUser(savedUserId: $savedUserId, saverUserId: $saverUserId, savedUser: $savedUser, savedAt: $savedAt)';
  }
}

/// @nodoc
abstract mixin class $SavedUserCopyWith<$Res> {
  factory $SavedUserCopyWith(SavedUser value, $Res Function(SavedUser) _then) =
      _$SavedUserCopyWithImpl;
  @useResult
  $Res call(
      {String savedUserId,
      String saverUserId,
      String savedUser,
      DateTime savedAt});
}

/// @nodoc
class _$SavedUserCopyWithImpl<$Res> implements $SavedUserCopyWith<$Res> {
  _$SavedUserCopyWithImpl(this._self, this._then);

  final SavedUser _self;
  final $Res Function(SavedUser) _then;

  /// Create a copy of SavedUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savedUserId = null,
    Object? saverUserId = null,
    Object? savedUser = null,
    Object? savedAt = null,
  }) {
    return _then(_self.copyWith(
      savedUserId: null == savedUserId
          ? _self.savedUserId
          : savedUserId // ignore: cast_nullable_to_non_nullable
              as String,
      saverUserId: null == saverUserId
          ? _self.saverUserId
          : saverUserId // ignore: cast_nullable_to_non_nullable
              as String,
      savedUser: null == savedUser
          ? _self.savedUser
          : savedUser // ignore: cast_nullable_to_non_nullable
              as String,
      savedAt: null == savedAt
          ? _self.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SavedUser implements SavedUser {
  const _SavedUser(
      {required this.savedUserId,
      required this.saverUserId,
      required this.savedUser,
      required this.savedAt});
  factory _SavedUser.fromJson(Map<String, dynamic> json) =>
      _$SavedUserFromJson(json);

  @override
  final String savedUserId;
// Primary key
  @override
  final String saverUserId;
// User who saved
  @override
  final String savedUser;
// User being saved
  @override
  final DateTime savedAt;

  /// Create a copy of SavedUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SavedUserCopyWith<_SavedUser> get copyWith =>
      __$SavedUserCopyWithImpl<_SavedUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SavedUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SavedUser &&
            (identical(other.savedUserId, savedUserId) ||
                other.savedUserId == savedUserId) &&
            (identical(other.saverUserId, saverUserId) ||
                other.saverUserId == saverUserId) &&
            (identical(other.savedUser, savedUser) ||
                other.savedUser == savedUser) &&
            (identical(other.savedAt, savedAt) || other.savedAt == savedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, savedUserId, saverUserId, savedUser, savedAt);

  @override
  String toString() {
    return 'SavedUser(savedUserId: $savedUserId, saverUserId: $saverUserId, savedUser: $savedUser, savedAt: $savedAt)';
  }
}

/// @nodoc
abstract mixin class _$SavedUserCopyWith<$Res>
    implements $SavedUserCopyWith<$Res> {
  factory _$SavedUserCopyWith(
          _SavedUser value, $Res Function(_SavedUser) _then) =
      __$SavedUserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String savedUserId,
      String saverUserId,
      String savedUser,
      DateTime savedAt});
}

/// @nodoc
class __$SavedUserCopyWithImpl<$Res> implements _$SavedUserCopyWith<$Res> {
  __$SavedUserCopyWithImpl(this._self, this._then);

  final _SavedUser _self;
  final $Res Function(_SavedUser) _then;

  /// Create a copy of SavedUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? savedUserId = null,
    Object? saverUserId = null,
    Object? savedUser = null,
    Object? savedAt = null,
  }) {
    return _then(_SavedUser(
      savedUserId: null == savedUserId
          ? _self.savedUserId
          : savedUserId // ignore: cast_nullable_to_non_nullable
              as String,
      saverUserId: null == saverUserId
          ? _self.saverUserId
          : saverUserId // ignore: cast_nullable_to_non_nullable
              as String,
      savedUser: null == savedUser
          ? _self.savedUser
          : savedUser // ignore: cast_nullable_to_non_nullable
              as String,
      savedAt: null == savedAt
          ? _self.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

// dart format on
