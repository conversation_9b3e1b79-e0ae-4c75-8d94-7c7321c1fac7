import 'package:freezed_annotation/freezed_annotation.dart';

part 'attendee.freezed.dart';
part 'attendee.g.dart';

@freezed
abstract class Attendee with _$Attendee {
  const factory Attendee({
    required String attendeeId,
    required String userId,
    required String eventId,
    @Default(1) int joinCount, // Add with a default value of 1
    DateTime? createdAt,
  }) = _Attendee;
  factory Attendee.fromJson(Map<String, dynamic> json) =>
      _$AttendeeFromJson(json);
}
