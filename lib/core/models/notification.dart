import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

@freezed
abstract class UserNotification with _$UserNotification {
  const factory UserNotification({
    required String notificationId,
    required String userId, // User receiving the notification
    String? wishlistItemId, // Could be null
    String? eventId, // Could be null
    String? chatRoomId,
    required String
        notificationType, // 'new_wave', 'friend_request', 'new_message', 'new_message_nonfriend', 'event_update', etc.
    required String content, // The message to display
    required bool isRead,
    String?
        relatedUserId, // The user who TRIGGERED the notification (e.g., sender of wave)
    required DateTime createdAt,
  }) = _UserNotification;

  factory UserNotification.fromJson(Map<String, dynamic> json) =>
      _$UserNotificationFromJson(json);
}
