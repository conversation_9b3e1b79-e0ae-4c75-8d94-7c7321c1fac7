// lib/core/models/app_location.dart
import 'dart:math' as math;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:mapbox_search/mapbox_search.dart' as mapbox;

part 'app_location.freezed.dart';
part 'app_location.g.dart';

/// Package-independent location model for the Watermelon app
///
/// This model provides a clean abstraction over location data that is:
/// - Independent of external packages (flutter_osm_plugin, mapbox_search)
/// - Compatible with PostGIS geometry storage
/// - Easily convertible to/from different location formats
/// - Future-proof for package migrations
///
/// Use cases:
/// - User location storage
/// - Event and wishlist item locations
/// - Search and filtering by location
/// - Map display and marker positioning
@freezed
abstract class AppLocation with _$AppLocation {
  const factory AppLocation({
    /// Latitude in decimal degrees (-90 to 90)
    required double latitude,

    /// Longitude in decimal degrees (-180 to 180)
    required double longitude,

    /// Optional accuracy in meters (for GPS-derived locations)
    double? accuracy,

    /// Optional timestamp when location was captured
    DateTime? timestamp,

    /// Optional source of the location data
    @Default(AppLocationSource.unknown) AppLocationSource source,
  }) = _AppLocation;

  factory AppLocation.fromJson(Map<String, dynamic> json) =>
      _$AppLocationFromJson(json);

  /// Create AppLocation from GPS coordinates
  factory AppLocation.fromGps({
    required double latitude,
    required double longitude,
    double? accuracy,
    DateTime? timestamp,
  }) {
    return AppLocation(
      latitude: latitude,
      longitude: longitude,
      accuracy: accuracy,
      timestamp: timestamp ?? DateTime.now(),
      source: AppLocationSource.gps,
    );
  }

  /// Create AppLocation from city/address geocoding
  factory AppLocation.fromGeocoding({
    required double latitude,
    required double longitude,
  }) {
    return AppLocation(
      latitude: latitude,
      longitude: longitude,
      source: AppLocationSource.geocoding,
    );
  }

  /// Create AppLocation from user input/selection
  factory AppLocation.fromUserInput({
    required double latitude,
    required double longitude,
  }) {
    return AppLocation(
      latitude: latitude,
      longitude: longitude,
      source: AppLocationSource.userInput,
    );
  }
}

/// Source of location data for tracking and debugging
enum AppLocationSource {
  /// Location source is unknown or not specified
  unknown,

  /// Location obtained from GPS/device location services
  gps,

  /// Location obtained from geocoding (address → coordinates)
  geocoding,

  /// Location manually selected/input by user
  userInput,

  /// Location from MapBox search/geocoding
  mapbox,

  /// Location from OpenStreetMap/Nominatim
  osm,
}

/// Extension methods for AppLocation
extension AppLocationExtensions on AppLocation {
  /// Validate that coordinates are within valid ranges
  bool get isValid {
    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  /// Convert to GeoJSON format for PostGIS storage
  Map<String, dynamic> toGeoJson() {
    if (!isValid) {
      throw ArgumentError('Invalid coordinates: lat=$latitude, lng=$longitude');
    }

    return {
      'type': 'Point',
      'coordinates': [longitude, latitude], // GeoJSON: [lng, lat]
    };
  }

  /// Convert to PostGIS POINT format string
  String toPostGisPoint() {
    if (!isValid) {
      throw ArgumentError('Invalid coordinates: lat=$latitude, lng=$longitude');
    }

    return 'POINT($longitude $latitude)';
  }

  /// Create from GeoJSON format
  static AppLocation? fromGeoJson(Map<String, dynamic>? json) {
    if (json == null ||
        json['coordinates'] == null ||
        (json['coordinates'] as List).length < 2) {
      return null;
    }

    try {
      final List<dynamic> coords = json['coordinates'] as List<dynamic>;
      return AppLocation(
        latitude: (coords[1] as num).toDouble(), // GeoJSON: [lng, lat]
        longitude: (coords[0] as num).toDouble(),
        source: AppLocationSource.unknown,
      );
    } catch (e) {
      print("Error parsing AppLocation from GeoJSON: $json, Error: $e");
      return null;
    }
  }

  /// Calculate distance to another location in meters
  /// Uses Haversine formula for great-circle distance
  double distanceTo(AppLocation other) {
    const double earthRadius = 6371000; // Earth radius in meters

    final double lat1Rad = latitude * (math.pi / 180);
    final double lat2Rad = other.latitude * (math.pi / 180);
    final double deltaLatRad = (other.latitude - latitude) * (math.pi / 180);
    final double deltaLngRad = (other.longitude - longitude) * (math.pi / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);
    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  /// Calculate distance to another location in kilometers
  double distanceToKm(AppLocation other) {
    return distanceTo(other) / 1000;
  }

  /// Create a human-readable string representation
  String toDisplayString() {
    return 'AppLocation(lat: ${latitude.toStringAsFixed(6)}, '
        'lng: ${longitude.toStringAsFixed(6)}, source: $source)';
  }
}

/// Conversion utilities for legacy compatibility
class AppLocationConverters {
  /// Convert from flutter_osm_plugin GeoPoint
  static AppLocation fromGeoPoint(GeoPoint geoPoint,
      {AppLocationSource? source}) {
    return AppLocation(
      latitude: geoPoint.latitude,
      longitude: geoPoint.longitude,
      source: source ?? AppLocationSource.unknown,
    );
  }

  /// Convert to flutter_osm_plugin GeoPoint
  static GeoPoint toGeoPoint(AppLocation location) {
    return GeoPoint(
      latitude: location.latitude,
      longitude: location.longitude,
    );
  }

  /// Convert from MapBox Location
  /// TODO: Implement during migration phase when MapBox API is verified
  static AppLocation fromMapBoxLocation(mapbox.Location location) {
    return AppLocation(
      latitude: location.lat,
      longitude: location.long,
      source: AppLocationSource.mapbox,
    );
  }

  /// Convert to MapBox Location
  /// TODO: Implement during migration phase when MapBox API is verified
  // static mapbox.Location toMapBoxLocation(AppLocation location) {
  //   return mapbox.Location(
  //     lat: location.latitude,
  //     long: location.longitude,
  //   );
  // }

  /// JSON converters for Freezed models
  static AppLocation? appLocationFromJson(Map<String, dynamic>? json) {
    return AppLocationExtensions.fromGeoJson(json);
  }

  static Map<String, dynamic>? appLocationToJson(AppLocation? location) {
    return location?.toGeoJson();
  }
}
