import 'package:watermelon_draft/core/models/question.dart';
import 'package:watermelon_draft/core/models/answer.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_answer.freezed.dart'; // Only need freezed

@freezed
abstract class UserAnswer with _$UserAnswer {
  const factory UserAnswer({
    required Question question,
    Answer? answer, // Could be null if the user hasn't answered yet
  }) = _UserAnswer;
}
