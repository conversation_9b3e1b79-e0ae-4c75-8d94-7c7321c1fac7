// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'interested_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InterestedUser {
  String get interestedUserId;
  String get userId;
  String get wishlistItemId;
  String? get status; // 'interested', 'lets_go', 'going'
  DateTime get createdAt;

  /// Create a copy of InterestedUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestedUserCopyWith<InterestedUser> get copyWith =>
      _$InterestedUserCopyWithImpl<InterestedUser>(
          this as InterestedUser, _$identity);

  /// Serializes this InterestedUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestedUser &&
            (identical(other.interestedUserId, interestedUserId) ||
                other.interestedUserId == interestedUserId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, interestedUserId, userId, wishlistItemId, status, createdAt);

  @override
  String toString() {
    return 'InterestedUser(interestedUserId: $interestedUserId, userId: $userId, wishlistItemId: $wishlistItemId, status: $status, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $InterestedUserCopyWith<$Res> {
  factory $InterestedUserCopyWith(
          InterestedUser value, $Res Function(InterestedUser) _then) =
      _$InterestedUserCopyWithImpl;
  @useResult
  $Res call(
      {String interestedUserId,
      String userId,
      String wishlistItemId,
      String? status,
      DateTime createdAt});
}

/// @nodoc
class _$InterestedUserCopyWithImpl<$Res>
    implements $InterestedUserCopyWith<$Res> {
  _$InterestedUserCopyWithImpl(this._self, this._then);

  final InterestedUser _self;
  final $Res Function(InterestedUser) _then;

  /// Create a copy of InterestedUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? interestedUserId = null,
    Object? userId = null,
    Object? wishlistItemId = null,
    Object? status = freezed,
    Object? createdAt = null,
  }) {
    return _then(_self.copyWith(
      interestedUserId: null == interestedUserId
          ? _self.interestedUserId
          : interestedUserId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      wishlistItemId: null == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _InterestedUser implements InterestedUser {
  const _InterestedUser(
      {required this.interestedUserId,
      required this.userId,
      required this.wishlistItemId,
      this.status,
      required this.createdAt});
  factory _InterestedUser.fromJson(Map<String, dynamic> json) =>
      _$InterestedUserFromJson(json);

  @override
  final String interestedUserId;
  @override
  final String userId;
  @override
  final String wishlistItemId;
  @override
  final String? status;
// 'interested', 'lets_go', 'going'
  @override
  final DateTime createdAt;

  /// Create a copy of InterestedUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestedUserCopyWith<_InterestedUser> get copyWith =>
      __$InterestedUserCopyWithImpl<_InterestedUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestedUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestedUser &&
            (identical(other.interestedUserId, interestedUserId) ||
                other.interestedUserId == interestedUserId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.wishlistItemId, wishlistItemId) ||
                other.wishlistItemId == wishlistItemId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, interestedUserId, userId, wishlistItemId, status, createdAt);

  @override
  String toString() {
    return 'InterestedUser(interestedUserId: $interestedUserId, userId: $userId, wishlistItemId: $wishlistItemId, status: $status, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$InterestedUserCopyWith<$Res>
    implements $InterestedUserCopyWith<$Res> {
  factory _$InterestedUserCopyWith(
          _InterestedUser value, $Res Function(_InterestedUser) _then) =
      __$InterestedUserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String interestedUserId,
      String userId,
      String wishlistItemId,
      String? status,
      DateTime createdAt});
}

/// @nodoc
class __$InterestedUserCopyWithImpl<$Res>
    implements _$InterestedUserCopyWith<$Res> {
  __$InterestedUserCopyWithImpl(this._self, this._then);

  final _InterestedUser _self;
  final $Res Function(_InterestedUser) _then;

  /// Create a copy of InterestedUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? interestedUserId = null,
    Object? userId = null,
    Object? wishlistItemId = null,
    Object? status = freezed,
    Object? createdAt = null,
  }) {
    return _then(_InterestedUser(
      interestedUserId: null == interestedUserId
          ? _self.interestedUserId
          : interestedUserId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      wishlistItemId: null == wishlistItemId
          ? _self.wishlistItemId
          : wishlistItemId // ignore: cast_nullable_to_non_nullable
              as String,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

// dart format on
