# GeoPoint → AppLocation Migration Plan

## Overview

**Objective**: Migrate from `flutter_osm_plugin.GeoPoint` to our custom `AppLocation` model throughout the Watermelon app.

**Benefits**:
- ✅ **Package Independence**: No longer tied to flutter_osm_plugin
- ✅ **Future Proof**: Easy to change mapping providers
- ✅ **Better Architecture**: Clean separation of concerns
- ✅ **Enhanced Features**: Built-in validation, distance calculations, source tracking
- ✅ **PostGIS Compatibility**: Native support for Supabase geometry storage

## Migration Strategy: Gradual Feature-by-Feature

### Phase 1: Core Infrastructure (Foundation)
**Status**: ✅ Complete
- [x] Create `AppLocation` model with Freezed
- [x] Add conversion utilities (`AppLocationConverters`)
- [x] Add validation and distance calculation methods
- [x] Generate Freezed files

### Phase 2: Core Models Migration
**Priority**: High (affects all features)
**Estimated Time**: 2-3 hours

#### 2.1 User Model Migration
- [ ] Update `User.location` from `GeoPoint?` to `AppLocation?`
- [ ] Update JSON converters in `user.dart`
- [ ] Update `json_converters.dart` to use AppLocation
- [ ] Test user creation/update in Supabase

#### 2.2 Event & Wishlist Models
- [ ] Update `Event.location` to use `AppLocation`
- [ ] Update `WishlistItem.location` to use `AppLocation`
- [ ] Update their respective JSON converters

#### 2.3 State Models
- [ ] Update `OnboardingState.location` to use `AppLocation`
- [ ] Update `SearchState.currentUserLocation` to use `AppLocation`

### Phase 3: Services Layer Migration
**Priority**: High (core functionality)
**Estimated Time**: 3-4 hours

#### 3.1 LocationService Updates
- [ ] Update method signatures to return `AppLocation` instead of `GeoPoint`
- [ ] Update `getCurrentLocation()` return type
- [ ] Update `getLocationAndCountryFromCity()` return type
- [ ] Update conversion methods for MapBox integration

#### 3.2 SupabaseService Updates
- [ ] Update PostGIS handling to use `AppLocation.toPostGisPoint()`
- [ ] Update user search methods to accept `AppLocation`
- [ ] Test database operations with new format

#### 3.3 Utility Updates
- [ ] Update `map_utils.dart` to use `AppLocation`
- [ ] Update bounding box calculations

### Phase 4: Feature-Specific Migration
**Priority**: Medium (user-facing features)
**Estimated Time**: 4-5 hours

#### 4.1 Onboarding Feature
- [ ] Update `OnboardingViewModel` to use `AppLocation`
- [ ] Update location-related methods
- [ ] Update `CitySelectorWidget` conversion logic
- [ ] Test complete onboarding flow

#### 4.2 Discover Feature
- [ ] Update `DiscoverViewModel` to use `AppLocation`
- [ ] Update map marker positioning
- [ ] Update distance calculations
- [ ] Update user location handling
- [ ] Test map functionality and user markers

#### 4.3 Search Feature
- [ ] Update `SearchState` and related ViewModels
- [ ] Update location-based filtering
- [ ] Update city search functionality
- [ ] Test search with location filters

### Phase 5: Widget Layer Migration
**Priority**: Low (presentation layer)
**Estimated Time**: 2-3 hours

#### 5.1 Map Widgets
- [ ] Update `CitySelectorWidget` to use AppLocation internally
- [ ] Update any map-related widgets
- [ ] Maintain backward compatibility where needed

#### 5.2 UI Components
- [ ] Update any location display components
- [ ] Update distance display formatting

### Phase 6: Testing & Cleanup
**Priority**: High (quality assurance)
**Estimated Time**: 2-3 hours

#### 6.1 Comprehensive Testing
- [ ] Test complete onboarding flow with location
- [ ] Test Discover page map functionality
- [ ] Test Search with location filters
- [ ] Test user creation and location storage
- [ ] Test distance calculations and filtering

#### 6.2 Code Cleanup
- [ ] Remove unused GeoPoint imports
- [ ] Remove legacy conversion methods
- [ ] Update documentation
- [ ] Code review and optimization

## Migration Order (Recommended)

### Week 1: Foundation & Core
1. **Core Models** (User, Event, WishlistItem) - 1 day
2. **Services Layer** (LocationService, SupabaseService) - 1-2 days
3. **Basic Testing** - 0.5 day

### Week 2: Features
4. **Onboarding Feature** - 1 day
5. **Discover Feature** - 1-2 days
6. **Search Feature** - 1 day

### Week 3: Polish & Testing
7. **Widget Layer** - 1 day
8. **Comprehensive Testing** - 1 day
9. **Cleanup & Documentation** - 0.5 day

## Risk Mitigation

### High-Risk Areas
1. **PostGIS Integration**: Ensure AppLocation.toPostGisPoint() works correctly
2. **Map Functionality**: OSMFlutter still needs GeoPoint for display
3. **Distance Calculations**: Verify accuracy of Haversine formula implementation

### Mitigation Strategies
1. **Gradual Migration**: One feature at a time
2. **Conversion Layer**: Keep AppLocationConverters for compatibility
3. **Extensive Testing**: Test each phase thoroughly before proceeding
4. **Rollback Plan**: Git branches for each phase

## Testing Strategy

### Unit Tests
- [ ] AppLocation model validation
- [ ] Distance calculation accuracy
- [ ] JSON serialization/deserialization
- [ ] Conversion utilities

### Integration Tests
- [ ] User creation with location
- [ ] Location-based search
- [ ] Map marker positioning
- [ ] PostGIS storage and retrieval

### Manual Testing
- [ ] Complete onboarding flow
- [ ] Discover page functionality
- [ ] Search with location filters
- [ ] Cross-platform compatibility

## Success Criteria

### Technical
- ✅ All GeoPoint references removed from core models
- ✅ All features working with AppLocation
- ✅ No flutter_osm_plugin dependencies in core logic
- ✅ PostGIS integration working correctly

### Functional
- ✅ Users can complete onboarding with location
- ✅ Discover page shows users on map correctly
- ✅ Search filters by location work properly
- ✅ Distance calculations are accurate

### Performance
- ✅ No performance regression
- ✅ Memory usage stable
- ✅ Database operations efficient

## Next Steps

1. **Start with Phase 2.1**: User model migration
2. **Create feature branch**: `feature/geopoint-to-applocation-migration`
3. **Implement one phase at a time**
4. **Test thoroughly after each phase**
5. **Document any issues or learnings**

## Notes

- Keep `AppLocationConverters` for backward compatibility during transition
- OSMFlutter widgets will still need GeoPoint - use converters
- Consider creating migration utilities for existing data
- Monitor performance impact of new distance calculations
- Update documentation as we progress
