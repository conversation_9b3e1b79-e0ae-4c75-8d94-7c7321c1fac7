// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Event _$EventFromJson(Map<String, dynamic> json) => _Event(
      eventId: json['eventId'] as String,
      eventName: json['eventName'] as String,
      creatorId: json['creatorId'] as String,
      categoryId: json['categoryId'] as String?,
      eventDate: json['eventDate'] == null
          ? null
          : DateTime.parse(json['eventDate'] as String),
      placeName: json['placeName'] as String?,
      placeLocation: appLocationFromJson(json['place_location']),
      eventDescription: json['eventDescription'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      imageUrl: json['imageUrl'] as String?,
      chatRoomId: json['chatRoomId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      status: json['status'] as String? ?? 'draft',
    );

Map<String, dynamic> _$EventToJson(_Event instance) => <String, dynamic>{
      'eventId': instance.eventId,
      'eventName': instance.eventName,
      'creatorId': instance.creatorId,
      'categoryId': instance.categoryId,
      'eventDate': instance.eventDate?.toIso8601String(),
      'placeName': instance.placeName,
      'place_location': appLocationToJson(instance.placeLocation),
      'eventDescription': instance.eventDescription,
      'capacity': instance.capacity,
      'imageUrl': instance.imageUrl,
      'chatRoomId': instance.chatRoomId,
      'createdAt': instance.createdAt.toIso8601String(),
      'status': instance.status,
    };
