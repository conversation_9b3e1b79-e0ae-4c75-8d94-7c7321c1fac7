// lib/core/models/user.dart
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:watermelon_draft/core/utils/json_converters.dart';

part 'user.freezed.dart';
part 'user.g.dart'; // Required for json_serializable

@freezed
abstract class User with _$User {
  const factory User({
    @JsonKey(name: 'user_id') required String userId,
    required String email, // Email is known at creation
    required String? username, // Nullable until set in onboarding

    @<PERSON>son<PERSON>ey(name: 'full_name') String? fullName,
    int? age,
    String? gender,
    String? city,
    String? country,

    // Field non-nullable, defaults to [] if key missing
    @<PERSON><PERSON><PERSON><PERSON>(name: 'shared_activities', defaultValue: [])
    required List<String> sharedActivities, // Use activity IDs

    // Field non-nullable, defaults to []
    @<PERSON>son<PERSON>ey(name: 'my_interests', defaultValue: []) List<String>? myInterests,
    @JsonKey(
        name: 'location', fromJson: geoPointFromJson, toJson: geoPointToJson)
    GeoPoint? location,
    @JsonKey(name: 'profile_picture_url') String? profilePictureUrl,
    @JsonKey(name: 'avatar_type') String? avatarType,
    @JsonKey(name: 'generated_avatar_color')
    String? generatedAvatarColor, // Hex string

    @JsonKey(name: 'onboarding_complete', defaultValue: false)
    required bool onboardingComplete,
    @JsonKey(name: 'discoverable', defaultValue: true)
    required bool discoverable,
    @JsonKey(name: 'location_updated_at') DateTime? locationUpdatedAt,
    // Omit if null when serializing
    @JsonKey(name: 'created_at', includeIfNull: false) DateTime? createdAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
