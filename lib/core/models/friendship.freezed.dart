// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'friendship.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Friendship {
  String get friendshipId;
  String get userAId;
  String get userBId;
  String get statusAtoB; // 'pending', 'accepted', 'blocked', 'none'
  String get statusBtoA; // 'pending', 'accepted', 'blocked', 'none'
  DateTime? get requestSentAt;
  DateTime get createdAt;

  /// Create a copy of Friendship
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FriendshipCopyWith<Friendship> get copyWith =>
      _$FriendshipCopyWithImpl<Friendship>(this as Friendship, _$identity);

  /// Serializes this Friendship to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Friendship &&
            (identical(other.friendshipId, friendshipId) ||
                other.friendshipId == friendshipId) &&
            (identical(other.userAId, userAId) || other.userAId == userAId) &&
            (identical(other.userBId, userBId) || other.userBId == userBId) &&
            (identical(other.statusAtoB, statusAtoB) ||
                other.statusAtoB == statusAtoB) &&
            (identical(other.statusBtoA, statusBtoA) ||
                other.statusBtoA == statusBtoA) &&
            (identical(other.requestSentAt, requestSentAt) ||
                other.requestSentAt == requestSentAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, friendshipId, userAId, userBId,
      statusAtoB, statusBtoA, requestSentAt, createdAt);

  @override
  String toString() {
    return 'Friendship(friendshipId: $friendshipId, userAId: $userAId, userBId: $userBId, statusAtoB: $statusAtoB, statusBtoA: $statusBtoA, requestSentAt: $requestSentAt, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $FriendshipCopyWith<$Res> {
  factory $FriendshipCopyWith(
          Friendship value, $Res Function(Friendship) _then) =
      _$FriendshipCopyWithImpl;
  @useResult
  $Res call(
      {String friendshipId,
      String userAId,
      String userBId,
      String statusAtoB,
      String statusBtoA,
      DateTime? requestSentAt,
      DateTime createdAt});
}

/// @nodoc
class _$FriendshipCopyWithImpl<$Res> implements $FriendshipCopyWith<$Res> {
  _$FriendshipCopyWithImpl(this._self, this._then);

  final Friendship _self;
  final $Res Function(Friendship) _then;

  /// Create a copy of Friendship
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? friendshipId = null,
    Object? userAId = null,
    Object? userBId = null,
    Object? statusAtoB = null,
    Object? statusBtoA = null,
    Object? requestSentAt = freezed,
    Object? createdAt = null,
  }) {
    return _then(_self.copyWith(
      friendshipId: null == friendshipId
          ? _self.friendshipId
          : friendshipId // ignore: cast_nullable_to_non_nullable
              as String,
      userAId: null == userAId
          ? _self.userAId
          : userAId // ignore: cast_nullable_to_non_nullable
              as String,
      userBId: null == userBId
          ? _self.userBId
          : userBId // ignore: cast_nullable_to_non_nullable
              as String,
      statusAtoB: null == statusAtoB
          ? _self.statusAtoB
          : statusAtoB // ignore: cast_nullable_to_non_nullable
              as String,
      statusBtoA: null == statusBtoA
          ? _self.statusBtoA
          : statusBtoA // ignore: cast_nullable_to_non_nullable
              as String,
      requestSentAt: freezed == requestSentAt
          ? _self.requestSentAt
          : requestSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Friendship implements Friendship {
  const _Friendship(
      {required this.friendshipId,
      required this.userAId,
      required this.userBId,
      required this.statusAtoB,
      required this.statusBtoA,
      this.requestSentAt,
      required this.createdAt});
  factory _Friendship.fromJson(Map<String, dynamic> json) =>
      _$FriendshipFromJson(json);

  @override
  final String friendshipId;
  @override
  final String userAId;
  @override
  final String userBId;
  @override
  final String statusAtoB;
// 'pending', 'accepted', 'blocked', 'none'
  @override
  final String statusBtoA;
// 'pending', 'accepted', 'blocked', 'none'
  @override
  final DateTime? requestSentAt;
  @override
  final DateTime createdAt;

  /// Create a copy of Friendship
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FriendshipCopyWith<_Friendship> get copyWith =>
      __$FriendshipCopyWithImpl<_Friendship>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FriendshipToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Friendship &&
            (identical(other.friendshipId, friendshipId) ||
                other.friendshipId == friendshipId) &&
            (identical(other.userAId, userAId) || other.userAId == userAId) &&
            (identical(other.userBId, userBId) || other.userBId == userBId) &&
            (identical(other.statusAtoB, statusAtoB) ||
                other.statusAtoB == statusAtoB) &&
            (identical(other.statusBtoA, statusBtoA) ||
                other.statusBtoA == statusBtoA) &&
            (identical(other.requestSentAt, requestSentAt) ||
                other.requestSentAt == requestSentAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, friendshipId, userAId, userBId,
      statusAtoB, statusBtoA, requestSentAt, createdAt);

  @override
  String toString() {
    return 'Friendship(friendshipId: $friendshipId, userAId: $userAId, userBId: $userBId, statusAtoB: $statusAtoB, statusBtoA: $statusBtoA, requestSentAt: $requestSentAt, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$FriendshipCopyWith<$Res>
    implements $FriendshipCopyWith<$Res> {
  factory _$FriendshipCopyWith(
          _Friendship value, $Res Function(_Friendship) _then) =
      __$FriendshipCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String friendshipId,
      String userAId,
      String userBId,
      String statusAtoB,
      String statusBtoA,
      DateTime? requestSentAt,
      DateTime createdAt});
}

/// @nodoc
class __$FriendshipCopyWithImpl<$Res> implements _$FriendshipCopyWith<$Res> {
  __$FriendshipCopyWithImpl(this._self, this._then);

  final _Friendship _self;
  final $Res Function(_Friendship) _then;

  /// Create a copy of Friendship
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? friendshipId = null,
    Object? userAId = null,
    Object? userBId = null,
    Object? statusAtoB = null,
    Object? statusBtoA = null,
    Object? requestSentAt = freezed,
    Object? createdAt = null,
  }) {
    return _then(_Friendship(
      friendshipId: null == friendshipId
          ? _self.friendshipId
          : friendshipId // ignore: cast_nullable_to_non_nullable
              as String,
      userAId: null == userAId
          ? _self.userAId
          : userAId // ignore: cast_nullable_to_non_nullable
              as String,
      userBId: null == userBId
          ? _self.userBId
          : userBId // ignore: cast_nullable_to_non_nullable
              as String,
      statusAtoB: null == statusAtoB
          ? _self.statusAtoB
          : statusAtoB // ignore: cast_nullable_to_non_nullable
              as String,
      statusBtoA: null == statusBtoA
          ? _self.statusBtoA
          : statusBtoA // ignore: cast_nullable_to_non_nullable
              as String,
      requestSentAt: freezed == requestSentAt
          ? _self.requestSentAt
          : requestSentAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

// dart format on
