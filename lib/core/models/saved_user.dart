import 'package:freezed_annotation/freezed_annotation.dart';

part 'saved_user.freezed.dart';
part 'saved_user.g.dart';

@freezed
abstract class SavedUser with _$SavedUser {
  const factory SavedUser({
    required String savedUserId, // Primary key
    required String saverUserId, // User who saved
    required String savedUser, // User being saved
    required DateTime savedAt,
  }) = _SavedUser;

  factory SavedUser.fromJson(Map<String, dynamic> json) =>
      _$SavedUserFromJson(json);
}
