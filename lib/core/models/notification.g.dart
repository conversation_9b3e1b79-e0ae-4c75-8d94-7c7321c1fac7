// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserNotification _$UserNotificationFromJson(Map<String, dynamic> json) =>
    _UserNotification(
      notificationId: json['notificationId'] as String,
      userId: json['userId'] as String,
      wishlistItemId: json['wishlistItemId'] as String?,
      eventId: json['eventId'] as String?,
      chatRoomId: json['chatRoomId'] as String?,
      notificationType: json['notificationType'] as String,
      content: json['content'] as String,
      isRead: json['isRead'] as bool,
      relatedUserId: json['relatedUserId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$UserNotificationToJson(_UserNotification instance) =>
    <String, dynamic>{
      'notificationId': instance.notificationId,
      'userId': instance.userId,
      'wishlistItemId': instance.wishlistItemId,
      'eventId': instance.eventId,
      'chatRoomId': instance.chatRoomId,
      'notificationType': instance.notificationType,
      'content': instance.content,
      'isRead': instance.isRead,
      'relatedUserId': instance.relatedUserId,
      'createdAt': instance.createdAt.toIso8601String(),
    };
