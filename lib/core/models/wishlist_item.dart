import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:watermelon_draft/core/models/app_location.dart';
import 'package:watermelon_draft/core/utils/json_converters.dart';

part 'wishlist_item.freezed.dart';
part 'wishlist_item.g.dart';

@freezed
abstract class WishlistItem with _$WishlistItem {
  // Singular - represents a single wish
  const factory WishlistItem({
    required String wishlistItemId,
    required String creatorId,
    required String itemName,
    String? itemDescription,
    String? itemUrl,
    String? categoryId, // Now holds the category ID
    DateTime? eventDate, // Optional (mutually exclusive with generalDate)
    String? generalDate, // Optional (mutually exclusive with eventDate)
    String? placeName,

    /// Location of the wishlist item using AppLocation model
    @JsonKey(
      name: 'place_location',
      fromJson: appLocationFromJson,
      toJson: appLocationToJson,
    )
    AppLocation? placeLocation,
    required DateTime createdAt,
    @Default('draft') String status,
    String? chatRoomId,
  }) = _WishlistItem;

  factory WishlistItem.fromJson(Map<String, dynamic> json) =>
      _$WishlistItemFromJson(json);
}
