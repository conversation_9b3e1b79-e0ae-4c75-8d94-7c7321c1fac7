import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'wishlist_item.freezed.dart';
part 'wishlist_item.g.dart';

@freezed
abstract class WishlistItem with _$WishlistItem {
  // Singular - represents a single wish
  const factory WishlistItem({
    required String wishlistItemId,
    required String creatorId,
    required String itemName,
    String? itemDescription,
    String? itemUrl,
    String? categoryId, // Now holds the category ID
    DateTime? eventDate, // Optional (mutually exclusive with generalDate)
    String? generalDate, // Optional (mutually exclusive with eventDate)
    String? placeName,
    @JsonKey(toJson: _geoPointToJson, fromJson: _geoPointFromJson)
    GeoPoint? placeLocation, // Corrected
    required DateTime createdAt,
    @Default('draft') String status,
     String? chatRoomId,
  }) = _WishlistItem;

  factory WishlistItem.fromJson(Map<String, dynamic> json) =>
      _$WishlistItemFromJson(json);
}

// Helper functions for GeoPoint conversion (outside the class)
GeoPoint? _geoPointFromJson(Map<String, dynamic>? json) {
  if (json == null) {
    return null;
  }
  return GeoPoint(
    latitude: (json['coordinates'][1] as num).toDouble(),
    longitude: (json['coordinates'][0] as num).toDouble(),
  );
}

Map<String, dynamic>? _geoPointToJson(GeoPoint? geoPoint) {
  if (geoPoint == null) return null;
  return {
    'type': 'Point', // This is the GeoJSON format
    'coordinates': [
      geoPoint.longitude,
      geoPoint.latitude
    ], // Note: Longitude first!
    'crs': {
      'type': 'name',
      'properties': {'name': 'urn:ogc:def:crs:EPSG::4326'}
    }, // Specify SRID 4326
  };
}
