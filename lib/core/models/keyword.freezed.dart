// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'keyword.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Keyword {
  @JsonKey(name: 'keyword_id')
  String get keywordId;
  @JsonKey(name: 'keyword_text')
  String get keywordText;
  String?
      get category; // Assumes DB column is 'category' or handled by json_serializable default
  @JsonKey(name: 'usage_count', defaultValue: 0)
  int get usageCount;
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;

  /// Create a copy of Keyword
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $KeywordCopyWith<Keyword> get copyWith =>
      _$KeywordCopyWithImpl<Keyword>(this as Keyword, _$identity);

  /// Serializes this Keyword to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Keyword &&
            (identical(other.keywordId, keywordId) ||
                other.keywordId == keywordId) &&
            (identical(other.keywordText, keywordText) ||
                other.keywordText == keywordText) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.usageCount, usageCount) ||
                other.usageCount == usageCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, keywordId, keywordText, category, usageCount, createdAt);

  @override
  String toString() {
    return 'Keyword(keywordId: $keywordId, keywordText: $keywordText, category: $category, usageCount: $usageCount, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $KeywordCopyWith<$Res> {
  factory $KeywordCopyWith(Keyword value, $Res Function(Keyword) _then) =
      _$KeywordCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'keyword_id') String keywordId,
      @JsonKey(name: 'keyword_text') String keywordText,
      String? category,
      @JsonKey(name: 'usage_count', defaultValue: 0) int usageCount,
      @JsonKey(name: 'created_at') DateTime? createdAt});
}

/// @nodoc
class _$KeywordCopyWithImpl<$Res> implements $KeywordCopyWith<$Res> {
  _$KeywordCopyWithImpl(this._self, this._then);

  final Keyword _self;
  final $Res Function(Keyword) _then;

  /// Create a copy of Keyword
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? keywordId = null,
    Object? keywordText = null,
    Object? category = freezed,
    Object? usageCount = null,
    Object? createdAt = freezed,
  }) {
    return _then(_self.copyWith(
      keywordId: null == keywordId
          ? _self.keywordId
          : keywordId // ignore: cast_nullable_to_non_nullable
              as String,
      keywordText: null == keywordText
          ? _self.keywordText
          : keywordText // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      usageCount: null == usageCount
          ? _self.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Keyword implements Keyword {
  const _Keyword(
      {@JsonKey(name: 'keyword_id') required this.keywordId,
      @JsonKey(name: 'keyword_text') required this.keywordText,
      this.category,
      @JsonKey(name: 'usage_count', defaultValue: 0) required this.usageCount,
      @JsonKey(name: 'created_at') this.createdAt});
  factory _Keyword.fromJson(Map<String, dynamic> json) =>
      _$KeywordFromJson(json);

  @override
  @JsonKey(name: 'keyword_id')
  final String keywordId;
  @override
  @JsonKey(name: 'keyword_text')
  final String keywordText;
  @override
  final String? category;
// Assumes DB column is 'category' or handled by json_serializable default
  @override
  @JsonKey(name: 'usage_count', defaultValue: 0)
  final int usageCount;
  @override
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  /// Create a copy of Keyword
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$KeywordCopyWith<_Keyword> get copyWith =>
      __$KeywordCopyWithImpl<_Keyword>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$KeywordToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Keyword &&
            (identical(other.keywordId, keywordId) ||
                other.keywordId == keywordId) &&
            (identical(other.keywordText, keywordText) ||
                other.keywordText == keywordText) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.usageCount, usageCount) ||
                other.usageCount == usageCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, keywordId, keywordText, category, usageCount, createdAt);

  @override
  String toString() {
    return 'Keyword(keywordId: $keywordId, keywordText: $keywordText, category: $category, usageCount: $usageCount, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$KeywordCopyWith<$Res> implements $KeywordCopyWith<$Res> {
  factory _$KeywordCopyWith(_Keyword value, $Res Function(_Keyword) _then) =
      __$KeywordCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'keyword_id') String keywordId,
      @JsonKey(name: 'keyword_text') String keywordText,
      String? category,
      @JsonKey(name: 'usage_count', defaultValue: 0) int usageCount,
      @JsonKey(name: 'created_at') DateTime? createdAt});
}

/// @nodoc
class __$KeywordCopyWithImpl<$Res> implements _$KeywordCopyWith<$Res> {
  __$KeywordCopyWithImpl(this._self, this._then);

  final _Keyword _self;
  final $Res Function(_Keyword) _then;

  /// Create a copy of Keyword
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? keywordId = null,
    Object? keywordText = null,
    Object? category = freezed,
    Object? usageCount = null,
    Object? createdAt = freezed,
  }) {
    return _then(_Keyword(
      keywordId: null == keywordId
          ? _self.keywordId
          : keywordId // ignore: cast_nullable_to_non_nullable
              as String,
      keywordText: null == keywordText
          ? _self.keywordText
          : keywordText // ignore: cast_nullable_to_non_nullable
              as String,
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      usageCount: null == usageCount
          ? _self.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
