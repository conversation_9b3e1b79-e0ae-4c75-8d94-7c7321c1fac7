// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shared_activity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SharedActivity {
  @JsonKey(name: 'activity_id')
  String get activityId;
  @JsonKey(name: 'activity_name')
  String get activityName;
  @JsonKey(name: 'sort_order')
  int?
      get sortOrder; // 'category' matches DB column name, so @JsonKey is optional if no other config needed
  String? get category;
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;

  /// Create a copy of SharedActivity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SharedActivityCopyWith<SharedActivity> get copyWith =>
      _$SharedActivityCopyWithImpl<SharedActivity>(
          this as SharedActivity, _$identity);

  /// Serializes this SharedActivity to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SharedActivity &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.activityName, activityName) ||
                other.activityName == activityName) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, activityId, activityName, sortOrder, category, createdAt);

  @override
  String toString() {
    return 'SharedActivity(activityId: $activityId, activityName: $activityName, sortOrder: $sortOrder, category: $category, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $SharedActivityCopyWith<$Res> {
  factory $SharedActivityCopyWith(
          SharedActivity value, $Res Function(SharedActivity) _then) =
      _$SharedActivityCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'activity_id') String activityId,
      @JsonKey(name: 'activity_name') String activityName,
      @JsonKey(name: 'sort_order') int? sortOrder,
      String? category,
      @JsonKey(name: 'created_at') DateTime? createdAt});
}

/// @nodoc
class _$SharedActivityCopyWithImpl<$Res>
    implements $SharedActivityCopyWith<$Res> {
  _$SharedActivityCopyWithImpl(this._self, this._then);

  final SharedActivity _self;
  final $Res Function(SharedActivity) _then;

  /// Create a copy of SharedActivity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activityId = null,
    Object? activityName = null,
    Object? sortOrder = freezed,
    Object? category = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_self.copyWith(
      activityId: null == activityId
          ? _self.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as String,
      activityName: null == activityName
          ? _self.activityName
          : activityName // ignore: cast_nullable_to_non_nullable
              as String,
      sortOrder: freezed == sortOrder
          ? _self.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SharedActivity implements SharedActivity {
  const _SharedActivity(
      {@JsonKey(name: 'activity_id') required this.activityId,
      @JsonKey(name: 'activity_name') required this.activityName,
      @JsonKey(name: 'sort_order') this.sortOrder,
      this.category,
      @JsonKey(name: 'created_at') this.createdAt});
  factory _SharedActivity.fromJson(Map<String, dynamic> json) =>
      _$SharedActivityFromJson(json);

  @override
  @JsonKey(name: 'activity_id')
  final String activityId;
  @override
  @JsonKey(name: 'activity_name')
  final String activityName;
  @override
  @JsonKey(name: 'sort_order')
  final int? sortOrder;
// 'category' matches DB column name, so @JsonKey is optional if no other config needed
  @override
  final String? category;
  @override
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  /// Create a copy of SharedActivity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SharedActivityCopyWith<_SharedActivity> get copyWith =>
      __$SharedActivityCopyWithImpl<_SharedActivity>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SharedActivityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SharedActivity &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.activityName, activityName) ||
                other.activityName == activityName) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, activityId, activityName, sortOrder, category, createdAt);

  @override
  String toString() {
    return 'SharedActivity(activityId: $activityId, activityName: $activityName, sortOrder: $sortOrder, category: $category, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$SharedActivityCopyWith<$Res>
    implements $SharedActivityCopyWith<$Res> {
  factory _$SharedActivityCopyWith(
          _SharedActivity value, $Res Function(_SharedActivity) _then) =
      __$SharedActivityCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'activity_id') String activityId,
      @JsonKey(name: 'activity_name') String activityName,
      @JsonKey(name: 'sort_order') int? sortOrder,
      String? category,
      @JsonKey(name: 'created_at') DateTime? createdAt});
}

/// @nodoc
class __$SharedActivityCopyWithImpl<$Res>
    implements _$SharedActivityCopyWith<$Res> {
  __$SharedActivityCopyWithImpl(this._self, this._then);

  final _SharedActivity _self;
  final $Res Function(_SharedActivity) _then;

  /// Create a copy of SharedActivity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activityId = null,
    Object? activityName = null,
    Object? sortOrder = freezed,
    Object? category = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_SharedActivity(
      activityId: null == activityId
          ? _self.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as String,
      activityName: null == activityName
          ? _self.activityName
          : activityName // ignore: cast_nullable_to_non_nullable
              as String,
      sortOrder: freezed == sortOrder
          ? _self.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      category: freezed == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
