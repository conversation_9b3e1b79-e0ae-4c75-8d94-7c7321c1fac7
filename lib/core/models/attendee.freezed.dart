// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'attendee.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Attendee {
  String get attendeeId;
  String get userId;
  String get eventId;
  int get joinCount; // Add with a default value of 1
  DateTime? get createdAt;

  /// Create a copy of Attendee
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AttendeeCopyWith<Attendee> get copyWith =>
      _$AttendeeCopyWithImpl<Attendee>(this as Attendee, _$identity);

  /// Serializes this Attendee to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Attendee &&
            (identical(other.attendeeId, attendeeId) ||
                other.attendeeId == attendeeId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.joinCount, joinCount) ||
                other.joinCount == joinCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, attendeeId, userId, eventId, joinCount, createdAt);

  @override
  String toString() {
    return 'Attendee(attendeeId: $attendeeId, userId: $userId, eventId: $eventId, joinCount: $joinCount, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class $AttendeeCopyWith<$Res> {
  factory $AttendeeCopyWith(Attendee value, $Res Function(Attendee) _then) =
      _$AttendeeCopyWithImpl;
  @useResult
  $Res call(
      {String attendeeId,
      String userId,
      String eventId,
      int joinCount,
      DateTime? createdAt});
}

/// @nodoc
class _$AttendeeCopyWithImpl<$Res> implements $AttendeeCopyWith<$Res> {
  _$AttendeeCopyWithImpl(this._self, this._then);

  final Attendee _self;
  final $Res Function(Attendee) _then;

  /// Create a copy of Attendee
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attendeeId = null,
    Object? userId = null,
    Object? eventId = null,
    Object? joinCount = null,
    Object? createdAt = freezed,
  }) {
    return _then(_self.copyWith(
      attendeeId: null == attendeeId
          ? _self.attendeeId
          : attendeeId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      eventId: null == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      joinCount: null == joinCount
          ? _self.joinCount
          : joinCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Attendee implements Attendee {
  const _Attendee(
      {required this.attendeeId,
      required this.userId,
      required this.eventId,
      this.joinCount = 1,
      this.createdAt});
  factory _Attendee.fromJson(Map<String, dynamic> json) =>
      _$AttendeeFromJson(json);

  @override
  final String attendeeId;
  @override
  final String userId;
  @override
  final String eventId;
  @override
  @JsonKey()
  final int joinCount;
// Add with a default value of 1
  @override
  final DateTime? createdAt;

  /// Create a copy of Attendee
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AttendeeCopyWith<_Attendee> get copyWith =>
      __$AttendeeCopyWithImpl<_Attendee>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AttendeeToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Attendee &&
            (identical(other.attendeeId, attendeeId) ||
                other.attendeeId == attendeeId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.joinCount, joinCount) ||
                other.joinCount == joinCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, attendeeId, userId, eventId, joinCount, createdAt);

  @override
  String toString() {
    return 'Attendee(attendeeId: $attendeeId, userId: $userId, eventId: $eventId, joinCount: $joinCount, createdAt: $createdAt)';
  }
}

/// @nodoc
abstract mixin class _$AttendeeCopyWith<$Res>
    implements $AttendeeCopyWith<$Res> {
  factory _$AttendeeCopyWith(_Attendee value, $Res Function(_Attendee) _then) =
      __$AttendeeCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String attendeeId,
      String userId,
      String eventId,
      int joinCount,
      DateTime? createdAt});
}

/// @nodoc
class __$AttendeeCopyWithImpl<$Res> implements _$AttendeeCopyWith<$Res> {
  __$AttendeeCopyWithImpl(this._self, this._then);

  final _Attendee _self;
  final $Res Function(_Attendee) _then;

  /// Create a copy of Attendee
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? attendeeId = null,
    Object? userId = null,
    Object? eventId = null,
    Object? joinCount = null,
    Object? createdAt = freezed,
  }) {
    return _then(_Attendee(
      attendeeId: null == attendeeId
          ? _self.attendeeId
          : attendeeId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      eventId: null == eventId
          ? _self.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      joinCount: null == joinCount
          ? _self.joinCount
          : joinCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: freezed == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
