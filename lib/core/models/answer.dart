import 'package:freezed_annotation/freezed_annotation.dart';
part 'answer.freezed.dart';
part 'answer.g.dart';

@freezed
abstract class Answer with _$Answer {
  const factory Answer(
      {required String id,
      required String userId,
      required String questionId,
      required String answerText,
      List<String>? answerValue}) = _Answer;
  factory Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);
}
