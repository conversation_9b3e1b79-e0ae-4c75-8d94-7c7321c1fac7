// lib/core/constants/constants.dart
import 'package:watermelon_draft/features/discover/screens/discover_dashboard.dart';
import 'package:watermelon_draft/features/events/screens/events_dashboard.dart';
import 'package:watermelon_draft/features/account/screens/account_dashboard.dart';
import 'package:watermelon_draft/features/friends/screens/FriendsDashboard.dart';
import 'package:watermelon_draft/features/wishlists/screens/wishlists_dashboard.dart';

class Constants {
  // static const textLogoPath = 'assets/logos/watermelon-logo.png';
  // static const logoPath = 'assets/logos/watermelon-text-logo.png';
  static const defaultAvatar = 'assets/images/defaults/default_avatar.png';

  static const bottomNavBarItems = [
    DiscoverDashboard(),
    EventsDashboard(),
    FriendsDashboard(),
    WishlistsDashboard(),
    AccountDashboard(),
  ];

  static const double defaultSearchRadiusMiles = 4.0;
  static const double minSearchRadiusMiles = 1.0;
  static const double maxSearchRadiusMiles = 20.0;
  static const double metersPerMile = 1609.34;

  static const double localRadiusMeters = 40000.0;
  static const double regionalRadiusMeters = 300000.0;

  // static const double defaultSearchRadiusMeters = defaultSearchRadiusMiles * metersPerMile;
}
