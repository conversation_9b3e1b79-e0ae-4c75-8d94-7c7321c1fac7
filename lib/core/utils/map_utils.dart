// lib/core/utils/map_utils.dart
import 'dart:math';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';

BoundingBox calculateBoundingBox(GeoPoint center, double radiusMeters) {
  const double earthRadius = 6371000; // Approx Earth radius in meters

  // Convert radius from meters to degrees (latitude)
  double deltaLat = (radiusMeters / earthRadius) * (180 / pi);

  // Convert radius from meters to degrees (longitude) - depends on latitude
  double deltaLon =
      (radiusMeters / (earthRadius * cos(center.latitude * pi / 180))) *
          (180 / pi);

  double maxLat = center.latitude + deltaLat;
  double minLat = center.latitude - deltaLat;
  double maxLon = center.longitude + deltaLon;
  double minLon = center.longitude - deltaLon;

  // Ensure coordinates don't wrap around poles or date line (basic check)
  maxLat = min(maxLat, 90);
  minLat = max(minLat, -90);
  // maxLon = min(maxLon, 180); // Simplistic wrap check
  // minLon = max(minLon, -180);

  // OSMFlutter BoundingBox uses North, East, South, West
  return BoundingBox(
    north: maxLat,
    east: maxLon,
    south: minLat,
    west: minLon,
  );
}
