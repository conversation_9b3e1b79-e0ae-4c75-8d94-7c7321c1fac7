// lib/core/utils/dialog_utils.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Needed for WidgetRef
import 'package:permission_handler/permission_handler.dart'; // Needed for openAppSettings
import 'package:watermelon_draft/core/providers.dart'; // Assuming locationServiceProvider is here

// --- Dialog: Request Permission Again ---
Future<void> showLocationRequestDialog(
    BuildContext context, WidgetRef ref) async {
  // Use a different context name inside builder
  // Check mounted *before* showing the dialog if called from an async context initially
  if (!context.mounted) return;

  return showDialog<void>(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: const Text('Location Permission'),
        content: const Text(
            'Watermelon needs your location to search for users nearby. Would you like to grant permission again?'),
        actions: <Widget>[
          TextButton(
            child: const Text('No'),
            onPressed: () {
              Navigator.of(dialogContext).pop(); // Just close the dialog
            },
          ),
          TextButton(
            child: const Text('Yes'),
            onPressed: () async {
              // Pop the dialog FIRST before the await
              Navigator.of(dialogContext).pop(); // Close dialog

              // Await the permission request
              final granted =
                  await ref.read(locationServiceProvider).requestPermission();

              // --- CHECK MOUNTED ---
              // Check if the original context (from the widget that called the dialog)
              // is still mounted AFTER the await.
              if (context.mounted) {
                if (granted) {
                  // Optional: Add feedback or trigger action if granted
                  ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Location permission granted!')));
                } else {
                  // Optional: Add feedback if denied again
                  ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Location permission denied.')));
                }
              }
            },
          ),
        ],
      );
    },
  );
}

// --- Dialog: Direct User to Settings ---
Future<void> showLocationSettingsDialog(BuildContext context) async {
  // Made public
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('Location Permission Required'),
        content: const Text(
            'To use this feature, you need to enable location permissions for Watermelon in your device settings.'),
        actions: <Widget>[
          TextButton(
            child: const Text('Cancel'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          TextButton(
            child: const Text('Open Settings'),
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings(); // Open app settings (using permission_handler)
            },
          ),
        ],
      );
    },
  );
}
