// lib/core/utils/ui_utils.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart'; // For groupListsBy
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/providers.dart';

// Function to show the categorized shared activity selection bottom sheet
Future<List<String>?> showSharedActivitySelectionSheet({
  required BuildContext context,
  required WidgetRef ref, // Pass ref to read the provider
  required List<String> initialSelectedIds,
  required int maxSelection,
  int minSelection = 0, // Default minimum is 0 (allow empty for search)
  String title = 'Select Activities', // Optional title override
}) async {
  // Fetch all activities ONCE before showing the sheet for efficiency
  final allActivitiesAsyncValue =
      ref.read(sharedActivitiesProvider); // Use read for initial fetch

  // Use a local copy for the bottom sheet selections
  List<String> localSelectedActivities = List.from(initialSelectedIds);

  // Show the modal bottom sheet and return the result when popped
  return await showModalBottomSheet<List<String>?>(
    context: context,
    isScrollControlled: true, // Allows sheet to take more height
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (sheetContext) {
      // Use StatefulBuilder to manage the selection state *within* the sheet
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setSheetState) {
          return DraggableScrollableSheet(
              expand: false,
              initialChildSize: 0.7,
              minChildSize: 0.4,
              maxChildSize: 0.9,
              builder: (context, scrollController) {
                return Padding(
                  padding: const EdgeInsets.only(
                      top: 16.0,
                      left: 16.0,
                      right: 16.0,
                      bottom: 8.0), // Add padding
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // --- Title & Close Button ---
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '$title (Select $minSelection-$maxSelection)', // Show limits
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          IconButton(
                            icon: Icon(Icons.close),
                            onPressed: () => Navigator.pop(
                                context), // Pop without returning data
                            tooltip: 'Cancel',
                          )
                        ],
                      ),
                      const SizedBox(height: 10),
                      const Divider(),

                      // --- Display Based on Activity Fetch State ---
                      Expanded(
                        child: switch (allActivitiesAsyncValue) {
                          AsyncData(:final value) => _buildActivityListForSheet(
                              // Use helper
                              context,
                              value, // List<SharedActivity>
                              localSelectedActivities,
                              setSheetState,
                              scrollController,
                              maxSelection,
                              minSelection,
                            ),
                          AsyncError(:final error) => Center(
                              child: Text("Error loading activities: $error")),
                          _ => const Center(
                              child: CircularProgressIndicator()) // Loading
                        },
                      ),
                      const SizedBox(height: 10),

                      // --- Done Button ---
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          // Enable only if minSelection requirement is met
                          onPressed:
                              localSelectedActivities.length >= minSelection
                                  ? () => Navigator.pop(
                                      context, localSelectedActivities)
                                  : null,
                          child: const Text('Done'),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).viewInsets.bottom +
                              8), // Adjust for keyboard/safe area
                    ],
                  ),
                );
              });
        },
      );
    },
  );
}

// --- Helper Widget/Function to build the list within the sheet ---
// (Keep this private to ui_utils.dart or move near the main function)
Widget _buildActivityListForSheet(
    BuildContext context,
    List<SharedActivity> allActivities,
    List<String> tempSelectedIds,
    StateSetter setSheetState,
    ScrollController scrollController,
    int maxSelection,
    int minSelection) {
  if (allActivities.isEmpty) {
    return const Center(child: Text("No activities available to select."));
  }

  // 1. Sort the ENTIRE list by sort_order first
  allActivities.sort((a, b) {
    // Handle potential nulls in sortOrder, putting nulls last
    final sortA = a.sortOrder ?? 99999;
    final sortB = b.sortOrder ?? 99999;
    int comparison = sortA.compareTo(sortB);
    // If sort order is the same, sort alphabetically by name as a fallback
    if (comparison == 0) {
      return a.activityName
          .toLowerCase()
          .compareTo(b.activityName.toLowerCase());
    }
    return comparison;
  });

  // 2. Group the NOW SORTED list by category
  // The order of keys in the resulting map should reflect the primary sort order
  final groupedActivities = allActivities.groupListsBy((act) => act.category);

  // 3. Get the category keys IN THE ORDER they appear after sorting
  final orderedCategoryKeys = groupedActivities.keys.toList(); // List<String?>


  return ListView.builder(
    controller: scrollController,
    itemCount: orderedCategoryKeys.length,
    itemBuilder: (context, index) {
      final categoryKey = orderedCategoryKeys[index]; // Type is String?
      // Fallback title for null category
      final categoryTitle = categoryKey ?? 'Uncategorized';

      // Activities within this group are ALREADY sorted correctly due to the initial sort
      final activitiesInCategory = groupedActivities[categoryKey]!;

      return ExpansionTile(
        title: Text(
          categoryTitle, // Use the non-nullable title
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        initiallyExpanded: true, // Keep categories expanded initially
        childrenPadding: const EdgeInsets.only(left: 16.0), // Indent activities
        children: activitiesInCategory.map((activity) {
          final bool isSelected = tempSelectedIds.contains(activity.activityId);
          return CheckboxListTile(
            title: Text(activity.activityName),
            value: isSelected,
            controlAffinity: ListTileControlAffinity.leading,
            dense: true, // Make tiles more compact
            onChanged: (bool? newValue) {
              setSheetState(() {
                // Use the sheet's specific setState
                if (newValue == true) {
                  // Add if limit not reached
                  if (tempSelectedIds.length < maxSelection) {
                    tempSelectedIds.add(activity.activityId);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content:
                          Text('Maximum $maxSelection activities allowed.'),
                      duration: const Duration(seconds: 2),
                    ));
                  }
                } else {
                  // Remove if unchecked (allow removing even if below min temporarily)
                  tempSelectedIds.remove(activity.activityId);
                }
              });
            },
          );
        }).toList(),
      );
    },
  );
}
