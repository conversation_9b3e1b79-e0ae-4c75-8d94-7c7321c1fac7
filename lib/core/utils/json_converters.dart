// lib/core/utils/json_converters.dart
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/app_location.dart';

// Helper function for GeoPoint deserialization
GeoPoint? geoPointFromJson(Map<String, dynamic>? json) {
  // Renamed to public
  if (json == null ||
      json['coordinates'] == null ||
      (json['coordinates'] as List).length < 2) {
    return null;
  }
  try {
    // Ensure coordinates is a list and elements are numbers
    final List<dynamic> coords = json['coordinates'] as List<dynamic>;
    return GeoPoint(
      latitude: (coords[1] as num).toDouble(),
      longitude: (coords[0] as num).toDouble(),
    );
  } catch (e) {
    print("Error parsing GeoPoint from JSON: $json, Error: $e");
    return null;
  }
}

// Helper function for GeoPoint serialization
Map<String, dynamic>? geoPointToJson(GeoPoint? geoPoint) {
  // Renamed to public
  if (geoPoint == null) return null;

  // Validate coordinates to prevent invalid geometry
  if (geoPoint.latitude < -90 ||
      geoPoint.latitude > 90 ||
      geoPoint.longitude < -180 ||
      geoPoint.longitude > 180) {
    print(
        "Invalid GeoPoint coordinates: lat=${geoPoint.latitude}, lng=${geoPoint.longitude}");
    return null;
  }

  return {
    'type': 'Point',
    'coordinates': [geoPoint.longitude, geoPoint.latitude],
    // Note: Removed 'crs' field as PostGIS expects standard GeoJSON without explicit CRS
    // PostGIS assumes SRID 4326 (WGS84) by default for GeoJSON
  };
}

// ============================================================================
// AppLocation JSON Converters (New - for migration)
// ============================================================================

/// Helper function for AppLocation deserialization from GeoJSON
AppLocation? appLocationFromJson(Map<String, dynamic>? json) {
  if (json == null ||
      json['coordinates'] == null ||
      (json['coordinates'] as List).length < 2) {
    return null;
  }

  try {
    final List<dynamic> coords = json['coordinates'] as List<dynamic>;
    return AppLocation(
      latitude: (coords[1] as num).toDouble(), // GeoJSON: [lng, lat]
      longitude: (coords[0] as num).toDouble(),
      source: AppLocationSource.unknown, // Default source for JSON data
    );
  } catch (e) {
    print("Error parsing AppLocation from JSON: $json, Error: $e");
    return null;
  }
}

/// Helper function for AppLocation serialization to GeoJSON
Map<String, dynamic>? appLocationToJson(AppLocation? location) {
  if (location == null) return null;

  // Validate coordinates
  if (!location.isValid) {
    print(
        "Invalid AppLocation coordinates: lat=${location.latitude}, lng=${location.longitude}");
    return null;
  }

  return {
    'type': 'Point',
    'coordinates': [
      location.longitude,
      location.latitude
    ], // GeoJSON: [lng, lat]
    // PostGIS assumes SRID 4326 (WGS84) by default for GeoJSON
  };
}
