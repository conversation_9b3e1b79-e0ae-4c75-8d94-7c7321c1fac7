// lib/core/utils/json_converters.dart
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';

// Helper function for GeoPoint deserialization
GeoPoint? geoPointFromJson(Map<String, dynamic>? json) {
  // Renamed to public
  if (json == null ||
      json['coordinates'] == null ||
      (json['coordinates'] as List).length < 2) {
    return null;
  }
  try {
    // Ensure coordinates is a list and elements are numbers
    final List<dynamic> coords = json['coordinates'] as List<dynamic>;
    return GeoPoint(
      latitude: (coords[1] as num).toDouble(),
      longitude: (coords[0] as num).toDouble(),
    );
  } catch (e) {
    print("Error parsing GeoPoint from JSON: $json, Error: $e");
    return null;
  }
}

// Helper function for GeoPoint serialization
Map<String, dynamic>? geoPointToJson(GeoPoint? geoPoint) {
  // Renamed to public
  if (geoPoint == null) return null;

  // Validate coordinates to prevent invalid geometry
  if (geoPoint.latitude < -90 ||
      geoPoint.latitude > 90 ||
      geoPoint.longitude < -180 ||
      geoPoint.longitude > 180) {
    print(
        "Invalid GeoPoint coordinates: lat=${geoPoint.latitude}, lng=${geoPoint.longitude}");
    return null;
  }

  return {
    'type': 'Point',
    'coordinates': [geoPoint.longitude, geoPoint.latitude],
    // Note: Removed 'crs' field as PostGIS expects standard GeoJSON without explicit CRS
    // PostGIS assumes SRID 4326 (WGS84) by default for GeoJSON
  };
}
