// lib/core/utils/json_converters.dart
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/app_location.dart';

// Helper function for GeoPoint deserialization
GeoPoint? geoPointFromJson(Map<String, dynamic>? json) {
  // Renamed to public
  if (json == null ||
      json['coordinates'] == null ||
      (json['coordinates'] as List).length < 2) {
    return null;
  }
  try {
    // Ensure coordinates is a list and elements are numbers
    final List<dynamic> coords = json['coordinates'] as List<dynamic>;
    return GeoPoint(
      latitude: (coords[1] as num).toDouble(),
      longitude: (coords[0] as num).toDouble(),
    );
  } catch (e) {
    print("Error parsing GeoPoint from JSON: $json, Error: $e");
    return null;
  }
}

// Helper function for GeoPoint serialization
Map<String, dynamic>? geoPointToJson(GeoPoint? geoPoint) {
  // Renamed to public
  if (geoPoint == null) return null;

  // Validate coordinates to prevent invalid geometry
  if (geoPoint.latitude < -90 ||
      geoPoint.latitude > 90 ||
      geoPoint.longitude < -180 ||
      geoPoint.longitude > 180) {
    print(
        "Invalid GeoPoint coordinates: lat=${geoPoint.latitude}, lng=${geoPoint.longitude}");
    return null;
  }

  return {
    'type': 'Point',
    'coordinates': [geoPoint.longitude, geoPoint.latitude],
    // Note: Removed 'crs' field as PostGIS expects standard GeoJSON without explicit CRS
    // PostGIS assumes SRID 4326 (WGS84) by default for GeoJSON
  };
}

// ============================================================================
// AppLocation JSON Converters (New - for migration)
// ============================================================================

/// Helper function for AppLocation deserialization from GeoJSON or PostGIS
AppLocation? appLocationFromJson(dynamic json) {
  if (json == null) return null;

  try {
    // Handle different input formats
    if (json is String) {
      // Handle PostGIS POINT format: "POINT(lng lat)"
      if (json.startsWith('POINT(') && json.endsWith(')')) {
        final coordsStr =
            json.substring(6, json.length - 1); // Remove "POINT(" and ")"
        final coords = coordsStr.split(' ');
        if (coords.length >= 2) {
          return AppLocation(
            latitude: double.parse(coords[1]), // PostGIS: lng lat
            longitude: double.parse(coords[0]),
            source: AppLocationSource.unknown,
          );
        }
      }
      print("Unknown string format for AppLocation: $json");
      return null;
    }

    if (json is Map<String, dynamic>) {
      // Handle GeoJSON format
      if (json['coordinates'] == null ||
          (json['coordinates'] as List).length < 2) {
        return null;
      }

      final List<dynamic> coords = json['coordinates'] as List<dynamic>;
      return AppLocation(
        latitude: (coords[1] as num).toDouble(), // GeoJSON: [lng, lat]
        longitude: (coords[0] as num).toDouble(),
        source: AppLocationSource.unknown,
      );
    }

    print("Unknown format for AppLocation: $json (type: ${json.runtimeType})");
    return null;
  } catch (e) {
    print("Error parsing AppLocation from JSON: $json, Error: $e");
    return null;
  }
}

/// Helper function for AppLocation serialization to GeoJSON
Map<String, dynamic>? appLocationToJson(AppLocation? location) {
  if (location == null) return null;

  // Validate coordinates
  if (!location.isValid) {
    print(
        "Invalid AppLocation coordinates: lat=${location.latitude}, lng=${location.longitude}");
    return null;
  }

  return {
    'type': 'Point',
    'coordinates': [
      location.longitude,
      location.latitude
    ], // GeoJSON: [lng, lat]
    // PostGIS assumes SRID 4326 (WGS84) by default for GeoJSON
  };
}
