// lib/core/utils/avatar_utils.dart
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import 'package:watermelon_draft/core/models/user.dart';

Widget generateAvatar(String name, {double radius = 30.0, Color? color}) {
  final initials = getInitials(name);
  final avatarColor = color ?? getRandomColor();

  return CircleAvatar(
    radius: radius,
    backgroundColor: avatarColor,
    child: Text(
      initials,
      style: TextStyle(color: Colors.white, fontSize: radius * 0.8),
    ),
  );
}

String getInitials(String name) {
  if (name.trim().isEmpty) {
    return '?'; // Fallback for empty name
  }
  List<String> names = name.trim().split(' ');
  String initials = "";
  if (names.isNotEmpty) {
    initials += names[0][0].toUpperCase();
    if (names.length > 1) {
      initials += names[names.length - 1][0].toUpperCase();
    } else if (names[0].length > 1) {
      // Single word, take first and second letter if available
      initials += names[0][1];
    }
  }
  return initials.isEmpty ? '?' : initials; // Final fallback
}

Color getRandomColor() {
  final List<Color> colors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.indigo,
    Colors.amber,
    Colors.cyan,
    Colors.deepPurple,
    Colors.lightBlue,
    Colors.lime
  ];
  return colors[Random().nextInt(colors.length)];
}

Widget buildAvatar(BuildContext context, double radius, User user) {
  // Uses generateAvatar from avatar_utils.dart, uses user.avatarType
  if (user.avatarType == 'uploaded' &&
      user.profilePictureUrl != null) {
    return CircleAvatar(
      radius: radius,
      backgroundImage:
          CachedNetworkImageProvider(user.profilePictureUrl!),
      backgroundColor: Colors.grey[200], // Placeholder color
    );
  } else if (user.avatarType == 'default' &&
      user.profilePictureUrl != null) {
    return CircleAvatar(
      radius: radius,
      backgroundImage: AssetImage(user.profilePictureUrl!),
      backgroundColor: Colors.grey[200],
    );
  } else {
    // --- Generated or fallback ---
    Color? storedColor;
    if (user.avatarType == 'generated' &&
        user.generatedAvatarColor != null) {
      try {
        // Parse hex string back to Color
        storedColor =
            Color(int.parse('0x${user.generatedAvatarColor!}'));
      } catch (e) {
        print("Error parsing stored avatar color: $e");
        // Fallback to default random if parsing fails
      }
    }
    // --- Provide a non-null fallback for the name ---
    return generateAvatar(
      user.fullName ?? user.username ?? '?', // If both are null, pass '?'
      radius: radius,
      color: storedColor, // Pass the STORED color if available
    );
  }
}
