// lib/core/utils/utils.dart

// Convert meters to miles with formatting
String metersToMiles(double meters) {
  double miles = meters / 1609.34;
  // Show one decimal place for more granularity, unless it's very large
  if (miles < 0.1) {
    return "< 0.1 mi";
  }

  if (miles >= 10) {
    return "${miles.toStringAsFixed(0)} mi"; // Whole number for >= 10
  }

  return "${miles.toStringAsFixed(0)} mi"; // One decimal place
}
