// lib/core/widgets/auth_wrapper.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:beamer/beamer.dart';

/// NOTE: AuthWrapper is used in routes.dart to wrap the HomeScreen
/// It checks if the user is authenticated and has completed onboarding

/// Wrapper widget that checks user authentication and onboarding status
/// and redirects appropriately
class AuthWrapper extends ConsumerWidget {
  final Widget child;

  const AuthWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = Supabase.instance.client.auth.currentUser;

    // If no user is authenticated, show the child (should be auth screens)
    if (user == null) {
      return child;
    }

    // User is authenticated - check onboarding status
    return FutureBuilder<bool>(
      future: _checkOnboardingStatus(ref, user.id),
      builder: (context, snapshot) {
        // Show loading while checking status
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Handle error
        if (snapshot.hasError) {
          print('Error checking onboarding status: ${snapshot.error}');
          // On error, redirect to welcome to be safe
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.beamToNamed('/welcome');
          });
          return const Scaffold(
            body: Center(
              child: Text('Checking user status...'),
            ),
          );
        }

        final onboardingComplete = snapshot.data ?? false;

        // If onboarding is not complete, redirect to onboarding
        if (!onboardingComplete) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.beamToNamed('/onboarding');
          });
          return const Scaffold(
            body: Center(
              child: Text('Redirecting to onboarding...'),
            ),
          );
        }

        // Onboarding is complete - show the requested screen
        return child;
      },
    );
  }

  /// Check if user has completed onboarding
  Future<bool> _checkOnboardingStatus(WidgetRef ref, String userId) async {
    try {
      print('AuthWrapper: Checking onboarding status for user: $userId');
      final userRepository = ref.read(userRepositoryProvider);
      final result = await userRepository.getUser(userId);

      return result.fold(
        (failure) {
          print('AuthWrapper: Failed to get user data: $failure');
          return false; // Assume onboarding not complete on error
        },
        (user) {
          print(
              'AuthWrapper: User found - onboarding status: ${user.onboardingComplete}');
          print('AuthWrapper: User data: ${user.toString()}');
          return user.onboardingComplete;
        },
      );
    } catch (e) {
      print('AuthWrapper: Error checking onboarding status: $e');
      return false; // Assume onboarding not complete on error
    }
  }
}
