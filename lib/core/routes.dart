// lib/core/routes.dart
import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
// import 'package:supabase_flutter/supabase_flutter.dart'
//     as supabase; // For guards later
// import 'package:shared_preferences/shared_preferences.dart'; // For guards later
import 'package:watermelon_draft/features/auth/screens/login_screen.dart';
import 'package:watermelon_draft/features/auth/screens/signup_screen.dart';
import 'package:watermelon_draft/features/auth/screens/reset_password_screen.dart';
import 'package:watermelon_draft/features/auth/screens/welcome_screen.dart';
import 'package:watermelon_draft/features/home/<USER>/home_screen.dart';
import 'package:watermelon_draft/features/onboarding/screens/onboarding_screen.dart';
import 'package:watermelon_draft/features/onboarding/screens/onboarding_screen_refactored.dart';
import 'package:watermelon_draft/features/profile/screens/profile_screen.dart';
import 'package:watermelon_draft/features/profile/screens/edit_profile_screen.dart';
import 'package:watermelon_draft/features/account/screens/account_dashboard.dart';
import 'package:watermelon_draft/features/profile/screens/blocked_users_screen.dart';
import 'package:watermelon_draft/features/profile/screens/hidden_users_screen.dart';
import 'package:watermelon_draft/features/search/screens/search_results_page.dart';
import 'package:watermelon_draft/features/search/screens/search_page.dart';
import 'package:watermelon_draft/features/search/screens/city_search_page.dart';
import 'package:watermelon_draft/features/search/screens/filter_page.dart';
import 'package:watermelon_draft/features/notifications/screens/discover_notifications_page.dart';
import 'package:watermelon_draft/features/discover/screens/discover_dashboard.dart';

// --- Beam Locations ---

// Location for authentication screens
class AuthLocation extends BeamLocation<BeamState> {
  @override
  List<Pattern> get pathPatterns =>
      ['/welcome', '/login', '/signup', '/reset-password'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    // Prioritize welcome screen if the path matches exactly.
    // This handles the initial load case directly using the initialPath.
    if (state.uri.path == '/welcome') {
      return [
        const BeamPage(
            key: ValueKey('welcome'), title: 'Welcome', child: WelcomeScreen())
      ];
    }
    // Handle other auth routes
    if (state.uri.pathSegments.contains('login')) {
      return [
        const BeamPage(
            key: ValueKey('login'), title: 'Login', child: LoginScreen())
      ];
    }
    if (state.uri.pathSegments.contains('signup')) {
      return [
        const BeamPage(
            key: ValueKey('signup'), title: 'Sign Up', child: SignupScreen())
      ];
    }
    if (state.uri.pathSegments.contains('reset-password')) {
      return [
        const BeamPage(
            key: ValueKey('reset-password'),
            title: 'Reset Password',
            child: ResetPasswordScreen())
      ];
    }
    // Fallback: If the URI matched the location's patterns but none of the above,
    // default to welcome (or handle as an error/redirect if preferred).
    // This ensures we don't return an empty list if a path like '/auth/unknown' was somehow matched.
    return [
      const BeamPage(
          key: ValueKey('welcome'), title: 'Welcome', child: WelcomeScreen())
    ];
  }
}

// Location for onboarding
class OnboardingLocation extends BeamLocation<BeamState> {
  @override
  List<Pattern> get pathPatterns => ['/onboarding'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) => [
        const BeamPage(
            key: ValueKey('onboarding'),
            title: 'Onboarding',
            child: OnboardingScreenRefactored()),
      ];
}

// Location for main app screens (after login/onboarding)
// ----- OLD -----
// class HomeLocation extends BeamLocation<BeamState> {
//   HomeLocation() : super();
//   @override
//   List<Pattern> get pathPatterns => [
//         '/discover',
//         '/discover/*',
//         '/home',
//         '/account',
//         '/blocked-users',
//         '/hidden-users',
//         '/profile/:userId',
//         '/edit-profile',
//         '/events',
//         '/events/create',
//         '/events/:eventId',
//         '/events/:eventId/edit',
//         '/events/:eventId/notifications',
//         '/wishlists',
//         '/wishlists/create',
//         '/wishlists/:wishlistId',
//         '/wishlists/:wishlistId/edit',
//         '/wishlists/:wishlistId/notifications',
//         '/chat/:chatRoomId',
//         '/search',
//         '/city-search',
//         '/filter',
//         '/notifications/discover'
//       ]; // All main routes

//   @override
//   List<BeamPage> buildPages(BuildContext context, BeamState state) {
//     // Determine the base page (Discover or Home)
//     // BeamPage basePage;
//     // if (state.uri.path == '/discover') {
//     //   // Check exact path for base
//     //   basePage = const BeamPage(
//     //       key: ValueKey('discover'),
//     //       title: 'Discover',
//     //       child: DiscoverDashboard());
//     // } else {
//     //   // Default to HomeScreen or another base if not '/discover'
//     //   basePage = const BeamPage(
//     //       key: ValueKey('home'), title: 'Home', child: HomeScreen());
//     // }
//     // final pages = <BeamPage>[basePage]; // Start with the base page

//     final pages = <BeamPage>[
//       const BeamPage(
//         key: ValueKey('discover'),
//         title: 'Discover',
//         child: DiscoverDashboard(),
//       ), // Base is Discover
//     ];

//     // Add account related pages
//     if (state.uri.pathSegments.contains('account')) {
//       pages.add(const BeamPage(
//           key: ValueKey('account'),
//           title: 'Account',
//           child: AccountDashboard()));
//     }
//     if (state.uri.pathSegments.contains('blocked-users')) {
//       pages.add(const BeamPage(
//           key: ValueKey('blocked-users'),
//           title: 'Blocked Users',
//           child: BlockedUsersScreen()));
//     }
//     if (state.uri.pathSegments.contains('hidden-users')) {
//       pages.add(const BeamPage(
//           key: ValueKey('hidden-users'),
//           title: 'Hidden Users',
//           child: HiddenUsersScreen()));
//     }
//     // Add profile related pages
//     if (state.pathParameters.containsKey('userId')) {
//       final userId = state.pathParameters['userId']!;
//       pages.add(BeamPage(
//           key: ValueKey('profile-$userId'),
//           title: 'Profile',
//           child: ProfileScreen(userId: userId)));
//     }
//     if (state.uri.pathSegments.contains('edit-profile')) {
//       pages.add(const BeamPage(
//           key: ValueKey('edit-profile'),
//           title: 'Edit Profile',
//           child: EditProfileScreen()));
//     }

//     // Add Events pages
//     if (state.uri.pathSegments.contains('events')) {
//       if (state.uri.pathSegments.contains('create')) {
//         pages.add(const BeamPage(
//             key: ValueKey('events-create'),
//             title: 'Create Event',
//             child: CreateEventScreen()));
//       } else if (state.pathParameters.containsKey('eventId')) {
//         final eventId = state.pathParameters['eventId']!;
//         if (state.uri.pathSegments.contains('edit')) {
//           pages.add(BeamPage(
//               key: ValueKey('event-edit-$eventId'),
//               title: 'Edit Event',
//               child: EditEventScreen(eventId: eventId))); // Pass ID
//         } else if (state.uri.pathSegments.contains('notifications')) {
//           pages.add(BeamPage(
//               key: ValueKey('event-notifications-$eventId'),
//               title: 'Event Notifications',
//               child: EventNotificationsPage(eventId: eventId))); // Pass ID
//         } else {
//           pages.add(BeamPage(
//               key: ValueKey('event-detail-$eventId'),
//               title: 'Event Details',
//               child: EventDetailPage(eventId: eventId))); // Pass ID
//         }
//       } else {
//         pages.add(const BeamPage(
//             key: ValueKey('events-dashboard'),
//             title: 'Events',
//             child: EventsDashboard()));
//       }
//     }

//     // Add Wishlists pages
//     if (state.uri.pathSegments.contains('wishlists')) {
//       if (state.uri.pathSegments.contains('create')) {
//         pages.add(const BeamPage(
//             key: ValueKey('wishlists-create'),
//             title: 'Create Wishlist Item',
//             child: CreateWishlistItemScreen()));
//       } else if (state.pathParameters.containsKey('wishlistId')) {
//         final wishlistId = state.pathParameters['wishlistId']!;
//         if (state.uri.pathSegments.contains('edit')) {
//           pages.add(BeamPage(
//               key: ValueKey('wishlist-edit-$wishlistId'),
//               title: 'Edit Wishlist Item',
//               child: EditWishlistItemScreen(wishlistItemId: wishlistId)));
//         } else if (state.uri.pathSegments.contains('notifications')) {
//           pages.add(BeamPage(
//               key: ValueKey('wishlist-notifications-$wishlistId'),
//               title: 'Wishlist Notifications',
//               child:
//                   WishlistItemNotificationsPage(wishlistItemId: wishlistId)));
//         } else {
//           pages.add(BeamPage(
//               key: ValueKey('wishlist-detail-$wishlistId'),
//               title: 'Wishlist Details',
//               child: WishlistItemDetailPage(wishlistItemId: wishlistId)));
//         }
//       } else {
//         pages.add(const BeamPage(
//             key: ValueKey('wishlists-dashboard'),
//             title: 'Wishlists',
//             child: WishlistsDashboard()));
//       }
//     }

//     // Add Chat page
//     if (state.pathParameters.containsKey('chatRoomId')) {
//       final chatRoomId = state.pathParameters['chatRoomId']!;
//       pages.add(BeamPage(
//           key: ValueKey('chat-$chatRoomId'),
//           title: 'Chat',
//           child: ChatScreen(chatRoomId: chatRoomId)));
//     }

//     // Add Search pages
//     if (state.uri.pathSegments.contains('search')) {
//       pages.add(const BeamPage(
//           key: ValueKey('search'), title: 'Search', child: SearchPage()));
//     }
//     if (state.uri.pathSegments.contains('city-search')) {
//       pages.add(const BeamPage(
//           key: ValueKey('city-search'),
//           title: 'Search City',
//           child: CitySearchPage()));
//     }
//     if (state.uri.pathSegments.contains('filter')) {
//       pages.add(const BeamPage(
//           key: ValueKey('filter'), title: 'Filter', child: FilterPage()));
//     }
//     // Add Notifications page
//     if (state.uri.pathSegments.contains('notifications')) {
//       if (state.uri.pathSegments.contains('discover')) {
//         pages.add(const BeamPage(
//             key: ValueKey('notifications-discover'),
//             title: 'Notifications',
//             child: DiscoverNotificationsPage()));
//       }
//       // Add other top-level notification routes if needed
//     }
//     return pages;
//   }
// }

class HomeLocation extends BeamLocation<BeamState> {
  HomeLocation() : super(); // Use default constructor

  @override
  List<Pattern> get pathPatterns => [
        // Define patterns broadly first, let buildPages handle specifics
        '/discover',
        '/discover/*', // Handles anything starting with /discover/* (like /discover/city-search indirectly)
        '/home', // Keep if accessible directly
        '/account', '/blocked-users', '/hidden-users',
        '/profile/:userId', '/edit-profile',
        '/events',
        '/events/*', // Handles /events, /events/create, /events/:id, etc.
        '/wishlists', '/wishlists/*',
        '/chat/:chatRoomId',
        '/search', '/city-search', '/search/results',
        '/filter', // Treat search pages as separate top-levels within HomeLocation for now
        '/notifications/discover'
        // Add more top-level patterns if needed
      ];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    // TODO: Revert back to HomeScreen() later
    // 1. Start with the absolute base - HomeScreen
    final pages = <BeamPage>[
      BeamPage(
        key: const ValueKey('home'), // Always the root of this location
        title: 'Home',
        child: HomeScreen(),
      ),
    ];

    // final pages = <BeamPage>[
    //   BeamPage(
    //     key: const ValueKey('welcome'), // Always the root of this location
    //     title: 'Welcome',
    //     child: WelcomeScreen(),
    //   ),
    // ];

    // ----- while developing, start with Discover -----
    // final pages = <BeamPage>[
    //   BeamPage(
    //     key: const ValueKey('discover'), // Base page is Discover
    //     title: 'Discover',
    //     child: DiscoverDashboard(),
    //   ),
    // ];

    // Add SUB-PAGES on top based on the path/parameters
    // Only add if the base IS discover conceptually

    // Account Routes (If accessed via /discover/account ?) - Adjust paths if needed
    if (state.uri.pathSegments.contains('account')) {
      pages.add(const BeamPage(
          key: ValueKey('account'),
          title: 'Account',
          child: AccountDashboard()));
    } else if (state.uri.pathSegments.contains('blocked-users')) {
      pages.add(const BeamPage(
          key: ValueKey('blocked-users'),
          title: 'Blocked Users',
          child: BlockedUsersScreen()));
    } else if (state.uri.pathSegments.contains('hidden-users')) {
      pages.add(const BeamPage(
          key: ValueKey('hidden-users'),
          title: 'Hidden Users',
          child: HiddenUsersScreen()));
    }
    // Profile Routes
    else if (state.pathParameters.containsKey('userId')) {
      final userId = state.pathParameters['userId']!;
      pages.add(BeamPage(
          key: ValueKey('profile-$userId'),
          title: 'Profile',
          child: ProfileScreen(userId: userId)));
    } else if (state.uri.pathSegments.contains('edit-profile')) {
      // Check segments? Or full path?
      pages.add(const BeamPage(
          key: ValueKey('edit-profile'),
          title: 'Edit Profile',
          child: EditProfileScreen()));
    }
    // Event Routes
    else if (state.uri.pathSegments.contains('events')) {
      if (state.uri.pathSegments.contains('create')) {
        /* Add Create */
      } else if (state.pathParameters.containsKey('eventId')) {
        /* Add Detail/Edit/Notify */
      } else {/* Add Dashboard? */} // Maybe don't add dashboard on top?
    }
    // Wishlist Routes (similar logic)
    // ...
    // Chat Route
    else if (state.pathParameters.containsKey('chatRoomId')) {
      /* Add ChatScreen */
    }
    // Search Routes
    // Handle specific search-related top-level pages first
    if (state.uri.path == '/city-search') {
      pages.add(const BeamPage(
          key: ValueKey('city-search'),
          title: 'Search City',
          child: CitySearchPage()));
    } else if (state.uri.path == '/filter') {
      pages.add(const BeamPage(
          key: ValueKey('filter'), title: 'Filter', child: FilterPage()));
    }
    // Handle '/search' and '/search/results'
    else if (state.uri.pathSegments.isNotEmpty &&
        state.uri.pathSegments.first == 'search') {
      // Always add SearchPage if path starts with /search
      // Add only if it's not already the last page (to avoid duplicates if path is exactly /search)
      if (pages.last.key != const ValueKey('search')) {
        pages.add(const BeamPage(
            key: ValueKey('search'), title: 'Search', child: SearchPage()));
      }
      // Specifically check for the results page
      if (state.uri.path == '/search/results') {
        pages.add(const BeamPage(
          key: ValueKey('search-results'),
          title: 'Search Results',
          child: SearchResultsPage(), // No arguments needed
        ));
      }
    }
    // Notifications Route
    else if (state.uri.pathSegments.contains('notifications')) {
      if (state.uri.pathSegments.contains('discover')) {
        pages.add(const BeamPage(
            key: ValueKey('notifications-discover'),
            title: 'Notifications',
            child: DiscoverNotificationsPage()));
      }
    }

    // If URI is JUST /discover, stack is [DiscoverDashboard]
    return pages;
    // --------------

    // TODO: Uncomment this after Discover is fully functional
    // 2. Optionally add the current "main section" page if not just at '/home'
    //    (This part depends heavily on how HomeScreen manages its tabs/body.
    //     For now, let's assume direct navigation builds the stack fully.)
    // if (state.uri.pathSegments.isNotEmpty) {
    //   if (state.uri.pathSegments.first == 'discover' &&
    //       state.uri.pathSegments.length == 1) {
    //     pages.add(const BeamPage(
    //         key: ValueKey('discover'),
    //         title: 'Discover',
    //         child: DiscoverDashboard()));
    //   } else if (state.uri.pathSegments.first == 'events' &&
    //       state.uri.pathSegments.length == 1) {
    //     pages.add(const BeamPage(
    //         key: ValueKey('events-dashboard'),
    //         title: 'Events',
    //         child: EventsDashboard()));
    //   } else if (state.uri.pathSegments.first == 'wishlists' &&
    //       state.uri.pathSegments.length == 1) {
    //     pages.add(const BeamPage(
    //         key: ValueKey('wishlists-dashboard'),
    //         title: 'Wishlists',
    //         child: WishlistsDashboard()));
    //   } else if (state.uri.pathSegments.first == 'account' &&
    //       state.uri.pathSegments.length == 1) {
    //     pages.add(const BeamPage(
    //         key: ValueKey('account'),
    //         title: 'Account',
    //         child: AccountDashboard()));
    //   }
    //   // Add similar logic for '/friends' if it's a main section page
    // }

    // // 3. Add SUB-PAGES / DETAIL PAGES based on the full path or parameters
    // // These checks run *independently* of the main section logic above.

    // // Profile Routes (e.g., /profile/123 OR /edit-profile)
    // // Note: Assumes /profile and /edit-profile are pushed onto the existing stack
    // if (state.pathParameters.containsKey('userId')) {
    //   final userId = state.pathParameters['userId']!;
    //   pages.add(BeamPage(
    //       key: ValueKey('profile-$userId'),
    //       title: 'Profile',
    //       child: ProfileScreen(userId: userId)));
    // } else if (state.uri.path == '/edit-profile') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('edit-profile'),
    //       title: 'Edit Profile',
    //       child: EditProfileScreen()));
    // }

    // // Account Sub-Routes (e.g., /blocked-users OR /hidden-users - assuming pushed from /account)
    // if (state.uri.path == '/blocked-users') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('blocked-users'),
    //       title: 'Blocked Users',
    //       child: BlockedUsersScreen()));
    // } else if (state.uri.path == '/hidden-users') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('hidden-users'),
    //       title: 'Hidden Users',
    //       child: HiddenUsersScreen()));
    // }

    // // Event Sub-Routes (e.g., /events/create, /events/123, /events/123/edit)
    // if (state.uri.path == '/events/create') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('events-create'),
    //       title: 'Create Event',
    //       child: CreateEventScreen()));
    // } else if (state.pathParameters.containsKey('eventId')) {
    //   final eventId = state.pathParameters['eventId']!;
    //   // Add Detail page if the path contains the ID (e.g., /events/123, /events/123/edit, /events/123/notifications)
    //   if (state.uri.pathSegments.contains(eventId)) {
    //     // Check if detail page is already the last page to avoid duplicates if possible
    //     if (pages.last.key != ValueKey('event-detail-$eventId')) {
    //       pages.add(BeamPage(
    //           key: ValueKey('event-detail-$eventId'),
    //           title: 'Event Details',
    //           child: EventDetailPage(eventId: eventId)));
    //     }
    //   }
    //   // Add Edit or Notifications on top if the path matches exactly
    //   if (state.uri.path == '/events/$eventId/edit') {
    //     pages.add(BeamPage(
    //         key: ValueKey('event-edit-$eventId'),
    //         title: 'Edit Event',
    //         child: EditEventScreen(eventId: eventId)));
    //   } else if (state.uri.path == '/events/$eventId/notifications') {
    //     pages.add(BeamPage(
    //         key: ValueKey('event-notifications-$eventId'),
    //         title: 'Event Notifications',
    //         child: EventNotificationsPage(eventId: eventId)));
    //   }
    // }

    // // Wishlist Sub-Routes (similar logic)
    // // ... check for /wishlists/create, /wishlists/:id, /wishlists/:id/edit, /wishlists/:id/notifications ...

    // // Chat Route
    // if (state.pathParameters.containsKey('chatRoomId')) {
    //   final chatRoomId = state.pathParameters['chatRoomId']!;
    //   pages.add(BeamPage(
    //       key: ValueKey('chat-$chatRoomId'),
    //       title: 'Chat',
    //       child: ChatScreen(chatRoomId: chatRoomId)));
    // }

    // // Search Routes (Treating as pages pushed onto the current stack)
    // Handle specific search-related top-level pages first
    // if (state.uri.path == '/city-search') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('city-search'),
    //       title: 'Search City',
    //       child: CitySearchPage()));
    // } else if (state.uri.path == '/filter') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('filter'), title: 'Filter', child: FilterPage()));
    // }
    // // Handle '/search' and '/search/results'
    // else if (state.uri.pathSegments.isNotEmpty &&
    //     state.uri.pathSegments.first == 'search') {
    //   // Always add SearchPage if path starts with /search
    //   // Add only if it's not already the last page (to avoid duplicates if path is exactly /search)
    //   if (pages.last.key != const ValueKey('search')) {
    //     pages.add(const BeamPage(
    //         key: ValueKey('search'), title: 'Search', child: SearchPage()));
    //   }
    //   // Specifically check for the results page
    //   if (state.uri.path == '/search/results') {
    //     pages.add(const BeamPage(
    //       key: ValueKey('search-results'),
    //       title: 'Search Results',
    //       child: SearchResultsPage(), // No arguments needed
    //     ));
    //   }
    // }

    // // Notifications Route
    // if (state.uri.path == '/notifications/discover') {
    //   pages.add(const BeamPage(
    //       key: ValueKey('notifications-discover'),
    //       title: 'Notifications',
    //       child: DiscoverNotificationsPage()));
    // }

    // return pages;
  }
}

// --- Beamer Delegate ---
final routerDelegate = BeamerDelegate(
  // --- CHANGE INITIAL PATH FOR TESTING ---
  // Start directly in Discover for testing navigation within HomeLocation
  // initialPath: "/discover",
  // TODO: Uncomment this for production
  initialPath: "/welcome", // CHANGE BACK LATER
  locationBuilder: BeamerLocationBuilder(
    beamLocations: [
      // TODO: Add AuthLocation() and OnboardingLocation() when ready
      AuthLocation(),
      OnboardingLocation(),
      HomeLocation(), // Pass RouteInformation
    ],
  ),
  // --- GUARDS WILL BE ADDED LATER ---
  guards: [
    // Example Guard Structure (Implement later)
    // BeamGuard(
    //   pathPatterns: ['/welcome', '/login', '/signup', '/reset-password'],
    //   check: (context, location) {
    //     final user = supabase.Supabase.instance.client.auth.currentUser;
    //     return user == null; // Allow access only if user is NULL
    //   },
    //   beamToNamed: (origin, target) => '/home', // Redirect if logged in
    // ),
    // BeamGuard(
    //   pathPatterns: ['/onboarding'],
    //   check: (context, location) {
    //      // Check auth AND onboarding status
    //   },
    //    beamToNamed: (origin, target) => ..., // Redirect if needed
    // ),
    // BeamGuard(
    //   pathPatterns: ['/home', '/profile/*', '/events', /* ... all other protected routes */ ],
    //   check: (context, location) {
    //       // Check auth AND onboarding status
    //   },
    //    beamToNamed: (origin, target) => ..., // Redirect if needed
    // ),
  ],
);
