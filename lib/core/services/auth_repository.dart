// lib/core/services/auth_repository.dart

import 'package:supabase_flutter/supabase_flutter.dart'
    as supabase; // Use prefix
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/user.dart';

class AuthRepository {
  final SupabaseService _supabaseService;

  AuthRepository(this._supabaseService);

  Future<Either<Failure, supabase.User?>> signUp({
    required String email,
    required String password,
  }) async {
    try {
      // Define the redirect URL (must match one in your Supabase Auth settings)
      // const String redirectUrl = 'io.ngala.watermelon://auth/callback';

      // 1. Call Supabase Auth signup WITH redirectTo
      final authResponse = await _supabaseService.signUp(
        email: email,
        password: password,
        // emailRedirectTo: redirectUrl,
      );
      final user = authResponse.user;

      if (user != null) {
        // 2. Create a new User object (from your model) with MINIMAL info
        final newUser = User(
          userId: user.id, // Get ID from Supabase Auth user
          username: '', // Set to empty string initially
          email:
              user.email ?? email, // Get email from Supabase Auth user or param
          sharedActivities: [],
          myInterests: [],
          onboardingComplete: false, // Set to false initially
          discoverable: true, // Set to true initially

          // All other fields will be null or use their @Default values
        );

        // 3. Save the minimal user record to your 'Users' table
        await _supabaseService.createUser(newUser);

        // 4. Return the Supabase Auth User object
        return right(user);
      } else {
        // If email confirmation is required, user might be non-null but session null
        // Or if signup failed in a specific way for AuthResponse to be non-null but user null
        if (authResponse.session == null && authResponse.user == null) {
          // This indicates a failure that wasn't an AuthException
          return left(AuthFailure('Signup failed unexpectedly.'));
        }
        // If user is null but maybe session exists or other edge case?
        // Best practice is often to prompt for email verification check
        return left(AuthFailure(
            'Signup may require email verification. Please check your email.'));
      }
    } on supabase.AuthException catch (e) {
      return left(AuthFailure(e.message));
    } on supabase.PostgrestException catch (e) {
      // Catch potential createUser error
      // Attempt to clean up the auth user if the profile creation failed? (More advanced)
      print("ERROR creating user profile after signup: $e");
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN',
          "Signup successful, but failed to create profile: ${e.message}"));
    } catch (e) {
      return left(AuthFailure(
          "An unexpected error occurred during signup: ${e.toString()}"));
    }
  }

  Future<Either<Failure, supabase.User?>> signIn(
      {required String email, required String password}) async {
    try {
      final authResponse =
          await _supabaseService.signIn(email: email, password: password);
      final user = authResponse.user;
      if (user == null) {
        return left(AuthFailure('Sign in successful, but no user returned.'));
      }
      return right(user);
    } on supabase.AuthException catch (e) {
      return left(AuthFailure(e.message));
    } catch (e) {
      return left(AuthFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> signOut() async {
    try {
      await _supabaseService.signOut();
      return right(unit); // Use 'unit' for void success (fpdart convention)
    } on supabase.AuthException catch (e) {
      return left(AuthFailure(e.message));
    } catch (e) {
      return left(AuthFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> resetPassword({required String email}) async {
    try {
      // Define the redirect URL (must match one in your Supabase Auth settings)
      const String redirectUrl = 'io.ngala.watermelon://auth/callback';

      await _supabaseService.resetPassword(
          email: email, redirectTo: redirectUrl);
      return right(unit);
    } on supabase.AuthException catch (e) {
      return left(AuthFailure(e.message));
    } catch (e) {
      return left(AuthFailure(e.toString()));
    }
  }

  Future<Either<Failure, supabase.User?>> currentUser() async {
    try {
      final user = _supabaseService.currentUser;
      return right(user);
    } on supabase.AuthException catch (e) {
      return left(AuthFailure(e.message));
    } catch (e) {
      return left(AuthFailure(
          "An unexpected error occurred getting current user: ${e.toString()}"));
    }
  }
}
