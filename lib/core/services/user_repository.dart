// lib/core/services/user_repository.dart
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:watermelon_draft/core/models/friendship.dart';
import 'package:watermelon_draft/core/models/saved_user.dart';

class UserRepository {
  final SupabaseService _supabaseService;

  UserRepository(this._supabaseService);

// --------- Basic Get/Update ----------
  Future<Either<Failure, User>> getUser(String userId) async {
    try {
      final userData = await _supabaseService.getUser(userId);
      if (userData == null) {
        return left(DatabaseFailure('User not found for ID: $userId'));
      }
      return right(User.fromJson(userData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, User?>> getCurrentUser(String userId) async {
    try {
      final userData = await _supabaseService.getUser(userId);
      if (userData == null) {
        return right(null); // Return a Left (failure)
      }
      return right(User.fromJson(userData)); // Return a Right (success)
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(
          e.toString())); // Catch any exceptions and return a Left
    }
  }

  Future<Either<Failure, Unit>> updateUser(User user) async {
    try {
      await _supabaseService.updateUser(user);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> updateDiscoverability(
      String userId, bool isDiscoverable) async {
    try {
      // Create a partial update map
      final updateData = {
        'discoverable': isDiscoverable,
      };
      await _supabaseService.updateUserPartial(
          userId, updateData); // Implement in SupabaseService
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, bool>> isUsernameTaken(String username) async {
    // try {
    //   final result = await _supabaseService.isUsernameTaken(username);
    //   return right(result); // Simply pass on bool
    // } on supabase.PostgrestException catch (e) {
    //   return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    // } catch (e) {
    //   return left(DatabaseFailure(e.toString()));
    // }
    print("Repo: isUsernameTaken called (TEMPORARY TEST) - returning false");
    await Future.delayed(Duration(milliseconds: 300)); // Simulate network delay
    return right(false); // Always return available
  }

  Future<Either<Failure, String>> uploadProfilePicture(
      String userId, XFile imageFile) async {
    try {
      final String? imageUrl = await _supabaseService.uploadProfilePicture(
          userId, imageFile); // Upload and get URL
      if (imageUrl == null) {
        return left(DatabaseFailure("Image upload failed"));
      }
      return right(imageUrl);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// --------- Search Methods ----------
  Future<Either<Failure, List<User>>> searchUsersByLocation({
    required double latitude,
    required double longitude,
    required double radiusMeters,
    int? minAge,
    int? maxAge,
    String? gender,
    int limit = 20, // Add limit
    // int offset = 0, // For pagination later
  }) async {
    try {
      final usersData = await _supabaseService.searchUsersByLocation(
          latitude: latitude,
          longitude: longitude,
          radiusMeters: radiusMeters,
          minAge: minAge,
          maxAge: maxAge,
          gender: gender,
          limit: limit, // Pass limit
          // offset: offset, // Pass offset later
      );

      // Use helper method to map the dynamic list to User objects
      return right(_mapDynamicListToUserList(usersData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> searchUsersByName(String query) async {
    if (query.trim().isEmpty) {
      return right([]); // Return empty list if query is empty after trim
    }

    try {
      // Create a direct query to the database for name/username search
      // No limit parameter passed from ViewModel for simple autocomplete
      final usersData = await _supabaseService.searchUsersByName(query);

      // Use helper method to map the dynamic list to User objects
      return right(_mapDynamicListToUserList(usersData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      print("Error in UserRepository.searchUsersByName: $e");
      return left(
          DatabaseFailure("Failed to search users by name: ${e.toString()}"));
    }
  }

  Future<Either<Failure, List<User>>> searchUsersByInterests({
    int? minAge,
    int? maxAge,
    String? gender,
    String? distanceFilter,
    List<String>? sharedActivities,
    List<String>? keywords,
    GeoPoint? currentUserLocation,
    String? currentUserCountry,
    int limit = 20, // Add limit parameter, pass to service
    // int offset = 0, // For pagination later
  }) async {
    try {
      final usersData = await _supabaseService.searchUsersByInterests(
        minAge: minAge,
        maxAge: maxAge,
        gender: gender,
        distanceFilter: distanceFilter,
        sharedActivities: sharedActivities,
        keywords: keywords,
        currentUserLocation: currentUserLocation,
        currentUserCountry: currentUserCountry,
        limit: limit, // Pass limit
        // offset: offset, // Pass offset later
      );

       // Use helper method to map the dynamic list to User objects
      return right(_mapDynamicListToUserList(usersData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      print("Error in UserRepository.searchUsersByInterests: $e"); // Log error
      return left(DatabaseFailure("Failed to search users by interests: ${e.toString()}"));
    }
  }

  // Helper to map dynamic list to User list with error handling for individual items
  List<User> _mapDynamicListToUserList(List<dynamic> dataList) {
    return dataList
        .map((data) {
          try {
            return User.fromJson(data as Map<String, dynamic>);
          } catch (e) {
            print("Error parsing user data in searchUsers: $e");
            print("Problematic User Data: $data");
            // Return null for problematic data, which will be filtered out
            return null;
          }
        })
        .nonNulls
        .toList(); // Filter out any potential nulls from parsing errors
  }

// --------- Friendships ----------
  Future<Either<Failure, List<String>>> getFriendIds(String userId) async {
    try {
      final friendIds = await _supabaseService.getFriendIds(userId);
      return right(friendIds);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> getFriends(String userId) async {
    try {
      final friendIds = await _supabaseService.getFriendIds(userId);
      if (friendIds.isEmpty) {
        return right([]); // Return an empty list if no friends.
      }
      final users = await _supabaseService.getUsers(friendIds);
      return right(users.map((data) => User.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, bool>> areUsersFriends(
      String userId1, String userId2) async {
    try {
      final result = await _supabaseService.areUsersFriends(userId1, userId2);
      return right(result); // Simply return the bool
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, String?>> getFriendshipStatus(
      String currentUserId, String profileUserId) async {
    try {
      final status = await _supabaseService.getFriendshipStatus(
          currentUserId, profileUserId);
      return right(status);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Friendship?>> getFriendshipByIds(
      String userAId, String userBId) async {
    try {
      final friendshipData =
          await _supabaseService.getFriendship(userAId, userBId);
      if (friendshipData == null) return right(null);
      return right(friendshipData);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> sendFriendRequest(
      String senderUserId, String recipientUserId) async {
    try {
      final friendship = Friendship(
          friendshipId:
              '${senderUserId}_$recipientUserId', // Construct friendship ID
          userAId: senderUserId,
          userBId: recipientUserId,
          statusAtoB: 'pending',
          statusBtoA: 'received',
          createdAt: DateTime.now(),
          requestSentAt: DateTime.now());
      await _supabaseService.insertFriendship(friendship);
      return right(unit); // Use 'unit' for void success (fpdart convention)
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> acceptFriendRequest(
      String currentUserId, String senderUserId) async {
    try {
      // First, try to get the existing friendship record, regardless of order
      Friendship? existingFriendship =
          await _supabaseService.getFriendship(currentUserId, senderUserId);
      existingFriendship ??=
          await _supabaseService.getFriendship(senderUserId, currentUserId);

      if (existingFriendship == null) {
        // Handle the case where no friendship record exists.
        return left(DatabaseFailure("No friend request found."));
      }
      // Determine the correct userA and userB based on existing record
      String userAId = existingFriendship.userAId;
      String userBId = existingFriendship.userBId;
      final updatedFriendship = existingFriendship.copyWith(
        statusAtoB: 'accepted',
        statusBtoA: 'accepted',
      );

      await _supabaseService.updateFriendship(updatedFriendship);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> declineFriendRequest(
      String currentUserId, String senderUserId) async {
    try {
      //First check which order to delete
      Friendship? existingFriendship =
          await _supabaseService.getFriendship(currentUserId, senderUserId);
      existingFriendship ??=
          await _supabaseService.getFriendship(senderUserId, currentUserId);
      if (existingFriendship == null) {
        // Handle the case where no friendship record exists.  You could log this.
        return left(DatabaseFailure("No friend request found to decline."));
      }
      await _supabaseService.deleteFriendship(existingFriendship.friendshipId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> cancelFriendRequest(
      String senderUserId, String recipientUserId) async {
    try {
      //First check which order to delete
      Friendship? existingFriendship =
          await _supabaseService.getFriendship(senderUserId, recipientUserId);
      existingFriendship ??=
          await _supabaseService.getFriendship(recipientUserId, senderUserId);

      if (existingFriendship == null) {
        return left(DatabaseFailure("No friend request found to cancel."));
      }
      await _supabaseService.deleteFriendship(existingFriendship.friendshipId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// --------- Blocked Users ----------
  Future<Either<Failure, Unit>> blockUser(
      String blockerId, String blockedId) async {
    try {
      // First, try to get the existing friendship record, regardless of order
      Friendship? existingFriendship =
          await _supabaseService.getFriendship(blockerId, blockedId);
      existingFriendship ??=
          await _supabaseService.getFriendship(blockedId, blockerId);

      if (existingFriendship == null) {
        // If no existing friendship, INSERT a new one
        final newFriendship = Friendship(
            friendshipId: '${blockerId}_$blockedId',
            userAId: blockerId, // blocker is userA
            userBId: blockedId, // blocked is userB
            statusAtoB: 'blocked', // From blocker to blocked
            statusBtoA: 'blocked', // From blocked to blocker
            createdAt: DateTime.now(),
            requestSentAt: DateTime.now() // Set time
            );
        await _supabaseService.insertFriendship(newFriendship);
      } else {
        // Determine the correct userA and userB based on existing record
        String userAId = existingFriendship.userAId;
        String userBId = existingFriendship.userBId;

        String newStatusAtoB = existingFriendship.statusAtoB;
        String newStatusBtoA = existingFriendship.statusBtoA;

        if (userAId == blockerId) {
          // blockerId is userA
          newStatusAtoB = 'blocked';
        } else {
          // blockerId is userB
          newStatusBtoA = 'blocked';
        }
        final updatedFriendship = existingFriendship.copyWith(
          statusAtoB: newStatusAtoB,
          statusBtoA: newStatusBtoA,
        );

        await _supabaseService.updateFriendship(updatedFriendship);
      }

      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> unblockUser(
      String currentUserId, String blockedUserId) async {
    try {
      // First, try to get the existing friendship record, regardless of order
      Friendship? existingFriendship =
          await _supabaseService.getFriendship(currentUserId, blockedUserId);
      existingFriendship ??=
          await _supabaseService.getFriendship(blockedUserId, currentUserId);

      if (existingFriendship == null) {
        // If there is no existing friendship record, there's nothing to unblock.  This isn't necessarily an error
        return right(
            unit); // Return success, as the user is *effectively* unblocked.
      }

      // Determine the correct userA and userB based on existing record
      String userAId = existingFriendship.userAId;
      String userBId = existingFriendship.userBId;
      String newStatusAtoB = existingFriendship.statusAtoB;
      String newStatusBtoA = existingFriendship.statusBtoA;

      if (userAId == currentUserId) {
        // currentUserId is userA, clear statusAtoB
        newStatusAtoB = 'none';
      } else {
        // currentUserId is userB, clear statusBtoA
        newStatusBtoA = 'none';
      }
      final updatedFriendship = existingFriendship.copyWith(
          statusAtoB: newStatusAtoB, statusBtoA: newStatusBtoA);

      await _supabaseService.updateFriendship(updatedFriendship);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> getBlockedUsers(
      String currentUserId) async {
    try {
      final usersData = await _supabaseService.getBlockedUsers(currentUserId);
      final users = usersData.map((data) => User.fromJson(data)).toList();
      return right(users);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// --------- Hidden Users ----------
  Future<Either<Failure, Unit>> hideUser(
      String hiderUserId, String hiddenUserId) async {
    try {
      await _supabaseService.hideUser(hiderUserId, hiddenUserId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> unhideUser(
      String currentUserId, String hiddenUserId) async {
    try {
      await _supabaseService.unhideUser(currentUserId, hiddenUserId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> getHiddenUsers(String hiderUserId) async {
    try {
      final usersData = await _supabaseService.getHiddenUsers(hiderUserId);
      final users = usersData.map((data) => User.fromJson(data)).toList();
      return right(users);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, bool>> isUserHidden(
      String currentUserId, String userId) async {
    try {
      final result = await _supabaseService.isUserHidden(currentUserId, userId);
      return right(result); // Simply return the boolean
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// --------- Saved Users ----------
  Future<Either<Failure, List<User>>> getSavedUsers(String saverUserId) async {
    try {
      final usersData = await _supabaseService.getSavedUsers(saverUserId);
      final users = usersData.map((data) => User.fromJson(data)).toList();
      return right(users);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, bool>> isUserSaved(
      String currentUserId, String userId) async {
    try {
      final result = await _supabaseService.isUserSaved(currentUserId, userId);
      return right(result);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> saveUser(
      String saverUserId, String savedUserId) async {
    try {
      final savedUser = SavedUser(
          savedUserId: '${saverUserId}_$savedUserId',
          saverUserId: saverUserId,
          savedUser: savedUserId,
          savedAt: DateTime.now());
      await _supabaseService.saveUser(savedUser);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> unsaveUser(
      String saverUserId, String savedUserId) async {
    try {
      await _supabaseService.unsaveUser(saverUserId, savedUserId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}
