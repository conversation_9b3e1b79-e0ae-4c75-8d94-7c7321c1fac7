// lib/core/services/location_service.dart
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:fpdart/fpdart.dart';
import 'package:permission_handler/permission_handler.dart' as perm_handler;
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart' as geocoding;
import 'package:watermelon_draft/core/errors.dart';

class LocationService {
  Future<bool> checkService() async {
    // // --- TEMPORARY WORKAROUND FOR SIMULATOR/DEBUGGING ---
    // // Simulators (especially iOS) might not accurately report service status via permission_handler
    // // even when location is simulated via Features menu. Bypass check in debug mode.
    // // TODO: Remove this 'if (kDebugMode) return true;' before production testing/release!
    // if (kDebugMode) {
    //   // kDebugMode is true only during development builds
    //   print("⚠️ WARNING: Bypassing location service check in debug mode.");
    //   return true; // Assume enabled during debug runs on simulator
    // }
    // // --- <PERSON><PERSON> WOR<PERSON>ROUND ---

    try {
      // Use permission_handler to check the service status
      perm_handler.ServiceStatus serviceStatus =
          await perm_handler.Permission.location.serviceStatus;

      // Check if the service is enabled
      bool serviceEnabled = serviceStatus == perm_handler.ServiceStatus.enabled;

      if (!serviceEnabled) {
        // Location services are not enabled.
        // Log or handle this information. The UI layer will prompt the user.
        print("Location service is disabled. Status: $serviceStatus");
        return false;
      }
      return true; // Service is enabled
    } catch (e) {
      print("Error checking location service status: $e");
      return false; // Assume service is not available if check fails
    }
  }

  Future<bool> checkPermission() async {
    // // --- TEMPORARY WORKAROUND FOR SIMULATOR/DEBUGGING ---
    // // Simulators (especially iOS) can be unreliable with repeated permission dialogs.
    // // Assume permission is granted during debug runs to unblock UI development.
    // // TODO: Remove this 'if (kDebugMode) return true;' before production testing/release!
    // if (kDebugMode) {
    //   print("⚠️ WARNING: Bypassing location permission check in debug mode.");
    //   return true; // Assume granted during debug runs
    // }
    // // --- END WORKAROUND ---

    // try {
    //   final status = await perm_handler.Permission.location.status;
    //   // Consider limited sufficient for getting location
    //   return status.isGranted || status.isLimited;
    // } catch (e) {
    //   print("Error checking location permission: $e");
    //   return false;
    // }

    // ------ original code ------
    // Gets the current status without prompting the user
    var status = await perm_handler.Permission.location.status;
    // Returns true ONLY if the status is explicitly granted
    return status == perm_handler.PermissionStatus.granted;
  }

  // --- requestPermission should ideally NOT be bypassed, ---
  // --- but it might not show the dialog on simulator anyway ---
  Future<bool> requestPermission() async {
    // try {
    //   final status = await perm_handler.Permission.location.request();
    //   return status.isGranted || status.isLimited;
    // } catch (e) {
    //   print("Error requesting location permission: $e");
    //   return false;
    // }

    // ------ original code ------
    // Requests permission if not already granted/denied permanently.
    // Shows the OS permission dialog to the user.
    var status = await perm_handler.Permission.location.request();
    // Returns true ONLY if the status AFTER the request is granted.
    return status == perm_handler.PermissionStatus.granted;
  }

  Future<bool> openAppSettings() async {
    // Renamed to match permission_handler, returns bool
    bool opened = await perm_handler
        .openAppSettings(); // Call the function from the package
    return opened;
  }

  Future<Either<Failure, GeoPoint>> getCurrentLocation() async {
    try {
      // 1. Check if location services are enabled on the device
      bool serviceEnabled = await checkService();
      if (!serviceEnabled) {
        print("Location service disabled.");
        return left(
            LocationServiceDisabledFailure()); // Return specific failure
      }

      // 2. Check if the app has permission
      bool hasPermission = await checkPermission();
      if (!hasPermission) {
        print("Location permission not granted initially.");
        // Attempt to request permission if not granted
        hasPermission = await requestPermission();
        if (!hasPermission) {
          print("Location permission denied after request.");
          // Check if denial was permanent
          final status = await perm_handler.Permission.location.status;
          return left(LocationPermissionDeniedFailure(
              isPermanentlyDenied:
                  status.isPermanentlyDenied)); // Return specific failure
        }
      }

      // 3. Get current position using geolocator
      final LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high, // Using LocationSettings as corrected
      );
      Position position = await Geolocator.getCurrentPosition(
          locationSettings: locationSettings);

      // 4. Convert to GeoPoint and return success
      return right(
          GeoPoint(latitude: position.latitude, longitude: position.longitude));
    } on TimeoutException {
      // Catch specific geolocator timeout
      print("Location request timed out.");
      return left(LocationTimeoutFailure());
    }
    // Catch specific geolocator exceptions (though handled by checkService/checkPermission above mostly)
    on LocationServiceDisabledException {
      print("Location services are disabled (geolocator exception).");
      return left(LocationServiceDisabledFailure());
    }
    // Note: PermissionDeniedException might be less likely here now due to upfront checks
    on PermissionDeniedException catch (e) {
      print("Location permission denied (geolocator exception): ${e.message}");
      // Check status again to be sure about permanent denial
      final status = await perm_handler.Permission.location.status;
      return left(LocationPermissionDeniedFailure(
          isPermanentlyDenied: status.isPermanentlyDenied));
    } catch (e) {
      print("Error getting current location: $e");
      return left(LocationFailure(
          "An unexpected error occurred: ${e.toString()}")); // Use generic LocationFailure
    }
  }

  Future<Either<Failure, geocoding.Placemark?>> reverseGeocode(
      GeoPoint point) async {
    try {
      List<geocoding.Placemark> placemarks =
          await geocoding.placemarkFromCoordinates(
        point.latitude,
        point.longitude,
        // localeIdentifier: "en_US" // Optional: Specify locale
      );
      // Return the first placemark if found, otherwise null, wrapped in Right
      return right(placemarks.isNotEmpty ? placemarks.first : null);
    } on PlatformException catch (e) {
      // Catch platform exceptions from geocoding
      print(
          "PlatformException during reverse geocoding: ${e.code} - ${e.message}");
      return left(GeocodingFailure("Platform error: ${e.message}"));
    } catch (e) {
      print("Error during reverse geocoding: $e");
      return left(GeocodingFailure(
          "An unexpected error occurred during reverse geocoding."));
    }
  }

  Future<Either<Failure, String?>> getCountryFromLocation(
      GeoPoint location) async {
    try {
      // 1. Call the refactored reverseGeocode which returns Either
      final placemarkResult = await reverseGeocode(location);

      // 2. Handle the Either result using fold
      return placemarkResult.fold(
        (failure) {
          // If reverseGeocode failed, propagate the failure
          return left(failure);
        },
        (placemark) {
          // If reverseGeocode succeeded, check the placemark
          if (placemark == null) {
            // No placemark found for coordinates
            return right(null);
          } else {
            // Return the country from the placemark
            return right(placemark.country);
          }
        },
      );
    } catch (e) {
      print("Unexpected error in getCountryFromLocation: $e");
      return left(
          LocationFailure("Failed to determine country from location."));
    }
  }

  Future<Either<Failure, String?>> getCityFromLocation(
      GeoPoint location) async {
    try {
      // 1. Call the refactored reverseGeocode which returns Either
      final placemarkResult = await reverseGeocode(location);

      // 2. Handle the Either result using fold
      return placemarkResult.fold(
        (failure) {
          // If reverseGeocode failed, propagate the failure
          return left(failure);
        },
        (placemark) {
          // If reverseGeocode succeeded, check the placemark
          if (placemark == null) {
            // No placemark found for coordinates
            return right(null);
          } else {
            // Extract city, using locality or fallback to subAdministrativeArea
            final city = placemark.locality ?? placemark.subAdministrativeArea;
            return right(city); // Returns city name or null if both are null
          }
        },
      );
    } catch (e) {
      print("Unexpected error in getCityFromLocation: $e");
      return left(LocationFailure("Failed to determine city from location."));
    }
  }

  Future<Either<Failure, (GeoPoint?, String?)>> getLocationAndCountryFromCity(
      String city) async {
    try {
      // 1. Get locations (coordinates) from city name using geocoding
      List<geocoding.Location> locations =
          await geocoding.locationFromAddress(city);

      if (locations.isEmpty) {
        // City not found or geocoding returned no results
        return right((null, null)); // Success, but no data found
      }

      // 2. Convert the first location result to GeoPoint
      final geocodingLocation = locations.first;
      final geoPoint = GeoPoint(
          latitude: geocodingLocation.latitude,
          longitude: geocodingLocation.longitude);

      // 3. Get the country using the refactored getCountryFromLocation
      final countryResult = await getCountryFromLocation(geoPoint);

      // 4. Handle the Either result from getCountryFromLocation
      return countryResult.fold(
        (failure) {
          // If getting country failed, return that failure
          // We still have the GeoPoint, but maybe country is important too?
          // For now, let's propagate the failure. Alternatively, return right((geoPoint, null))?
          // Let's propagate failure for consistency.
          print("Failed to get country after getting location: $failure");
          return left(failure);
        },
        (country) {
          // Success! Return both GeoPoint and country (country might be null)
          return right((geoPoint, country));
        },
      );
    } on PlatformException catch (e) {
      // Catch platform exceptions from geocoding
      print(
          "PlatformException getting location from city: ${e.code} - ${e.message}");
      return left(GeocodingFailure("Platform error: ${e.message}"));
    } catch (e) {
      print("Error getting location/country from city: $e");
      return left(LocationFailure(
          "An unexpected error occurred getting location from city."));
    }
  }

  Future<Either<Failure, List<SearchInfo>>> getCitySuggestions(String query,
      {int limit = 5}) async {
    try {
      if (query.length < 2) {
        // Avoid searching for very short strings
        return right([]); // Return empty list successfully
      }

      // Sanitize the query to avoid API issues
      String sanitizedQuery = query.trim();
      if (sanitizedQuery.isEmpty) {
        return right([]);
      }

      print(
          "LocationService: Fetching city suggestions for query: '$sanitizedQuery'");

      // Add a small delay to avoid rate limiting
      await Future.delayed(const Duration(milliseconds: 300));

      // Use flutter_osm_plugin's top-level addressSuggestion function
      List<SearchInfo> suggestions = await addressSuggestion(
        sanitizedQuery,
        limitInformation: limit, // Use the limit parameter
      );
      print("LocationService: Received ${suggestions.length} suggestions.");

      // Filter to prioritize city-like results
      List<SearchInfo> filteredSuggestions = suggestions.where((suggestion) {
        // Prioritize results that have city information
        final address = suggestion.address;
        return address != null &&
            (address.city != null || address.name != null);
      }).toList();

      return right(
          filteredSuggestions.isEmpty ? suggestions : filteredSuggestions);
    } on PlatformException catch (e, s) {
      print(
          "LocationService: PlatformException fetching city suggestions: ${e.code} - ${e.message}\n$s");
      return left(GeocodingFailure(
          "Platform error fetching suggestions. Please check network or try again."));
    } on TypeError catch (e, s) {
      print(
          "LocationService: TypeError fetching/processing city suggestions (likely plugin internal issue): $e\n$s");
      return left(LocationFailure(
          "Error processing location data. Please try a different search term."));
    } catch (e, s) {
      print("LocationService: Unknown error fetching city suggestions: $e\n$s");

      // Check if it's a network/HTTP error by examining the error string
      String errorString = e.toString().toLowerCase();
      if (errorString.contains('400') || errorString.contains('bad request')) {
        return left(LocationFailure(
            "Invalid search query. Please try a different city name."));
      } else if (errorString.contains('429') ||
          errorString.contains('rate limit')) {
        return left(LocationFailure(
            "Too many requests. Please wait a moment and try again."));
      } else if (errorString.contains('network') ||
          errorString.contains('connection')) {
        return left(LocationFailure(
            "Network error fetching city suggestions. Please check your connection."));
      } else {
        return left(LocationFailure(
            "An unexpected error occurred fetching city suggestions."));
      }
    }
  }
}
