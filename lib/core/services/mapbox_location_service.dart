// lib/core/services/mapbox_location_service.dart
import 'package:fpdart/fpdart.dart';
import 'package:mapbox_search/mapbox_search.dart';
import 'package:mapbox_search/models/location.dart';
import 'package:watermelon_draft/core/errors.dart';

/// MapBox-based location service for reliable city search and geocoding
/// Replaces the problematic flutter_osm_plugin implementation
class MapBoxLocationService {
  late final GeoCoding _geocoding;
  
  /// Initialize with MapBox API key
  /// Call this once in your app initialization
  static void initialize(String apiKey) {
    MapBoxSearch.init(apiKey);
  }
  
  MapBoxLocationService({
    String? apiKey,
    String? country,
    int limit = 5,
    List<PlaceType> types = const [PlaceType.place, PlaceType.address],
  }) {
    _geocoding = GeoCoding(
      apiKey: apiKey, // Optional if set globally
      country: country,
      limit: limit,
      types: types,
    );
  }

  /// Get city suggestions for autocomplete
  /// Much more reliable than flutter_osm_plugin's addressSuggestion
  Future<Either<Failure, List<MapBoxPlace>>> getCitySuggestions(
    String query, {
    int? limit,
    String? country,
    Location? proximity,
  }) async {
    try {
      if (query.length < 2) {
        // Return empty list for short queries
        return right([]);
      }

      // Create geocoding instance with specific parameters if provided
      final geocoding = (limit != null || country != null) 
          ? GeoCoding(
              country: country ?? _geocoding.country,
              limit: limit ?? _geocoding.limit,
              types: [PlaceType.place, PlaceType.address],
            )
          : _geocoding;

      print("MapBoxLocationService: Searching for '$query'");
      
      // Make the API call
      final ApiResponse<List<MapBoxPlace>> response = await geocoding.getPlaces(
        query,
        proximity: proximity ?? const NoProximity(),
      );

      // Handle the response using fold
      return response.fold(
        (success) {
          print("MapBoxLocationService: Found ${success?.length ?? 0} results");
          return right(success ?? []);
        },
        (failure) {
          print("MapBoxLocationService: API error: ${failure?.message}");
          return left(LocationFailure(
            failure?.message ?? "Failed to fetch city suggestions"
          ));
        },
      );
    } catch (e, s) {
      print("MapBoxLocationService: Unexpected error: $e\n$s");
      return left(LocationFailure("An unexpected error occurred: $e"));
    }
  }

  /// Convert MapBoxPlace to a format compatible with existing code
  /// This helps with migration from flutter_osm_plugin
  MapBoxPlaceInfo convertToPlaceInfo(MapBoxPlace place) {
    return MapBoxPlaceInfo(
      placeName: place.placeName ?? place.text ?? 'Unknown',
      city: _extractCityName(place),
      country: _extractCountryName(place),
      coordinates: place.center,
      fullAddress: place.placeName ?? place.text ?? '',
    );
  }

  /// Extract city name from MapBoxPlace
  String? _extractCityName(MapBoxPlace place) {
    // Try different sources for city name
    if (place.text != null && place.text!.isNotEmpty) {
      return place.text;
    }
    if (place.placeName != null) {
      // Extract city from "City, State, Country" format
      final parts = place.placeName!.split(',');
      if (parts.isNotEmpty) {
        return parts.first.trim();
      }
    }
    return null;
  }

  /// Extract country name from MapBoxPlace
  String? _extractCountryName(MapBoxPlace place) {
    if (place.placeName != null) {
      // Extract country from "City, State, Country" format
      final parts = place.placeName!.split(',');
      if (parts.length >= 2) {
        return parts.last.trim();
      }
    }
    // Fallback to shortCode if available
    return place.properties?.shortCode;
  }

  /// Reverse geocoding - get address from coordinates
  Future<Either<Failure, MapBoxPlace?>> getAddressFromCoordinates(
    Location location
  ) async {
    try {
      print("MapBoxLocationService: Reverse geocoding for ${location.lat}, ${location.long}");
      
      final ApiResponse<List<MapBoxPlace>?> response = 
          await _geocoding.getAddress(location);

      return response.fold(
        (success) {
          final places = success ?? [];
          print("MapBoxLocationService: Found ${places.length} addresses");
          return right(places.isNotEmpty ? places.first : null);
        },
        (failure) {
          print("MapBoxLocationService: Reverse geocoding error: ${failure?.message}");
          return left(LocationFailure(
            failure?.message ?? "Failed to get address from coordinates"
          ));
        },
      );
    } catch (e, s) {
      print("MapBoxLocationService: Reverse geocoding unexpected error: $e\n$s");
      return left(LocationFailure("An unexpected error occurred: $e"));
    }
  }

  /// Get coordinates from city name
  Future<Either<Failure, Location?>> getCoordinatesFromCity(String cityName) async {
    try {
      final response = await getCitySuggestions(cityName, limit: 1);
      
      return response.fold(
        (places) {
          if (places.isNotEmpty && places.first.center != null) {
            return right(places.first.center);
          }
          return right(null);
        },
        (failure) => left(failure),
      );
    } catch (e) {
      return left(LocationFailure("Failed to get coordinates: $e"));
    }
  }
}

/// Helper class to bridge MapBox data with existing code
class MapBoxPlaceInfo {
  final String placeName;
  final String? city;
  final String? country;
  final Location? coordinates;
  final String fullAddress;

  const MapBoxPlaceInfo({
    required this.placeName,
    this.city,
    this.country,
    this.coordinates,
    required this.fullAddress,
  });

  @override
  String toString() => fullAddress;
}
