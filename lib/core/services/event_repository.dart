// lib/core/services/event_repository.dart
import 'package:uuid/uuid.dart';
import 'package:watermelon_draft/core/models/chat_room.dart';
import 'package:watermelon_draft/core/models/chat_rooms_users.dart';
import 'package:watermelon_draft/core/models/event.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:image_picker/image_picker.dart'; // If you're handling image uploads here
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/models/attendee.dart';
import 'package:supabase_flutter/supabase_flutter.dart'
    as supabase; // Use prefix for clarity
import 'package:watermelon_draft/core/models/category.dart';

class EventRepository {
  final SupabaseService _supabaseService;

  EventRepository(this._supabaseService);

  Future<Either<Failure, List<Event>>> getEventsByLocation({
    required double latitude,
    required double longitude,
    required double radiusMeters,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final eventsData = await _supabaseService.getEventsByLocation(
        latitude: latitude,
        longitude: longitude,
        radiusMeters: radiusMeters,
        startDate: startDate,
        endDate: endDate,
      );
      // Convert the raw data to a List<Event>
      return right(eventsData.map((data) => Event.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      // Catch specific exception
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Event?>> getEventById(String eventId) async {
    try {
      final eventData = await _supabaseService.getEventById(eventId);
      if (eventData == null) {
        return right(null); // Return null if event not found
      }
      return right(Event.fromJson(eventData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, int>> getAttendeeCount(String eventId) async {
    try {
      final count = await _supabaseService.getAttendeeCount(eventId);
      return right(count);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> getAttendees(String eventId) async {
    try {
      final List<dynamic> result = await _supabaseService.getAttendees(eventId);
      final users = result.map((e) => User.fromJson(e)).toList();
      return right(users); // Filter and return
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, bool>> isUserAttendingEvent(
      String userId, String eventId) async {
    try {
      final result =
          await _supabaseService.isUserAttendingEvent(userId, eventId);
      return right(result);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> updateEvent(Event updatedEvent) async {
    try {
      await _supabaseService.updateEvent(updatedEvent);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> deleteEvent(String eventId) async {
    try {
      await _supabaseService.deleteEvent(eventId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, String>> uploadEventImage(
      String eventId, XFile imageFile) async {
    try {
      final String? imageUrl = await _supabaseService.uploadEventImage(
          eventId, imageFile); // Upload and get URL
      if (imageUrl == null) {
        return left(DatabaseFailure("Image upload failed"));
      }
      return right(imageUrl);
    } on supabase.PostgrestException catch (e) {
      // Catch specific exception
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Event>> createEvent(
      Event newEvent, String? imageUrl) async {
    try {
      // 1. Add imageUrl to event
      if (imageUrl != null) {
        newEvent = newEvent.copyWith(imageUrl: imageUrl);
      }

      // 2. Create ChatRoom
      final chatRoom = ChatRoom(
          chatRoomId: const Uuid().v4(),
          eventId: newEvent.eventId,
          createdAt: DateTime.now(),
          creatorId: newEvent.creatorId // Pass the creatorId
          );

      final chatRoomResult = await _supabaseService.createChatRoom(chatRoom);

      // 3. Add chatRoomId to newEvent
      newEvent = newEvent.copyWith(chatRoomId: chatRoomResult['chat_room_id']);

      // 4. Save the event to Supabase (using EventRepository)
      final eventData = await _supabaseService.createEvent(newEvent);
      if (eventData == null) {
        return left(DatabaseFailure('Failed to create event'));
      }

      // 5. Create ChatRoomUser
      //Add the creator to the users list
      await _supabaseService.addUserToChatRoom(
        ChatRoomsUsers(
          chatRoomsUsersId: const Uuid().v4(),
          chatRoomId: newEvent.chatRoomId!,
          userId: newEvent.creatorId,
        ),
      );

      return right(Event.fromJson(eventData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<(Event, DateTime?)>>> getMyEventsWithLastActivity(
      String userId) async {
    try {
      final List<dynamic> result = await _supabaseService
          .getMyEventsWithNotificationCounts(userId); // to do
      // Convert the result into List
      final List<(Event, DateTime?)> eventsWithLastActivity = [];
      for (final item in result) {
        // Ensure that 'last_activity' can be null and is parsed correctly
        DateTime? lastActivity = item['last_activity'] != null
            ? DateTime.parse(item['last_activity'])
            : null; // Can be null

        eventsWithLastActivity
            .add((Event.fromJson(item), lastActivity)); // Convert and add.
      }
      return right(eventsWithLastActivity);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, int>> getUnreadEventNotificationCountForUserEvents(
      String userId) async {
    try {
      final count = await _supabaseService
          .getUnreadEventNotificationCountForUserEvents(userId);
      return right(count);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> joinEvent(String userId, String eventId) async {
    try {
      // 1. Check if the user is already attending, AND get their join_count
      final existingAttendee =
          await _supabaseService.getAttendee(userId, eventId);

      // 2. Check the join count
      if (existingAttendee != null) {
        if (existingAttendee.joinCount >= 2) {
          return left(DatabaseFailure(
              'You have reached the maximum number of times you can join this event.'));
        }
      }

      // 3.  Proceed with joining/rejoining
      if (existingAttendee == null) {
        // Not an attendee yet.
        final attendee = Attendee(
            attendeeId: const Uuid().v4(),
            userId: userId,
            eventId: eventId); //join count default is 1
        await _supabaseService.insertAttendee(attendee); // implement in service
      } else {
        // Already an attendee, increment join_count
        final updatedAttendee = existingAttendee.copyWith(
            joinCount: existingAttendee.joinCount + 1);
        await _supabaseService
            .updateAttendee(updatedAttendee); // Implement in SupabaseService
      }

      // 4. Add the user to the event's chat room.  This is the NEW part.
      final eventResult = await getEventById(eventId); // Get the event
      return await eventResult.fold((failure) => left(failure), (event) async {
        if (event != null && event.chatRoomId != null) {
          // Make sure event and chat room id
          await _supabaseService.addUserToChatRoom(
            ChatRoomsUsers(
              chatRoomsUsersId: const Uuid().v4(),
              chatRoomId: event.chatRoomId!,
              userId: userId,
            ),
          ); // Add to chat room
        }
        return right(unit); // Success
      });
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> leaveEvent(
      String userId, String eventId) async {
    try {
      // 1. Get the event to find the chat room ID
      final eventResult = await getEventById(eventId);
      return await eventResult.fold((failure) => left(failure), (event) async {
        // Get chatroom id from Event object
        if (event != null && event.chatRoomId != null) {
          // 2. Remove the user from the chat room
          await _supabaseService.removeUserFromChatRoom(
              event.chatRoomId!, userId);
          // 3. Remove the user from the Attendees table.
          await _supabaseService.leaveEvent(userId, eventId); // Existing
          return right(unit); // Success
        } else {
          return left(DatabaseFailure('Could not find event or chat room.')); //
        }
      });
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// Helper Methods for My Events Subsets
  Future<Either<Failure, List<Event>>> getHostedEvents(String userId) async {
    try {
      final eventsData = await _supabaseService.getHostedEvents(userId);
      return right(eventsData.map((data) => Event.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<Event>>> getAttendingEvents(String userId) async {
    try {
      final eventsData = await _supabaseService.getAttendingEvents(userId);
      return right(eventsData.map((data) => Event.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<Event>>> getDraftEvents(String userId) async {
    try {
      final eventsData = await _supabaseService.getDraftEvents(userId);
      return right(eventsData.map((data) => Event.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<Event>>> getPastEvents(String userId) async {
    try {
      final eventsData = await _supabaseService.getPastEvents(userId);
      return right(eventsData.map((data) => Event.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  // Add get categories
  Future<Either<Failure, List<Category>>> getCategories() async {
    try {
      final categoriesData = await _supabaseService.getCategories();
      return right(
          categoriesData.map((data) => Category.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      // Catch specific exception
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}
