// lib/core/services/chat_repository.dart
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:uuid/uuid.dart';
import 'package:watermelon_draft/core/models/chat_room.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/chat_rooms_users.dart';
import 'package:watermelon_draft/core/models/message.dart';

class ChatRepository {
  final SupabaseService _supabaseService;

  ChatRepository(this._supabaseService);

  Future<Either<Failure, List<ChatRoom>>> getChatRoomsForUser(
      String userId) async {
    try {
      final chatRooms = await _supabaseService.getChatRoomsForUser(userId);
      return right(chatRooms.map((data) => ChatRoom.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Message?>> getLastMessageForChatRoom(
      String chatRoomId) async {
    try {
      final message =
          await _supabaseService.getLastMessageForChatRoom(chatRoomId);
      if (message == null) {
        return right(null);
      }
      return right(Message.fromJson(message));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, int>> getUnreadMessageCount(
      String chatRoomId, String userId) async {
    try {
      final count =
          await _supabaseService.getUnreadMessageCount(chatRoomId, userId);
      return right(count);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, ChatRoomsUsers?>> getChatRoomUser(
      String chatRoomId, String userId) async {
    try {
      final result = await _supabaseService.getChatRoomUser(chatRoomId, userId);
      if (result == null) {
        return right(null);
      }
      return right(ChatRoomsUsers.fromJson(result));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, String?>> getOrCreateChatRoom(
      String currentUserId, String otherUserId) async {
    try {
      // 1. Try to find an existing one-on-one chat room
      final existingChatRoom = await _supabaseService.getOneOnOneChatRoom(
          currentUserId, otherUserId);

      if (existingChatRoom != null) {
        // Chat room exists, return its ID
        return right(existingChatRoom.chatRoomId);
      } else {
        // 2. Chat room doesn't exist.  Create a new one.
        final newChatRoom = ChatRoom(
          chatRoomId: const Uuid().v4(), // Generate unique ID
          createdAt: DateTime.now(),
          creatorId: currentUserId,
        );

        final createResult = await _supabaseService.createChatRoom(newChatRoom);
        //Add error handling
        return createResult.fold((l) {
          return left(DatabaseFailure("Failed to create chat room"));
        }, (r) {
          return right(ChatRoom.fromJson(r).chatRoomId); //Return id
        });
      }
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> updateChatRoomUser(
      ChatRoomsUsers updatedChatRoomUser) async {
    try {
      await _supabaseService.updateChatRoomUser(
          updatedChatRoomUser); // Implement in SupabaseService
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> sendMessage(String senderUserId,
      String recipientUserId, String messageText, String chatRoomId) async {
    try {
      // 1. Check for blocking (using existing areUsersFriends method - assuming you have this)
      final areFriends =
          await _supabaseService.areUsersFriends(senderUserId, recipientUserId);
      if (!areFriends) {
        // if not friend, create a notification that route to Discover page
        // Handle blocked scenario.  Don't send the message.
        return left(BlockedUserFailure(
            "Cannot send message to a non-friend user.")); // Use a custom Failure type
      }

      // 2. Create the Message object
      final message = Message(
        messageId: const Uuid().v4(), // Generate unique ID
        chatRoomId: chatRoomId,
        senderId: senderUserId,
        messageText: messageText,
        createdAt: DateTime.now(),
      );

      // 3. Save the message to Supabase
      final insertResult = await _supabaseService.insertMessage(message);
      if (insertResult.isLeft()) {
        // If there's an error saving, return it
        return left(insertResult
            .getLeft()
            .getOrElse(() => DatabaseFailure("Failed to insert message.")));
      }
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, String?>> getOtherUserIdInChatRoom(
      String chatRoomId, String currentUserId) async {
    try {
      final otherUserId = await _supabaseService.getOtherUserIdInChatRoom(
          chatRoomId, currentUserId);
      return right(otherUserId); // Simply return the ID (or null)
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

   Future<Either<Failure, Unit>> addUserToChatRoom(
      String chatRoomId, String userId) async {
    try {
      final chatRoomsUsers = ChatRoomsUsers(
          chatRoomsUsersId: const Uuid().v4(),
          chatRoomId: chatRoomId,
          userId: userId);
      await _supabaseService.addUserToChatRoom(chatRoomsUsers); // Add user
      return right(unit); // Success
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> removeUserFromChatRoom(
      String chatRoomId, String userId) async {
    try {
      await _supabaseService.removeUserFromChatRoom(chatRoomId, userId);
      return right(unit); // Success
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}
