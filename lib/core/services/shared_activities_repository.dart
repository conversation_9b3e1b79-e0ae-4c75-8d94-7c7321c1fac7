// lib/core/services/shared_activities_repository.dart
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

class SharedActivitiesRepository {
  final SupabaseService _supabaseService;

  SharedActivitiesRepository(this._supabaseService);

  Future<Either<Failure, List<SharedActivity>>> getSharedActivities() async {
    try {
      final activitiesData = await _supabaseService.getSharedActivities();
      return right(
          activitiesData.map((data) => SharedActivity.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      print(
          "Repo: PostgrestException - Code: ${e.code}, Msg: ${e.message}, Details: ${e.details}");
      return left(PostgresqlFailure(e.code ?? 'PGRST_ERR_FALLBACK',
          e.message ?? 'A database error occurred while fetching data.'));
    } catch (e, s) {
      print("Repo: Unknown Exception - $e\n$s");
      return left(DatabaseFailure(e.toString().isNotEmpty
          ? e.toString()
          : 'An unknown error occurred fetching data.'));
    }
  }

  Future<Either<Failure, SharedActivity?>> getSharedActivityById(
      String activityId) async {
    try {
      final activityData =
          await _supabaseService.getSharedActivityById(activityId);
      if (activityData == null) {
        return right(null); // Or return a specific "not found" error
      }
      return right(SharedActivity.fromJson(activityData));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}
