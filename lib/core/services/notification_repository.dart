// lib/core/services/notification_repository.dart

import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/notification.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:uuid/uuid.dart';

class NotificationRepository {
  final SupabaseService _supabaseService;

  NotificationRepository(this._supabaseService);

  Future<Either<Failure, Unit>> createNotification(
      {required String userId,
      String? wishlistItemId,
      String? eventId,
      required String notificationType,
      required String content,
      String? relatedUserId,
      String? chatRoomId}) async {
    try {
      final notification = UserNotification(
          notificationId: const Uuid().v4(),
          userId: userId,
          wishlistItemId: wishlistItemId,
          eventId: eventId,
          notificationType: notificationType,
          content: content,
          isRead: false,
          createdAt: DateTime.now(),
          relatedUserId: relatedUserId,
          chatRoomId: chatRoomId);
      await _supabaseService.createNotification(notification.toJson());
      return right(unit); // Use fpdart's unit for void success
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> markNotificationAsRead(
      String notificationId) async {
    try {
      await _supabaseService.markNotificationAsRead(notificationId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> markAllNotificationsAsRead(
      String userId) async {
    try {
      await _supabaseService.markAllNotificationsAsRead(userId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<UserNotification>>> getUnreadNotifications(
      String userId) async {
    try {
      final notificationsData =
          await _supabaseService.getUnreadNotifications(userId);
      return right(notificationsData
          .map((data) => UserNotification.fromJson(data))
          .toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<UserNotification>>> getNotificationsForUser(
      String userId) async {
    try {
      final notificationsData =
          await _supabaseService.getNotificationsForUser(userId);
      return right(notificationsData
          .map((data) => UserNotification.fromJson(data))
          .toList()); // Convert
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, int>> getTotalUnreadNotificationCount(
      String userId) async {
    try {
      final count =
          await _supabaseService.getTotalUnreadNotificationCount(userId);
      return right(count);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> deleteNotification(
      String notificationId) async {
    try {
      await _supabaseService.deleteNotification(notificationId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}