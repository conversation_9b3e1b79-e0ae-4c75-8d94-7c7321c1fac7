// lib/core/services/wishlist_repository.dart
import 'package:watermelon_draft/core/models/chat_room.dart';
import 'package:watermelon_draft/core/models/chat_rooms_users.dart';
import 'package:watermelon_draft/core/models/wishlist_item.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/interested_user.dart';
import 'package:watermelon_draft/core/models/category.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:uuid/uuid.dart';

class WishlistRepository {
  final SupabaseService _supabaseService;

  WishlistRepository(this._supabaseService);

  Future<Either<Failure, WishlistItem>> createWishlistItem(
      WishlistItem newWishlistItem) async {
    try {
      // 1. Create ChatRoom
      final chatRoom = ChatRoom(
          chatRoomId: const Uuid().v4(),
          wishlistItemId: newWishlistItem.wishlistItemId,
          createdAt: DateTime.now(),
          creatorId: newWishlistItem.creatorId // Pass the creatorId
          );

      final chatRoomResult =
          await _supabaseService.createChatRoom(chatRoom); // Create chat room

      // 2. Add chatRoomId to newWishlistItem
      newWishlistItem = newWishlistItem.copyWith(
          chatRoomId: ChatRoom.fromJson(chatRoomResult).chatRoomId);

      // 3. Save the wishlist item to Supabase (using EventRepository)
      final wishlistItemData =
          await _supabaseService.createWishlistItem(newWishlistItem); // Create
      if (wishlistItemData == null) {
        return left(DatabaseFailure('Failed to create wishlist item'));
      }

      // 4. Create ChatRoomUser
      await _supabaseService.addUserToChatRoom(ChatRoomsUsers(
          chatRoomsUsersId: const Uuid().v4(),
          chatRoomId: newWishlistItem.chatRoomId!,
          userId: newWishlistItem.creatorId));
      return right(
          WishlistItem.fromJson(wishlistItemData)); // Success, return item
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, WishlistItem?>> getWishlistItemById(
      String itemId) async {
    try {
      final data =
          await _supabaseService.getWishlistItemById(itemId); // Implement this
      if (data == null) {
        return right(null); // Or return a specific "not found" error
      }
      return right(WishlistItem.fromJson(data));
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> updateWishlistItem(
      WishlistItem updatedWishlistItem) async {
    try {
      await _supabaseService.updateWishlistItem(updatedWishlistItem);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> deleteWishlistItem(
      String wishlistItemId) async {
    try {
      await _supabaseService.deleteWishlistItem(wishlistItemId);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<WishlistItem>>> getWishlistItemsByCreator(
      String creatorId) async {
    try {
      final data = await _supabaseService.getWishlistItemsByCreator(creatorId);
      return right(data.map((item) => WishlistItem.fromJson(item)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<WishlistItem>>> getWishlistItemsInterestedByUser(
      String userId) async {
    try {
      final data =
          await _supabaseService.getWishlistItemsInterestedByUser(userId);
      return right(data.map((item) => WishlistItem.fromJson(item)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<WishlistItem>>> getWishlistItemsLetsGoByUser(
      String userId) async {
    try {
      final data = await _supabaseService.getWishlistItemsLetsGoByUser(userId);
      return right(data.map((item) => WishlistItem.fromJson(item)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<WishlistItem>>> getPastWishlistItems(
      String userId) async {
    try {
      final data = await _supabaseService.getPastWishlistItems(userId);
      return right(data.map((item) => WishlistItem.fromJson(item)).toList());
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, InterestedUser?>> checkInterestedUser(
      String userId, String wishlistItemId) async {
    try {
      final result =
          await _supabaseService.checkInterestedUser(userId, wishlistItemId);
      if (result == null) return right(null);
      return right(result);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> insertInterestedUser(
      InterestedUser interestedUser) async {
    try {
      await _supabaseService.insertInterestedUser(interestedUser);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, Unit>> updateInterestedUser(
      InterestedUser interestedUser) async {
    try {
      await _supabaseService.updateInterestedUser(interestedUser);
      return right(unit);
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

// Inside WishlistRepository
  Future<Either<Failure, Unit>> removeInterestedUser(
      String userId, String wishlistItemId) async {
    try {
      // 1. Get wishlist item to find the chat room ID
      final wishlistItemResult =
          await getWishlistItemById(wishlistItemId); //Get chat id

      return await wishlistItemResult.fold((l) => left(l),
          (wishlistItem) async {
        if (wishlistItem != null && wishlistItem.chatRoomId != null) {
          //Check for null
          // 2. Remove user from chatroom
          await _supabaseService.removeUserFromChatRoom(
              wishlistItem.chatRoomId!, userId);

          // 3. Remove from interested user
          await _supabaseService.removeInterestedUser(userId, wishlistItemId);
          return right(unit);
        } else {
          return left(
              DatabaseFailure('Could not find wishlist item or chat room.'));
        }
      });
    } on supabase.PostgrestException catch (e) {
      // Catch specific exception
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<InterestedUser>>> getInterestedUsers(
      String wishlistItemId) async {
    try {
      final result = await _supabaseService.getInterestedUsers(wishlistItemId);
      return right(result
          .map((e) => InterestedUser.fromJson(e))
          .toList()); // Convert and return
    } on supabase.PostgrestException catch (e) {
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<Category>>> getCategories() async {
    try {
      final categoriesData = await _supabaseService.getCategories();
      return right(
          categoriesData.map((data) => Category.fromJson(data)).toList());
    } on supabase.PostgrestException catch (e) {
      // Catch specific exception
      return left(PostgresqlFailure(e.code ?? 'UNKNOWN', e.message));
    } catch (e) {
      return left(DatabaseFailure(e.toString()));
    }
  }
}
