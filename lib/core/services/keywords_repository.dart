// lib/core/services/keywords_repository.dart
import 'package:watermelon_draft/core/models/keyword.dart';
import 'package:watermelon_draft/core/services/supabase_service.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
// import 'package:collection/collection.dart';

class KeywordsRepository {
  final SupabaseService _supabaseService;

  KeywordsRepository(this._supabaseService);

  Future<Either<Failure, List<Keyword>>> getAllKeywords() async {
    try {
      final data = await _supabaseService.getAllKeywords();
      final keywordsList = data
          .map((item) {
            try {
              return Keyword.fromJson(item as Map<String, dynamic>);
            } catch (e) {
              print("Error parsing Keyword from JSON: $item, Error: $e");
              return null; // Handle potential parsing error for an individual item
            }
          })
          .nonNulls
          .toList(); // Filter out any nulls from parsing errors
      return right(keywordsList);
} on supabase.PostgrestException catch (e) {
      print(
          "Repo: PostgrestException - Code: ${e.code}, Msg: ${e.message}, Details: ${e.details}");
      return left(PostgresqlFailure(
          e.code ?? 'PGRST_ERR_FALLBACK',
          e.message ??
              'A database error occurred while fetching data.'
          ));
    } catch (e, s) {
      print("Repo: Unknown Exception - $e\n$s");
      return left(DatabaseFailure(e.toString().isNotEmpty
              ? e.toString()
              : 'An unknown error occurred fetching data.' // This is fine
          ));
    }
  }

  // Future<Either<Failure, List<Keyword>>> getAllKeywords() async {
  //   try {
  //     final data = await _supabaseService.getAllKeywords();
  //     print("DEBUG: Raw keywords data from Supabase: $data"); // Log raw data

  //     final keywordsList = data
  //         .map((itemMap) {
  //           final item = itemMap as Map<String, dynamic>;
  //           print("DEBUG: Parsing item: $item"); // Log each item
  //           print(
  //               "DEBUG: item['keyword_id'] is ${item['keyword_id']} (type: ${item['keyword_id'].runtimeType})");
  //           print(
  //               "DEBUG: item['keyword_text'] is ${item['keyword_text']} (type: ${item['keyword_text'].runtimeType})");
  //           print(
  //               "DEBUG: item['category'] is ${item['category']} (type: ${item['category'].runtimeType})");

  //           try {
  //             return Keyword.fromJson(item);
  //           } catch (e, s) {
  //             print(
  //                 "ERROR parsing Keyword from JSON: $item, Error: $e, Stack: $s");
  //             return null;
  //           }
  //         })
  //         .nonNulls
  //         .toList();

  //     print("DEBUG: Parsed keywords count: ${keywordsList.length}");
  //     for (var kw in keywordsList) {
  //       print(
  //           "DEBUG: Parsed Keyword: id=${kw.keywordId}, text=${kw.keywordText}, category=${kw.category}");
  //     }
  //     return right(keywordsList);
  //   } on supabase.PostgrestException catch (e) {
  //          print("KeywordsRepo: PostgrestException - ${e.message}");
  //     // Provide a default for null messages
  //     return left(PostgresqlFailure(
  //         e.code ?? 'PGRST_UNKNOWN', // Provide default for code too
  //         e.message ?? 'A database error occurred.' // Default message
  //         ));
  //   } catch (e, s) {
  //        print("KeywordsRepo: Unknown Exception - $e\n$s");
  //     return left(DatabaseFailure(e.toString()));
  //   }
  // }
}
