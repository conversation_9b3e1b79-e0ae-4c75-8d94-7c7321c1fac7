// lib/core/ui/widgets/profile_image_picker.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
// import 'package:cached_network_image/cached_network_image.dart'; // Import if using for initialImageUrl display

class ProfileImagePicker extends ConsumerStatefulWidget {
  final String? initialDefaultAvatarPath; // Renamed from initialImageUrl
  final XFile? initialImageFile; // Pass the initial XFile if resuming
  final Color? initialGeneratedColor;
  final String? initialAvatarType; // 'uploaded', 'default', 'generated'
  final String nameForGeneratedAvatar; // Name for initial generation

  // Callback for gallery/camera selection
  final Function(XFile? imageFile) onImageSelected;
  // Callback for default asset selection
  final Function(String? defaultAvatarPath) onDefaultAvatarSelected;
  final Function(Color color) onGeneratedAvatarSelected;
  // Optional callback for color change
  final Function(Color newColor)? onGeneratedAvatarColorRegenerated;

  final VoidCallback onUpdate;
  final VoidCallback onBack;
  final String updateButtonText;
  final String cancelButtonText;

  const ProfileImagePicker({
    super.key,
    this.initialDefaultAvatarPath,
    this.initialImageFile,
    this.initialGeneratedColor,
    this.initialAvatarType,
    required this.nameForGeneratedAvatar,
    required this.onImageSelected,
    required this.onDefaultAvatarSelected,
    required this.onGeneratedAvatarSelected,
    required this.onGeneratedAvatarColorRegenerated,
    required this.onUpdate,
    required this.onBack,
    required this.updateButtonText,
    required this.cancelButtonText,
  });

  @override
  ConsumerState<ProfileImagePicker> createState() => _ProfileImagePickerState();
}

class _ProfileImagePickerState extends ConsumerState<ProfileImagePicker> {
  final _picker = ImagePicker();
  XFile? _selectedImage; // User picked image via gallery/camera
  String? _selectedDefaultAvatarPath; // User picked default asset path

  // --- State for Generated Avatar ---
  late String _generatedAvatarInitials; // Initials won't change
  late Color _generatedAvatarColor; // Current color for preview
  // late bool _isSelectedGenerated; // Is generated avatar the current choice?

  // --- Default Avatar Asset Path ---
  static const String _defaultAvatarAssetPath =
      'assets/images/defaults/default_avatar.png';

  @override
  void initState() {
    super.initState();
    // Initialize local state based on parent's initial values from the widget
    _selectedImage = widget.initialImageFile;
    _selectedDefaultAvatarPath = widget.initialDefaultAvatarPath;
    _generatedAvatarInitials = getInitials(widget.nameForGeneratedAvatar);

    bool shouldSetInitialGenerated = false;

    // --- Initialize based on initialAvatarType and initialGeneratedColor ---
    if (widget.initialAvatarType == 'generated') {
      _generatedAvatarColor = widget.initialGeneratedColor ?? getRandomColor();
      // Ensure other selections are null if initial type is 'generated'
      _selectedImage = null;
      _selectedDefaultAvatarPath = null;
      shouldSetInitialGenerated = true;
    } else if (widget.initialAvatarType == 'uploaded' &&
        widget.initialImageFile != null) {
      // _selectedImage is already set from widget.initialImageFile
      _generatedAvatarColor = widget.initialGeneratedColor ??
          getRandomColor(); // Keep a fallback generated color
      _selectedDefaultAvatarPath = null;
    } else if (widget.initialAvatarType == 'default' &&
        widget.initialDefaultAvatarPath != null) {
      // _selectedDefaultAvatarPath is already set from widget.initialDefaultAvatarPath
      _generatedAvatarColor = widget.initialGeneratedColor ??
          getRandomColor(); // Keep a fallback generated color
      _selectedImage = null;
    } else {
      // Default to generated if no specific initial type is provided
      // This means _selectedImage and _selectedDefaultAvatarPath will be null from above
      _generatedAvatarColor = widget.initialGeneratedColor ?? getRandomColor();
      _selectedImage = null;
      _selectedDefaultAvatarPath = null;
      shouldSetInitialGenerated = true;
    }

    // If we are defaulting to showing a generated avatar, tell the parent ViewModel
    if (shouldSetInitialGenerated) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          print(
              "ProfileImagePicker: Initializing with generated avatar by default. Color: $_generatedAvatarColor");
          // This callback updates OnboardingState: avatarType='generated', generatedAvatarColor=_generatedAvatarColor
          widget.onGeneratedAvatarSelected(_generatedAvatarColor);
        }
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    XFile? image;
    CroppedFile? croppedFile; // Use CroppedFile type from image_cropper

    // --- Request Permission for Camera ---
    if (source == ImageSource.camera) {
      PermissionStatus cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        if (mounted) {
          String message = 'Camera permission is required to take a photo.';
          if (cameraStatus.isPermanentlyDenied) {
            message += ' Please enable it in app settings.';
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              action: cameraStatus.isPermanentlyDenied
                  ? SnackBarAction(
                      label: 'Settings', onPressed: openAppSettings)
                  : null,
            ),
          );
        }
        return; // Don't proceed if permission not granted
      }
    }

    try {
      image = await _picker.pickImage(source: source, imageQuality: 70);
      if (image == null) {
        print("Image picking cancelled by user or failed silently.");
        return; // User cancelled picker or plugin returned null
      }
      print("Image picked: ${image.path}");

      // --- Cropping ---
      croppedFile = await ImageCropper().cropImage(
        sourcePath: image.path,
        aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              toolbarColor: Colors.deepOrange,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.original,
              lockAspectRatio: true),
          IOSUiSettings(title: 'Crop Image', aspectRatioLockEnabled: true)
        ],
        compressQuality: 80,
        compressFormat: ImageCompressFormat.jpg,
      );

      if (croppedFile == null) {
        print("Image cropping cancelled by user.");
        return; // User cancelled cropper
      }
      print("Image cropped: ${croppedFile.path}");

      if (mounted) {
        final newImage = XFile(croppedFile.path);
        setState(() {
          _selectedImage = newImage;
          _selectedDefaultAvatarPath = null; // Clear other selections
        });
        widget.onImageSelected(newImage); // Call image callback
      }
    } on PlatformException catch (e, s) {
      // Catch platform-specific errors
      print(
          "PlatformException during image picking/cropping ($source): ${e.code} - ${e.message}\n$s");
      if (mounted) {
        String errorMessage = 'Could not select image. Please try again.';
        if (source == ImageSource.camera) {
          if (e.code == 'camera_access_denied' ||
              e.code == 'camera_unavailable') {
            errorMessage =
                'Could not access camera. Please check permissions or try on a real device.';
          } else if (e.message?.toLowerCase().contains("simulator") ?? false) {
            errorMessage = 'Camera functionality is limited on simulators.';
          }
        }
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text(errorMessage)));
      }
    } catch (e, s) {
      // Catch any other unexpected errors
      print("Unexpected error during image picking/cropping ($source): $e\n$s");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('An unexpected error occurred. Please try again.')),
        );
      }
    }
  }

  void _selectDefaultAvatar() {
    if (mounted) {
      setState(() {
        _selectedDefaultAvatarPath = _defaultAvatarAssetPath;
        _selectedImage = null;
      });
      widget.onDefaultAvatarSelected(
          _defaultAvatarAssetPath); // Call default callback
    }
  }

  void _selectGeneratedAvatar() {
    if (mounted) {
      setState(() {
        _selectedImage = null;
        _selectedDefaultAvatarPath = null;
      });
      // Call the callbacks to inform the parent
      widget.onImageSelected(null); // Signal no uploaded image
      widget.onDefaultAvatarSelected(null); // Signal no default asset
      // Call the generated avatar callback to update the ViewModel
      // Pass the CURRENT local color
      widget.onGeneratedAvatarSelected(_generatedAvatarColor);
    }
  }

  void _regenerateColor() {
    if (mounted) {
      final newColor = getRandomColor();
      setState(() {
        _generatedAvatarColor = newColor; // Update local preview color

        // If user regenerates color, it implies they intend to use the generated avatar.
        // Clear other local selections so the preview updates to show the generated avatar.
        _selectedImage = null;
        _selectedDefaultAvatarPath = null;
      });
      // Optionally notify parent that color changed IF generated is selected
      widget.onGeneratedAvatarColorRegenerated?.call(newColor);

      // ALSO, explicitly tell parent that 'generated' is now the selected type with this new color.
      // This ensures OnboardingState.avatarType becomes 'generated'.
      widget.onGeneratedAvatarSelected(newColor);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine preview based on LOCAL state first, then fallback to generation
    Widget previewWidget;
    if (_selectedImage != null) {
      previewWidget = Image.file(File(_selectedImage!.path), fit: BoxFit.cover);
    } else if (_selectedDefaultAvatarPath != null) {
      previewWidget =
          Image.asset(_selectedDefaultAvatarPath!, fit: BoxFit.cover);
    } else {
      // If nothing is selected locally, show the current generated color/initials
      previewWidget = generateAvatar(_generatedAvatarInitials,
          radius: MediaQuery.of(context).size.width * 0.3,
          color: _generatedAvatarColor); // Use the current color
    }

    return Column(
      // Use Column instead of Wrap for layout flexibility
      mainAxisSize: MainAxisSize.min,
      children: [
        // --- Preview Image ---
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.6, // Adjust size
            height:
                MediaQuery.of(context).size.width * 0.6, // Make it square-ish
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8.0),
              // Use appropriate background or image based on state
            ),
            clipBehavior: Clip.antiAlias, // Clip the image to rounded corners
            child: previewWidget,
          ),
        ),

        // --- Upload Photo Options ---
        ListTile(
          leading: Icon(Icons.photo_library),
          title: Text('Choose from Gallery'),
          onTap: () => _pickImage(ImageSource.gallery),
        ),
        ListTile(
          leading: Icon(Icons.camera_alt),
          title: Text('Take Photo'),
          onTap: () => _pickImage(ImageSource.camera),
        ),

        // --- Default Avatar Option ---
        ListTile(
          leading: CircleAvatar(
            // Show the default avatar here
            backgroundImage: AssetImage(_defaultAvatarAssetPath),
            radius: 18, // Smaller radius for the leading icon
          ),
          title: Text('Use Default Avatar'),
          selected: _selectedDefaultAvatarPath != null, // Highlight if selected
          selectedTileColor: Colors.blue
              .withValues(alpha: 0.1), // Optional: Customize highlight color
          onTap: _selectDefaultAvatar,
        ),

        // --- Generated Avatar Option ---
        ListTile(
          leading: InkWell(
            // Make the avatar tappable
            onTap:
                _selectGeneratedAvatar, // Calls the method that calls the callback
            child: generateAvatar(
              _generatedAvatarInitials,
              radius: 18,
              color: _generatedAvatarColor, // Show current generated color
            ),
          ),
          title: Text('Use Initial Avatar'),
          // Determine if generated is selected based on parent state passed via props if needed
          // OR infer if both image and default path are null in LOCAL state
          selected: _selectedImage == null &&
              _selectedDefaultAvatarPath == null, // Highlight if selected
          selectedTileColor: Colors.blue
              .withValues(alpha: 0.1), // Optional: Customize highlight color
          trailing: TextButton(
            // Button to regenerate color
            onPressed: _regenerateColor,
            child: Text('Regenerate Color'),
          ),
          onTap: _selectGeneratedAvatar, // Also allow tapping the text part
        ),

        // --- Update and Cancel Buttons ---
        Padding(
          // Add padding around buttons
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment:
                MainAxisAlignment.spaceBetween, // Space out buttons
            children: [
              ElevatedButton(
                onPressed: widget.onBack, // Use the callback directly
                child: Text(widget.cancelButtonText),
              ),
              ElevatedButton(
                onPressed: widget.onUpdate, // Use the callback directly
                child: Text(widget.updateButtonText),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
