// lib/widgets/custom_keyword_input_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/models/keyword.dart';
import 'package:fuzzywuzzy/fuzzywuzzy.dart' as fuzz;

class CustomKeywordInputWidget extends ConsumerStatefulWidget {
  final List<Keyword> allKeywords; // Pass full list for fuzzy matching
  final List<String> currentSelectedKeywords; // Predefined + Custom combined
  // Callback provides keyword and indicates if it's the custom one (true)
  // or a suggested predefined one (false) that the user confirmed
  final Function(String keyword, bool isCustom) onKeywordAdded;
  final int maxCustomKeywords;
  final int maxTotalKeywords;

  const CustomKeywordInputWidget({
    super.key,
    required this.allKeywords,
    required this.currentSelectedKeywords,
    required this.onKeywordAdded,
    required this.maxCustomKeywords,
    required this.maxTotalKeywords,
  });

  @override
  ConsumerState<CustomKeywordInputWidget> createState() =>
      _CustomKeywordInputWidgetState();
}

class _CustomKeywordInputWidgetState
    extends ConsumerState<CustomKeywordInputWidget> {
  final TextEditingController _controller = TextEditingController();
  bool _isProcessing = false;

  int get _currentTotalCount => widget.currentSelectedKeywords.length;
  int get _currentCustomCount => widget.currentSelectedKeywords
      .where((kw) => !widget.allKeywords.any((k) => k.keywordText == kw))
      .length;

  Future<void> _tryAddCustomKeyword() async {
    final String keyword = _controller.text.trim();
    final String normalizedKeyword = keyword.toLowerCase();
    final bool isPredefined = widget.allKeywords
        .any((k) => k.keywordText.toLowerCase() == normalizedKeyword);

    if (normalizedKeyword.isEmpty || _isProcessing) return;

    // Basic Length Check
    if (normalizedKeyword.length > 25) {
      // Match maxLength in TextFormField
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text("Interest is too long (max 25 chars).")));
      }
      return;
    }

    setState(() => _isProcessing = true);

    // --- Check Limits FIRST ---
    // 1. Check Limits
    if (_currentTotalCount >= widget.maxTotalKeywords) {
      _showMaxKeywordError(); // This method already checks mounted
      setState(() => _isProcessing = false);
      return;
    }
    // 2. Check if already selected (case-insensitive)
    if (widget.currentSelectedKeywords
        .any((selKw) => selKw.toLowerCase() == normalizedKeyword)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Interest already added.")));
      }
      setState(() => _isProcessing = false);
      return;
    }

    // 3. Check custom limit ONLY if it's not a predefined keyword
    if (!isPredefined && _currentCustomCount >= widget.maxCustomKeywords) {
      _showMaxCustomError(); // This method already checks mounted
      setState(() => _isProcessing = false);
      return;
    }
    // --- End Initial Limit Checks ---

    // --- Fuzzy Match Check ---
    String? bestMatch; // Stores the text of the best predefined match
    int bestRatio = 0;
    if (!isPredefined) {
      // Only fuzzy match if it's not an exact predefined match
      for (final existingKeyword in widget.allKeywords) {
        final ratio = fuzz.ratio(
            normalizedKeyword, existingKeyword.keywordText.toLowerCase());
        if (ratio > bestRatio) {
          bestRatio = ratio;
          bestMatch = existingKeyword.keywordText;
        }
      }
    }

    bool useSuggestion = false;
    // Suggest only if ratio is high AND it's not already selected
    if (!isPredefined &&
        bestRatio > 85 &&
        bestMatch != null &&
        !widget.currentSelectedKeywords.contains(bestMatch)) {
      if (!mounted) {
        setState(() => _isProcessing = false);
        return;
      } // Check mounted
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Similar Interest Found'),
          content: Text(
              'Did you mean "$bestMatch"? Using existing interests helps with matching.'),
          actions: [
            TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('No, use "$keyword"')), // Show original casing
            TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('Yes, use "$bestMatch"')),
          ],
        ),
      );
      useSuggestion = confirmed ?? false;
    }

    if (!mounted) {
      setState(() => _isProcessing = false);
      return;
    } // Check mounted again

    if (useSuggestion && bestMatch != null) {
      // User chose the suggestion - callback indicates it's NOT custom
      widget.onKeywordAdded(
          bestMatch, false); // 'false' because it's a predefined keyword
      _controller.clear(); // Clear input
    } else {
     // User chose their custom input (normalizedKeyword),
      // OR no strong suggestion was made/accepted.
      // The initial checks at the top of this function already validated
      // that adding 'normalizedKeyword' (if it's not a duplicate and within limits) is okay.
      // The 'isPredefined' flag tells us if 'normalizedKeyword' itself was an exact predefined match initially.
      widget.onKeywordAdded(normalizedKeyword,
          !isPredefined); // Pass normalized; !isPredefined correctly flags if it's custom
      _controller.clear();
    }

    if (mounted) {
      // Ensure mounted before calling setState
      setState(() => _isProcessing = false);
    }
  }

  void _showMaxKeywordError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content:
              Text("Maximum ${widget.maxTotalKeywords} interests allowed.")));
    }
  }

  void _showMaxCustomError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
              "Maximum ${widget.maxCustomKeywords} custom interests allowed.")));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start, // Align button baseline
        children: [
          Expanded(
            child: TextFormField(
              controller: _controller,
              decoration: const InputDecoration(
                labelText: 'Add Custom Interest',
                hintText: 'Enter unique interest',
                border: OutlineInputBorder(),
                counterText: "", // Hide counter
              ),
              maxLength: 25, // Max length for custom keywords
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (_) => _tryAddCustomKeyword(), // Add on submit
            ),
          ),
          const SizedBox(width: 8),
          Padding(
            // Add padding to align button better
            padding: const EdgeInsets.only(top: 8.0),
            child: ElevatedButton(
              onPressed: _isProcessing ? null : _tryAddCustomKeyword,
              child: _isProcessing
                  ? const SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(strokeWidth: 2))
                  : const Text('Add'),
            ),
          ),
        ],
      ),
      const SizedBox(height: 4),
      Text(
        "Can't find an interest? Add up to ${widget.maxCustomKeywords} unique ones.",
        style: Theme.of(context)
            .textTheme
            .bodySmall
            ?.copyWith(color: Colors.grey[600]),
      ),
    ]);
  }
}
