// lib/features/profile/widgets/city_selector_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/utils/dialog_utils.dart';

class CitySelectorWidget extends ConsumerStatefulWidget {
  final String? initialCity;
  final GeoPoint? initialLocation; // Optional initial GeoPoint
  final String? initialCountry; // Optional initial Country
  // Callback returns all three potentially derived values
  final Function(GeoPoint? location, String? city, String? country)
      onLocationSet;

  const CitySelectorWidget({
    super.key,
    this.initialCity,
    this.initialLocation,
    this.initialCountry,
    required this.onLocationSet,
  });

  @override
  ConsumerState<CitySelectorWidget> createState() => _CitySelectorWidgetState();
}

class _CitySelectorWidgetState extends ConsumerState<CitySelectorWidget> {
  final TextEditingController _cityController = TextEditingController();
  List<SearchInfo> _suggestions = [];
  GeoPoint? _selectedLocation; // From GPS or geocoding
  String? _selectedCityName; // Entered/selected city name
  String? _selectedCountry; // Derived country
  bool _isLoadingLocation = false;
  bool _permissionGranted = false; // Track permission status

  @override
  void initState() {
    super.initState();
    _selectedCityName = widget.initialCity;
    _selectedLocation = widget.initialLocation;
    _selectedCountry = widget.initialCountry;
    _cityController.text = widget.initialCity ?? '';
    _checkInitialPermission(); // Check permission on init
  }

  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  Future<void> _checkInitialPermission() async {
    final status = await Permission.location.status;
    if (mounted) {
      setState(() {
        _permissionGranted =
            status.isGranted || status.isLimited; // Granted or limited is OK
      });
    }
  }

  Future<void> _handlePermissionRequest() async {
    final status = await Permission.location.status;

    if (status.isGranted || status.isLimited) {
      if (mounted) setState(() => _permissionGranted = true);
      _getCurrentLocation(); // Permission already granted, get location
    } else if (status.isPermanentlyDenied || status.isRestricted) {
      if (mounted) showLocationSettingsDialog(context); // Direct to settings
    } else {
      // Permission denied, show the request dialog
      if (mounted) showLocationRequestDialog(context, ref);
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!_permissionGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Location permission not granted.')));
      }
      return;
    }

    final locationService = ref.read(locationServiceProvider);
    if (mounted) setState(() => _isLoadingLocation = true);

    // 1. Get Current Location
    final locationResult = await locationService.getCurrentLocation();

    await locationResult.fold((failure) async {
      // Handle failure getting location
      print("Failed to get current location: $failure");
      if (mounted) {
        setState(() => _isLoadingLocation = false);
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content:
                Text("Could not get current location: ${failure.message}")));
      }
    }, (locationPoint) async {
      // Location Success! Now get address
      // 2. Reverse Geocode
      final placemarkResult =
          await locationService.reverseGeocode(locationPoint);

      await placemarkResult.fold((failure) async {
        // Handle failure during reverse geocode
        print("Failed to reverse geocode: $failure");
        if (mounted) {
          setState(() {
            _selectedLocation = locationPoint; // Still have the point
            _selectedCityName = "Unknown Location"; // Fallback
            _selectedCountry = null;
            _cityController.text = _selectedCityName!;
            _suggestions = [];
            _isLoadingLocation = false;
          });
          // Notify parent even if geocode fails, but city/country might be null/fallback
          widget.onLocationSet(
              _selectedLocation, _selectedCityName, _selectedCountry);
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("Could not get address details for location.")));
        }
      }, (placemark) async {
        // Reverse geocode Success!
        String? city;
        String? country;
        if (placemark != null) {
          city = placemark.locality ?? placemark.subAdministrativeArea;
          country = placemark.country;
        }

        if (mounted) {
          setState(() {
            _selectedLocation = locationPoint;
            _selectedCityName = city ?? "Unknown Location";
            _selectedCountry = country;
            _cityController.text = _selectedCityName!;
            _suggestions = [];
            _isLoadingLocation = false;
          });
          // Call the callback with all derived info
          widget.onLocationSet(
              _selectedLocation, _selectedCityName, _selectedCountry);
        }
      }); // End fold placemarkResult
    }); // End fold locationResult
  }

  Future<void> _searchCities(String input) async {
    if (input.length < 2) {
      if (mounted) setState(() => _suggestions = []);
      return;
    }
    try {
      final suggestionResult =
          await ref.read(locationServiceProvider).getCitySuggestions(input);

      suggestionResult.fold((failure) {
        if (mounted) {
          // Show SnackBar on Error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('Could not fetch city suggestions: ${failure.message}'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _suggestions = []); // Clear suggestions
        }
      }, (suggestions) {
        if (mounted) {
          setState(() {
            _suggestions = suggestions; // Set suggestions on success
          });
        }
      });
    } catch (e) {
      // Catch any other unexpected error during the fold itself
      print("Error updating suggestions UI: $e");
      if (mounted) setState(() => _suggestions = []);
    }
  }

  Future<void> _selectSuggestion(SearchInfo suggestion) async {
    // We already have the necessary info in SearchInfo, less need for extra calls
    String? city = suggestion.address?.city ?? suggestion.address?.name;
    String? country = suggestion.address?.country;
    GeoPoint? location = suggestion.point;

    // Optionally, you COULD re-fetch country based on coords for consistency, but might be overkill
    // if (location != null) {
    //   final countryResult = await ref.read(locationServiceProvider).getCountryFromLocation(location);
    //   country = countryResult.getOrElse((_) => country); // Keep existing country if refetch fails
    // }

    if (mounted) {
      setState(() {
        _selectedCityName = city;
        _selectedLocation = location;
        _selectedCountry = country;
        _cityController.text = city ?? '';
        _suggestions = [];
      });
      widget.onLocationSet(
          _selectedLocation, _selectedCityName, _selectedCountry);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // Take up only needed vertical space
      children: [
        Text('Location', style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 8),

        // --- Use Current Location Button ---
        _isLoadingLocation
            ? Center(child: CircularProgressIndicator())
            : ElevatedButton.icon(
                icon: Icon(Icons.my_location),
                label: Text('Use Current Location'),
                onPressed: _permissionGranted
                    ? _getCurrentLocation // If permission granted, get location
                    : _handlePermissionRequest, // Otherwise, handle permission request
              ),
        SizedBox(height: 8),
        Center(
            child: Text('OR', style: TextStyle(fontWeight: FontWeight.bold))),
        SizedBox(height: 8),

        // --- Manual City Entry ---
        TextFormField(
          controller: _cityController,
          decoration: InputDecoration(
            labelText: 'Enter City Manually',
            hintText: 'Start typing for suggestions...',
            border: OutlineInputBorder(),
          ),
          onChanged: _searchCities, // Trigger search on change
          validator: (value) {
            // Basic validation
            if (value == null || value.isEmpty) {
              // Only strictly require if GPS wasn't used/failed
              if (_selectedLocation == null) {
                return 'Please enter a city or use current location';
              }
            }
            return null;
          },
        ),

        // --- Autocomplete Suggestions ---
        if (_suggestions.isNotEmpty)
          ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 120), // Limit height
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _suggestions[index];
                return ListTile(
                  title: Text(suggestion.address?.toString() ??
                      'Unknown location'), // Display formatted address
                  onTap: () => _selectSuggestion(suggestion),
                );
              },
            ),
          ),
      ],
    );
  }
}
