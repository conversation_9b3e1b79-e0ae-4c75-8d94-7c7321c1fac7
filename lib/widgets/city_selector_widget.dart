// lib/features/profile/widgets/city_selector_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mapbox_search/mapbox_search.dart' as mapbox;
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/utils/dialog_utils.dart';
import 'package:watermelon_draft/core/services/mapbox_location_service.dart';

class CitySelectorWidget extends ConsumerStatefulWidget {
  final String? initialCity;
  final GeoPoint? initialLocation; // Optional initial GeoPoint
  final String? initialCountry; // Optional initial Country
  // Callback returns all three potentially derived values
  final Function(GeoPoint? location, String? city, String? country)
      onLocationSet;

  const CitySelectorWidget({
    super.key,
    this.initialCity,
    this.initialLocation,
    this.initialCountry,
    required this.onLocationSet,
  });

  @override
  ConsumerState<CitySelectorWidget> createState() => _CitySelectorWidgetState();
}

class _CitySelectorWidgetState extends ConsumerState<CitySelectorWidget> {
  final TextEditingController _cityController = TextEditingController();
  List<SearchInfo> _suggestions = [];
  GeoPoint? _selectedLocation; // From GPS or geocoding
  String? _selectedCityName; // Entered/selected city name
  String? _selectedCountry; // Derived country
  bool _isLoadingLocation = false;
  bool _permissionGranted = false; // Track permission status

  // MapBox service for reliable city search
  late final MapBoxLocationService _mapBoxService;

  @override
  void initState() {
    super.initState();
    _selectedCityName = widget.initialCity;
    _selectedLocation = widget.initialLocation;
    _selectedCountry = widget.initialCountry;
    _cityController.text = widget.initialCity ?? '';
    _checkInitialPermission(); // Check permission on init

    // Initialize MapBox service
    _mapBoxService = MapBoxLocationService();

    // Listen to text changes to show/hide clear button
    _cityController.addListener(() {
      setState(() {}); // Rebuild to show/hide clear button
    });
  }

  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  Future<void> _checkInitialPermission() async {
    final status = await Permission.location.status;
    if (mounted) {
      setState(() {
        _permissionGranted =
            status.isGranted || status.isLimited; // Granted or limited is OK
      });
    }
  }

  Future<void> _handlePermissionRequest() async {
    final status = await Permission.location.status;

    if (status.isGranted || status.isLimited) {
      if (mounted) setState(() => _permissionGranted = true);
      _getCurrentLocation(); // Permission already granted, get location
    } else if (status.isPermanentlyDenied || status.isRestricted) {
      if (mounted) showLocationSettingsDialog(context); // Direct to settings
    } else {
      // Permission denied, show the request dialog
      if (mounted) showLocationRequestDialog(context, ref);
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!_permissionGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Location permission not granted.')));
      }
      return;
    }

    final locationService = ref.read(locationServiceProvider);
    if (mounted) setState(() => _isLoadingLocation = true);

    // 1. Get Current Location
    final locationResult = await locationService.getCurrentLocation();

    await locationResult.fold((failure) async {
      // Handle failure getting location
      print("Failed to get current location: $failure");
      if (mounted) {
        setState(() => _isLoadingLocation = false);
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content:
                Text("Could not get current location: ${failure.message}")));
      }
    }, (locationPoint) async {
      // Location Success! Now get address
      // 2. Reverse Geocode
      final placemarkResult =
          await locationService.reverseGeocode(locationPoint);

      await placemarkResult.fold((failure) async {
        // Handle failure during reverse geocode
        print("Failed to reverse geocode: $failure");
        if (mounted) {
          setState(() {
            _selectedLocation = locationPoint; // Still have the point
            _selectedCityName = "Unknown Location"; // Fallback
            _selectedCountry = null;
            _cityController.text = _selectedCityName!;
            _suggestions = [];
            _isLoadingLocation = false;
          });
          // Notify parent even if geocode fails, but city/country might be null/fallback
          widget.onLocationSet(
              _selectedLocation, _selectedCityName, _selectedCountry);
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("Could not get address details for location.")));
        }
      }, (placemark) async {
        // Reverse geocode Success!
        String? city;
        String? country;
        if (placemark != null) {
          city = placemark.locality ?? placemark.subAdministrativeArea;
          country = placemark.country;
        }

        if (mounted) {
          setState(() {
            _selectedLocation = locationPoint;
            _selectedCityName = city ?? "Unknown Location";
            _selectedCountry = country;
            _cityController.text = _selectedCityName!;
            _suggestions = [];
            _isLoadingLocation = false;
          });
          // Call the callback with all derived info
          widget.onLocationSet(
              _selectedLocation, _selectedCityName, _selectedCountry);
        }
      }); // End fold placemarkResult
    }); // End fold locationResult
  }

  Future<void> _searchCities(String input) async {
    if (input.length < 2) {
      if (mounted) setState(() => _suggestions = []);
      return;
    }

    try {
      print("CitySelectorWidget: Searching for '$input'");

      // Try MapBox first for reliable results
      final mapBoxResult =
          await _mapBoxService.getCitySuggestions(input, limit: 5);

      await mapBoxResult.fold(
        (failure) async {
          print("CitySelectorWidget: MapBox failed: $failure");
          // Instead of falling back to problematic OSM, provide helpful empty state
          if (mounted) {
            setState(() {
              _suggestions = [];
            });
          }
        },
        (mapBoxPlaces) async {
          print(
              "CitySelectorWidget: MapBox returned ${mapBoxPlaces.length} results");
          // Convert MapBox places to SearchInfo for compatibility
          final searchInfoList = mapBoxPlaces.map((place) {
            return _convertMapBoxPlaceToSearchInfo(place);
          }).toList();

          if (mounted) {
            setState(() {
              _suggestions = searchInfoList;
            });
          }
        },
      );
    } catch (e) {
      print("CitySelectorWidget: Unexpected error: $e");
      // Graceful degradation - show empty suggestions
      if (mounted) {
        setState(() {
          _suggestions = [];
        });
      }
    }
  }

  /// Convert MapBox place to SearchInfo for compatibility
  SearchInfo _convertMapBoxPlaceToSearchInfo(mapbox.MapBoxPlace place) {
    // Create a compatible Address object
    final address = Address(
      name: place.placeName ?? place.text ?? 'Unknown',
      city: place.text ?? _extractCityFromPlaceName(place.placeName),
      country: _extractCountryFromPlaceName(place.placeName),
    );

    // Create GeoPoint from MapBox Location with validation
    GeoPoint geoPoint;
    if (place.center != null) {
      final lat = place.center!.lat;
      final lng = place.center!.long;
      print(
          "CitySelectorWidget: Converting MapBox Location - lat: $lat, lng: $lng");

      // Validate coordinates
      if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        geoPoint = GeoPoint(latitude: lat, longitude: lng);
      } else {
        print(
            "CitySelectorWidget: Invalid coordinates from MapBox - lat: $lat, lng: $lng");
        geoPoint = GeoPoint(latitude: 0.0, longitude: 0.0);
      }
    } else {
      print("CitySelectorWidget: No center coordinates from MapBox");
      geoPoint = GeoPoint(latitude: 0.0, longitude: 0.0);
    }

    return SearchInfo(
      point: geoPoint,
      address: address,
    );
  }

  /// Extract city name from place name like "New York, NY, USA"
  String? _extractCityFromPlaceName(String? placeName) {
    if (placeName == null) return null;
    final parts = placeName.split(',');
    return parts.isNotEmpty ? parts.first.trim() : null;
  }

  /// Extract country from place name like "New York, NY, USA"
  String? _extractCountryFromPlaceName(String? placeName) {
    if (placeName == null) return null;
    final parts = placeName.split(',');
    return parts.length >= 2 ? parts.last.trim() : null;
  }

  Future<void> _selectSuggestion(SearchInfo suggestion) async {
    // We already have the necessary info in SearchInfo, less need for extra calls
    String? city = suggestion.address?.city ?? suggestion.address?.name;
    String? country = suggestion.address?.country;
    GeoPoint? location = suggestion.point;

    // Optionally, you COULD re-fetch country based on coords for consistency, but might be overkill
    // if (location != null) {
    //   final countryResult = await ref.read(locationServiceProvider).getCountryFromLocation(location);
    //   country = countryResult.getOrElse((_) => country); // Keep existing country if refetch fails
    // }

    if (mounted) {
      setState(() {
        _selectedCityName = city;
        _selectedLocation = location;
        _selectedCountry = country;
        _cityController.text = city ?? '';
        _suggestions = [];
      });
      widget.onLocationSet(
          _selectedLocation, _selectedCityName, _selectedCountry);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // Take up only needed vertical space
      children: [
        Text('Location', style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 8),

        // --- Use Current Location Button ---
        _isLoadingLocation
            ? Center(child: CircularProgressIndicator())
            : ElevatedButton.icon(
                icon: Icon(Icons.my_location),
                label: Text('Use Current Location'),
                onPressed: _permissionGranted
                    ? _getCurrentLocation // If permission granted, get location
                    : _handlePermissionRequest, // Otherwise, handle permission request
              ),
        SizedBox(height: 8),
        Center(
            child: Text('OR', style: TextStyle(fontWeight: FontWeight.bold))),
        SizedBox(height: 8),

        // --- Manual City Entry ---
        TextFormField(
          controller: _cityController,
          decoration: InputDecoration(
            labelText: 'Enter City Manually',
            hintText: 'Type 2+ characters for suggestions...',
            border: OutlineInputBorder(),
            suffixIcon: _cityController.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _cityController.clear();
                        _suggestions = [];
                        _selectedCityName = null;
                        _selectedLocation = null;
                        _selectedCountry = null;
                      });
                      // Notify parent that location was cleared
                      widget.onLocationSet(null, null, null);
                    },
                  )
                : null,
          ),
          onChanged: _searchCities, // Trigger search on change
          validator: (value) {
            // Basic validation
            if (value == null || value.isEmpty) {
              // Only strictly require if GPS wasn't used/failed
              if (_selectedLocation == null) {
                return 'Please enter a city or use current location';
              }
            }
            return null;
          },
        ),

        // --- Autocomplete Suggestions ---
        if (_suggestions.isNotEmpty)
          ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 120), // Limit height
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _suggestions[index];
                return ListTile(
                  title: Text(suggestion.address?.toString() ??
                      'Unknown location'), // Display formatted address
                  onTap: () => _selectSuggestion(suggestion),
                );
              },
            ),
          ),
      ],
    );
  }
}
