// lib/widgets/username_input_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for input formatters

class UsernameInputWidget extends StatelessWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator; // Allow custom validation
  final Function(String)? onChanged; // Allow custom onChanged

  // New properties for dynamic UI states
  final bool isChecking; // Show loading spinner
  final String? errorText; // Error message from external validation
  final bool isValid; // Show success icon
  final TextInputAction? textInputAction; // Keyboard action

  const UsernameInputWidget({
    super.key,
    required this.controller,
    this.validator,
    this.onChanged,
    this.isChecking = false,
    this.errorText,
    this.isValid = false,
    this.textInputAction = TextInputAction.next,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: 'Username',
        hintText: 'Choose a unique username', // Add hint text
        prefixIcon: const Icon(Icons.alternate_email),
        border: const OutlineInputBorder(),
        counterText: "", // Hide the default counter

        // Dynamic suffix icon based on state
        suffixIcon: _buildSuffixIcon(),

        // External error text (e.g., from async validation)
        errorText: errorText,
      ),
      maxLength: 20, // Set a reasonable max length
      keyboardType: TextInputType.text, // Allows letters, numbers, underscore
      // Optional: Add input formatter to restrict characters further
      inputFormatters: [
        FilteringTextInputFormatter.allow(
            RegExp(r'[a-zA-Z0-9_]')), // Allow letters, numbers, underscore
      ],
      autocorrect: false, // Disable autocorrect for usernames
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a username';
        }
        if (value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        if (value.length > 20) {
          return 'Username cannot exceed 20 characters'; // Match maxLength
        }
        if (value.contains(' ')) {
          return 'Username cannot contain spaces';
        }
        if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
          return 'Only letters, numbers, and underscores allowed';
        }
        // Allow additional validation if provided (e.g., async availability check)
        if (validator != null) {
          return validator!(value);
        }
        return null; // Return null if the input is valid
      },
      textInputAction: textInputAction,
      onChanged: onChanged, // Pass onChanged if provided
    );
  }

  /// Build the suffix icon based on the current state
  Widget? _buildSuffixIcon() {
    if (isChecking) {
      // Show loading spinner when checking availability
      return Container(
        padding: const EdgeInsets.all(12.0),
        child: const SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    } else if (isValid && errorText == null) {
      // Show success icon when valid and no errors
      return const Icon(Icons.check_circle, color: Colors.green);
    } else if (errorText != null && !isChecking) {
      // Show error icon when there's an error and not checking
      return const Icon(Icons.error, color: Colors.red);
    }

    // No icon for neutral state
    return null;
  }
}
