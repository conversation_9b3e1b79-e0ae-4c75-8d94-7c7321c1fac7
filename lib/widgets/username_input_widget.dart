// lib/features/profile/widgets/username_input_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for input formatters

class UsernameInputWidget extends StatelessWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator; // Allow custom validation
  final Function(String)? onChanged; // Allow custom onChanged

  const UsernameInputWidget({
    super.key,
    required this.controller,
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: const InputDecoration(
        labelText: 'Username',
        hintText: 'Choose a unique username', // Add hint text
        prefixIcon: Icon(Icons.alternate_email),
        border: OutlineInputBorder(),
        counterText: "", // Hide the default counter
      ),
      maxLength: 20, // Set a reasonable max length
      keyboardType: TextInputType.text, // Allows letters, numbers, underscore
      // Optional: Add input formatter to restrict characters further
      inputFormatters: [
        FilteringTextInputFormatter.allow(
            RegExp(r'[a-zA-Z0-9_]')), // Allow letters, numbers, underscore
      ],
      autocorrect: false, // Disable autocorrect for usernames
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a username';
        }
        if (value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        if (value.length > 20) {
          return 'Username cannot exceed 20 characters'; // Match maxLength
        }
        if (value.contains(' ')) {
          return 'Username cannot contain spaces';
        }
        if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
          return 'Only letters, numbers, and underscores allowed';
        }
        // Allow additional validation if provided (e.g., async availability check)
        if (validator != null) {
          return validator!(value);
        }
        return null; // Return null if the input is valid
      },
      onChanged: onChanged, // Pass onChanged if provided
    );
  }
}
