// lib/widgets/activity_search_input_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/providers.dart';

class ActivitySearchInputWidget extends ConsumerStatefulWidget {
  // Callback when a user taps a suggestion from the list
  final Function(SharedActivity selectedActivity) onActivitySelected;
  // Optional: Pass currently selected IDs to filter them out from suggestions
  final List<String> currentSelectionIds;

  const ActivitySearchInputWidget({
    super.key,
    required this.onActivitySelected,
    this.currentSelectionIds = const [], // Default to empty list
  });

  @override
  ConsumerState<ActivitySearchInputWidget> createState() =>
      _ActivitySearchInputWidgetState();
}

class _ActivitySearchInputWidgetState
    extends ConsumerState<ActivitySearchInputWidget> {
  final TextEditingController _controller = TextEditingController();
  List<SharedActivity> _suggestions = [];
  List<SharedActivity> _allActivities = [];
  bool _isLoading = false; // Loading suggestions
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    // Fetch all activities once for filtering - Provider handles caching
    WidgetsBinding.instance.addPostFrameCallback((_) => _loadAllActivities());
  }

  Future<void> _loadAllActivities() async {
    // Use watch here? Or read? Read is fine if provider is keepAlive
    // Let's use watch to react to potential future updates to the list
    final activitiesAsync =
        ref.read(sharedActivitiesProvider); // Read cached value
    activitiesAsync.whenData((activities) {
      if (mounted) {
        _allActivities = activities;
      }
    });
    // Note: This assumes sharedActivitiesProvider has keepAlive:true
    // and is loaded elsewhere or handles its own loading state.
    // If not, we'd need to watch it in build and handle loading/error there.
  }

  @override
  void dispose() {
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _filterSuggestions(String query) {
    _debounce?.cancel();
    if (query.length < 2) {
      // Minimum characters to start searching
      if (mounted) setState(() => _suggestions = []);
      return;
    }
    if (mounted) setState(() => _isLoading = true);

    _debounce = Timer(const Duration(milliseconds: 350), () {
      if (!mounted) return;
      final lowerCaseQuery = query.toLowerCase();
      final filtered = _allActivities.where((activity) {
        final nameMatch =
            activity.activityName.toLowerCase().contains(lowerCaseQuery);
        final notAlreadySelected =
            !widget.currentSelectionIds.contains(activity.activityId);
        return nameMatch && notAlreadySelected;
      }).toList();

      setState(() {
        _suggestions = filtered;
        _isLoading = false;
      });
    });
  }

  void _handleSelection(SharedActivity activity) {
    widget.onActivitySelected(activity); // Call parent callback
    if (mounted) {
      setState(() {
        _controller.clear(); // Clear input field
        _suggestions = []; // Clear suggestions
        _isLoading = false;
      });
      FocusScope.of(context).unfocus(); // Dismiss keyboard
    }
  }

  @override
  Widget build(BuildContext context) {
    // Optional: Watch the provider here if _loadAllActivities isn't sufficient
    // final allActivitiesAsync = ref.watch(sharedActivitiesProvider);
    // if (allActivitiesAsync is AsyncData) _allActivities = allActivitiesAsync.value!;
    // Handle loading/error state for the main provider if needed

    return Column(
      mainAxisSize: MainAxisSize.min, // Take minimum vertical space
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: 'Add Activity',
            hintText: 'Start typing an activity...',
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      _controller.clear();
                    },
                  )
                : null,
          ),
          onChanged: _filterSuggestions,
        ),
        // Display Suggestions or Loading indicator
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Center(
                child: SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(strokeWidth: 2))),
          )
        else if (_suggestions.isNotEmpty)
          ConstrainedBox(
            constraints: const BoxConstraints(
                maxHeight: 160), // Limit suggestion list height
            child: Card(
              margin: const EdgeInsets.only(top: 4),
              elevation: 2,
              child: ListView.builder(
                padding: EdgeInsets.zero, // Remove ListView padding
                shrinkWrap: true,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final activity = _suggestions[index];
                  return ListTile(
                    dense: true,
                    title: Text(activity.activityName),
                    // subtitle: Text(activity.category,
                    //     style: TextStyle(
                    //         fontSize: 11, color: Colors.grey)), // Show category
                    onTap: () => _handleSelection(activity),
                  );
                },
              ),
            ),
          )
        else if (_controller.text.length >= 2 && !_isLoading)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text('No matching activities found.',
                style: TextStyle(color: Colors.grey, fontSize: 12)),
          ),
      ],
    );
  }
}
