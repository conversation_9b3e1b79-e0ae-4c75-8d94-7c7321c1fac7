// lib/widgets/editable_chips_field.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';

/// A generic, reusable widget for displaying lists as chips with ID-to-name resolution
/// Optimized for compact layout and space efficiency
///
/// Use cases:
/// - User Profile Edit page (Shared Activities, My Interests)
/// - Any context where you need to display lists as chips with edit functionality
///
/// Examples:
/// ```dart
/// // For Shared Activities (resolves IDs to names)
/// EditableChipsField(
///   title: 'Shared Activities',
///   values: ['activity-id-1', 'activity-id-2'],
///   resolveIds: true,
///   onEdit: () => navigateToEditPage(),
/// )
///
/// // For My Interests (uses values directly)
/// EditableChipsField(
///   title: 'My Interests',
///   values: ['Travel', 'Photography', 'Cooking'],
///   resolveIds: false,
///   maxChipsToShow: 5,
///   onEdit: () => navigateToEditPage(),
/// )
/// ```
class EditableChipsField extends ConsumerWidget {
  /// The title of the field
  final String title;

  /// The list of IDs to display (for shared activities) or direct values (for interests)
  final List<String>? values;

  /// Whether to resolve IDs to names using SharedActivitiesProvider
  /// Set to true for shared activities, false for interests/keywords
  final bool resolveIds;

  /// Optional tooltip for the edit button
  final String? tooltip;

  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  /// Text to display when the list is empty
  final String emptyText;

  /// Maximum number of chips to display before showing "+X more"
  final int? maxChipsToShow;

  /// Compact mode for tighter spacing
  final bool compact;

  const EditableChipsField({
    super.key,
    required this.title,
    required this.values,
    this.resolveIds = false,
    this.tooltip,
    required this.onEdit,
    this.emptyText = 'Not yet added',
    this.maxChipsToShow,
    this.compact = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (resolveIds) {
      // For shared activities - need to resolve IDs to names
      final allActivitiesAsync = ref.watch(sharedActivitiesProvider);

      return allActivitiesAsync.when(
        data: (allActivities) => _buildContent(context, allActivities),
        loading: () => _buildLoadingContent(context),
        error: (error, stack) => _buildErrorContent(context, error),
      );
    } else {
      // For interests/keywords - use values directly
      return _buildContent(context, null);
    }
  }

  Widget _buildContent(
      BuildContext context, List<SharedActivity>? allActivities) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: compact ? 4.0 : 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Edit Button Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
                padding: compact ? const EdgeInsets.all(4) : null,
                constraints: compact
                    ? const BoxConstraints(minWidth: 32, minHeight: 32)
                    : null,
              ),
            ],
          ),
          SizedBox(height: compact ? 4 : 8),

          // Chips Display
          _buildChips(context, allActivities),
        ],
      ),
    );
  }

  Widget _buildChips(
      BuildContext context, List<SharedActivity>? allActivities) {
    if (values == null || values!.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: compact ? 4.0 : 8.0),
        child: Text(
          emptyText,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: compact ? 12 : 14,
          ),
        ),
      );
    }

    // Get display names
    List<String> displayNames;
    if (resolveIds && allActivities != null) {
      // Resolve IDs to names for shared activities
      displayNames = values!.map((id) {
        final activity = allActivities.firstWhereOrNull(
          (act) => act.activityId == id,
        );
        return activity?.activityName ?? 'Unknown Activity';
      }).toList();
    } else {
      // Use values directly for interests/keywords
      displayNames = values!;
    }

    // Handle max chips display
    List<String> chipsToShow = displayNames;
    String? moreText;

    if (maxChipsToShow != null && displayNames.length > maxChipsToShow!) {
      chipsToShow = displayNames.take(maxChipsToShow!).toList();
      final remaining = displayNames.length - maxChipsToShow!;
      moreText = '+$remaining more';
    }

    return Wrap(
      spacing: compact ? 4.0 : 6.0,
      runSpacing: compact ? 2.0 : 4.0,
      children: [
        // Display chips
        ...chipsToShow.map((name) {
          return Chip(
            label: Text(name),
            labelStyle: TextStyle(
              fontSize: compact ? 11 : 12,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: compact ? 6.0 : 8.0,
              vertical: compact ? 0.0 : 2.0,
            ),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: compact
                ? const VisualDensity(horizontal: -2, vertical: -2)
                : VisualDensity.compact,
            // No delete or tap functionality - just display
          );
        }),

        // Show "+X more" chip if needed
        if (moreText != null)
          Chip(
            label: Text(moreText),
            labelStyle: TextStyle(
              fontSize: compact ? 11 : 12,
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
            padding: EdgeInsets.symmetric(
              horizontal: compact ? 6.0 : 8.0,
              vertical: compact ? 0.0 : 2.0,
            ),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: compact
                ? const VisualDensity(horizontal: -2, vertical: -2)
                : VisualDensity.compact,
            backgroundColor: Colors.grey[100],
          ),
      ],
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: compact ? 4.0 : 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
                padding: compact ? const EdgeInsets.all(4) : null,
                constraints: compact
                    ? const BoxConstraints(minWidth: 32, minHeight: 32)
                    : null,
              ),
            ],
          ),
          SizedBox(height: compact ? 4 : 8),
          SizedBox(
            height: compact ? 16 : 20,
            width: compact ? 16 : 20,
            child: const CircularProgressIndicator(strokeWidth: 2),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorContent(BuildContext context, Object error) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: compact ? 4.0 : 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
                padding: compact ? const EdgeInsets.all(4) : null,
                constraints: compact
                    ? const BoxConstraints(minWidth: 32, minHeight: 32)
                    : null,
              ),
            ],
          ),
          SizedBox(height: compact ? 4 : 8),
          Text(
            'Error loading data',
            style: TextStyle(
              color: Colors.red[600],
              fontSize: compact ? 12 : 14,
            ),
          ),
        ],
      ),
    );
  }
}
