// lib/widgets/selected_items_chips_widget.dart
import 'package:flutter/material.dart';

class SelectedItemsChipsWidget extends StatelessWidget {
  final List<String> items; // The list of strings to display as chips
  final Function(String item) onItemDeleted; // Callback when 'x' is tapped
  final String noItemsText; // Text to show when the list is empty
  final WrapAlignment alignment; // How to align chips

  const SelectedItemsChipsWidget({
    super.key,
    required this.items,
    required this.onItemDeleted,
    this.noItemsText = '', // Default to showing nothing when empty
    this.alignment = WrapAlignment.start, // Default alignment
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      // Return placeholder text or an empty box if the list is empty
      return noItemsText.isNotEmpty
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(noItemsText, style: TextStyle(color: Colors.grey)),
            )
          : const SizedBox.shrink();
    }

    // Use Wrap to display chips, allowing them to flow to the next line
    return Wrap(
      spacing: 6.0, // Horizontal space between chips
      runSpacing: 6.0, // Vertical space between chip lines
      alignment: alignment,
      children: items
          .map((item) => Chip(
                key: ValueKey(
                    item), // Use item itself as key (assumes items are unique)
                label: Text(item),
                labelStyle:
                    const TextStyle(fontSize: 12), // Adjust style as needed
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
                deleteIconColor: Colors.grey[600],
                deleteButtonTooltipMessage: 'Remove', // Accessibility
                onDeleted: () {
                  onItemDeleted(
                      item); // Call the callback with the item to delete
                },
              ))
          .toList(),
    );
  }
}
