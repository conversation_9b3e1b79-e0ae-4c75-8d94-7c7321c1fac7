// lib/widgets/keyword_search_input_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/models/keyword.dart'; // Import Keyword model

class KeywordSearchInputWidget extends ConsumerStatefulWidget {
  // Callback when a user taps a suggestion
  final Function(Keyword selectedKeyword) onKeywordSelected;
  // Pass currently selected keyword strings to filter them out
  final List<String> currentSelectionNames;
  final List<Keyword> allKeywords;

  const KeywordSearchInputWidget({
    super.key,
    required this.onKeywordSelected,
    required this.allKeywords,
    this.currentSelectionNames = const [],
  });

  @override
  ConsumerState<KeywordSearchInputWidget> createState() =>
      _KeywordSearchInputWidgetState();
}

class _KeywordSearchInputWidgetState
    extends ConsumerState<KeywordSearchInputWidget> {
  final TextEditingController _controller = TextEditingController();
  List<Keyword> _suggestions = [];
  // List<Keyword> _allKeywords = []; // Cached list
  // bool _isLoading = false; // Only for debounce/filtering phase
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    // Load initial keywords from provider cache if possible
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _allKeywords = ref.read(keywordsProvider).valueOrNull ?? [];
    // });
  }

  @override
  void dispose() {
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _filterSuggestions(String query) {
    _debounce?.cancel();
    if (query.length < 2) {
      if (mounted) setState(() => _suggestions = []);
      return;
    }
    _debounce = Timer(const Duration(milliseconds: 300), () {
      if (!mounted) return;
      final lowerCaseQuery = query.toLowerCase();
      // --- Use widget.allKeywords passed from parent ---
      final filtered = widget.allKeywords.where((keyword) {
        final nameMatch =
            keyword.keywordText.toLowerCase().contains(lowerCaseQuery);
        final notAlreadySelected =
            !widget.currentSelectionNames.contains(keyword.keywordText);
        return nameMatch && notAlreadySelected;
      }).toList();

      setState(() {
        _suggestions = filtered;
        // REMOVE: _isLoading = false;
      });
    });
  }

  void _handleSelection(Keyword keyword) {
    print("KeywordSearchInput: _handleSelection for ${keyword.keywordText}");
    widget.onKeywordSelected(keyword);
    if (mounted) {
      print(
          "KeywordSearchInput: Clearing controller & suggestions after selection.");
      setState(() {
        _controller.clear();
        _suggestions = [];
        // _isLoading = false;
      });
      FocusScope.of(context).unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the main provider to react to updates/errors if needed
    // ref.watch(keywordsProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: 'Add Interest Keywords',
            hintText: 'Search interests (e.g., hiking)...',
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      _controller.clear();
                      _filterSuggestions('');
                    },
                  )
                : null,
          ),
          onChanged: _filterSuggestions,
        ),

        // Display Suggestions
        if (_suggestions.isNotEmpty)
          ConstrainedBox(
            /* ... Suggestions ListView ... */
            constraints: const BoxConstraints(maxHeight: 160),
            child: Card(
              margin: const EdgeInsets.only(top: 4),
              elevation: 2,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final keyword = _suggestions[index];
                  return ListTile(
                    dense: true,
                    title: Text(keyword.keywordText),
                    // subtitle: keyword.category != null ? Text(keyword.category!) : null, // Optional category display
                    onTap: () => _handleSelection(keyword),
                  );
                },
              ),
            ),
          )
        // Optional: Show "No results" only if query is long enough
        else if (_controller.text.length >= 2)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Center(
              child: Text(
                'No matching keywords found.',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ),
          ),
      ],
    );
  }
}
