// lib/widgets/gender_selector_widget.dart
import 'package:flutter/material.dart';

/// A reusable gender selector widget with predefined options and custom input.
class GenderSelectorWidget extends StatelessWidget {
  /// The currently selected gender
  final String? selectedGender;

  /// Callback when a gender is selected
  final Function(String) onGenderSelected;

  /// Optional validator function
  final String? Function(String?)? validator;

  /// List of predefined gender options
  final List<String> genderOptions;

  /// Whether to allow custom gender input
  final bool allowCustomGender;

  /// The label text for the field
  final String labelText;

  /// Whether to show required indicator (*)
  final bool isRequired;

  const GenderSelectorWidget({
    Key? key,
    required this.selectedGender,
    required this.onGenderSelected,
    this.validator,
    this.genderOptions = const [
      'Male',
      'Female',
      'Non-binary',
      'Prefer not to say'
    ],
    this.allowCustomGender = true,
    this.labelText = 'Gender',
    this.isRequired = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      initialValue: selectedGender,
      validator: validator,
      builder: (FormFieldState<String> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRequired ? '$labelText *' : labelText,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            // Predefined gender options as chips
            Wrap(
              spacing: 8.0,
              runSpacing: 4.0,
              children: genderOptions.map((gender) {
                final isSelected = selectedGender == gender;
                return FilterChip(
                  label: Text(gender),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      onGenderSelected(gender);
                      field.didChange(gender);
                    }
                  },
                  selectedColor:
                      Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  checkmarkColor: Theme.of(context).primaryColor,
                );
              }).toList(),
            ),

            // Custom gender input if enabled
            if (allowCustomGender) ...[
              const SizedBox(height: 16),
              TextFormField(
                decoration: InputDecoration(
                  labelText: 'Or specify your gender',
                  hintText: 'Enter your gender identity',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.person),
                ),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    onGenderSelected(value);
                    field.didChange(value);
                  }
                },
                // Pre-fill if selected gender is not in predefined options
                initialValue: selectedGender != null &&
                        !genderOptions.contains(selectedGender)
                    ? selectedGender
                    : null,
              ),
            ],

            // Show error text if validation fails
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  field.errorText!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// A simplified gender selector with just the essential options
class SimpleGenderSelector extends StatelessWidget {
  /// The currently selected gender
  final String? selectedGender;

  /// Callback when a gender is selected
  final Function(String) onGenderSelected;

  /// Optional validator function
  final String? Function(String?)? validator;

  const SimpleGenderSelector({
    Key? key,
    required this.selectedGender,
    required this.onGenderSelected,
    this.validator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GenderSelectorWidget(
      selectedGender: selectedGender,
      onGenderSelected: onGenderSelected,
      validator: validator ?? _defaultValidator,
      genderOptions: const [
        'Male',
        'Female',
        'Non-binary',
        'Prefer not to say'
      ],
      allowCustomGender: false,
      labelText: 'Gender',
      isRequired: true,
    );
  }

  /// Default validator for gender selection
  String? _defaultValidator(String? gender) {
    if (gender == null || gender.isEmpty) {
      return 'Please select your gender';
    }
    return null;
  }
}

/// A comprehensive gender selector with custom input option
class ComprehensiveGenderSelector extends StatelessWidget {
  /// The currently selected gender
  final String? selectedGender;

  /// Callback when a gender is selected
  final Function(String) onGenderSelected;

  /// Optional validator function
  final String? Function(String?)? validator;

  const ComprehensiveGenderSelector({
    Key? key,
    required this.selectedGender,
    required this.onGenderSelected,
    this.validator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GenderSelectorWidget(
      selectedGender: selectedGender,
      onGenderSelected: onGenderSelected,
      validator: validator ?? _defaultValidator,
      genderOptions: const [
        'Male',
        'Female',
        'Non-binary',
        'Genderfluid',
        'Agender',
        'Prefer not to say'
      ],
      allowCustomGender: true,
      labelText: 'Gender Identity',
    );
  }

  /// Default validator for gender selection
  String? _defaultValidator(String? gender) {
    if (gender == null || gender.isEmpty) {
      return 'Please select or specify your gender';
    }
    return null;
  }
}
