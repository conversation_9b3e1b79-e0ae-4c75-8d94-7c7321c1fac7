// lib/widgets/date_picker_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// A reusable date picker widget for selecting dates of birth or other dates.
class DatePickerWidget extends StatelessWidget {
  /// The currently selected date
  final DateTime? selectedDate;

  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;

  /// The label text for the field
  final String labelText;

  /// The hint text when no date is selected
  final String hintText;

  /// The earliest selectable date
  final DateTime? firstDate;

  /// The latest selectable date
  final DateTime? lastDate;

  /// Optional validator function
  final String? Function(DateTime?)? validator;

  /// Whether this field is required
  final bool isRequired;

  const DatePickerWidget({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.labelText = 'Date',
    this.hintText = 'Select Date',
    this.firstDate,
    this.lastDate,
    this.validator,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return FormField<DateTime>(
      initialValue: selectedDate,
      validator: validator,
      builder: (FormFieldState<DateTime> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () => _showDatePicker(context, field),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: labelText,
                  border: const OutlineInputBorder(),
                  suffixIcon: const Icon(Icons.calendar_today),
                  errorText: field.errorText,
                ),
                child: Text(
                  selectedDate == null ? hintText : _formatDate(selectedDate!),
                  style: TextStyle(
                    color: selectedDate == null
                        ? Theme.of(context).hintColor
                        : Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show the date picker dialog
  Future<void> _showDatePicker(
      BuildContext context, FormFieldState<DateTime> field) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? _getDefaultInitialDate(),
      firstDate: firstDate ?? DateTime(1900),
      lastDate: lastDate ?? DateTime.now(),
      helpText: 'Select $labelText',
      cancelText: 'Cancel',
      confirmText: 'Select',
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
      field.didChange(pickedDate);
    }
  }

  /// Get a reasonable default initial date
  DateTime _getDefaultInitialDate() {
    final now = DateTime.now();
    // For date of birth, default to 25 years ago
    if (labelText.toLowerCase().contains('birth')) {
      return DateTime(now.year - 25, now.month, now.day);
    }
    return now;
  }

  /// Format the date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// A specialized date picker for date of birth using Cupertino wheel picker
class DateOfBirthPicker extends StatefulWidget {
  /// The currently selected date of birth
  final DateTime? selectedDate;

  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;

  /// Optional validator function
  final String? Function(DateTime?)? validator;

  const DateOfBirthPicker({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.validator,
  });

  @override
  State<DateOfBirthPicker> createState() => _DateOfBirthPickerState();
}

class _DateOfBirthPickerState extends State<DateOfBirthPicker> {
  late DateTime _currentDate;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    // Default to 25 years ago if no date selected
    final now = DateTime.now();
    _currentDate =
        widget.selectedDate ?? DateTime(now.year - 25, now.month, now.day);
    _validateDate(_currentDate);
  }

  @override
  void didUpdateWidget(DateOfBirthPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != oldWidget.selectedDate &&
        widget.selectedDate != null) {
      _currentDate = widget.selectedDate!;
      _validateDate(_currentDate);
    }
  }

  void _validateDate(DateTime date) {
    setState(() {
      _errorText = _getValidationError(date);
    });
  }

  String? _getValidationError(DateTime date) {
    final validator = widget.validator ?? _defaultValidator;
    return validator(date);
  }

  /// Default validator for date of birth
  String? _defaultValidator(DateTime? date) {
    if (date == null) {
      return 'Please select your date of birth';
    }

    final now = DateTime.now();
    final age = now.year -
        date.year -
        ((now.month < date.month ||
                (now.month == date.month && now.day < date.day))
            ? 1
            : 0);

    // Check if the person is at least 13 years old
    if (age < 13) {
      return 'You must be at least 13 years old';
    }

    // Check if the date is not in the future
    if (date.isAfter(now)) {
      return 'Date of birth cannot be in the future';
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date display field
        GestureDetector(
          onTap: () => _showDatePicker(context),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Date of Birth',
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
              errorText: _errorText,
            ),
            child: Text(
              _formatDate(_currentDate),
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        ),

        // Additional info text
        if (_errorText == null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'We only use this to calculate your age',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ),
      ],
    );
  }

  /// Show the Cupertino date picker
  Future<void> _showDatePicker(BuildContext context) async {
    DateTime? pickedDate;

    await showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  Text(
                    'Select Date of Birth',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      if (pickedDate != null) {
                        setState(() {
                          _currentDate = pickedDate!;
                        });
                        _validateDate(pickedDate!);
                        widget.onDateSelected(pickedDate!);
                      }
                    },
                    child: const Text('Done'),
                  ),
                ],
              ),

              // Cupertino Date Picker
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: _currentDate,
                  minimumDate: DateTime(1900),
                  maximumDate: DateTime.now(),
                  onDateTimeChanged: (DateTime date) {
                    pickedDate = date;
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Format the date for display
  String _formatDate(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
