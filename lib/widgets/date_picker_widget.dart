// lib/widgets/date_picker_widget.dart
import 'package:flutter/material.dart';

/// A reusable date picker widget for selecting dates of birth or other dates.
class DatePickerWidget extends StatelessWidget {
  /// The currently selected date
  final DateTime? selectedDate;
  
  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;
  
  /// The label text for the field
  final String labelText;
  
  /// The hint text when no date is selected
  final String hintText;
  
  /// The earliest selectable date
  final DateTime? firstDate;
  
  /// The latest selectable date
  final DateTime? lastDate;
  
  /// Optional validator function
  final String? Function(DateTime?)? validator;
  
  /// Whether this field is required
  final bool isRequired;

  const DatePickerWidget({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.labelText = 'Date',
    this.hintText = 'Select Date',
    this.firstDate,
    this.lastDate,
    this.validator,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return FormField<DateTime>(
      initialValue: selectedDate,
      validator: validator,
      builder: (FormFieldState<DateTime> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: () => _showDatePicker(context, field),
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: labelText,
                  border: const OutlineInputBorder(),
                  suffixIcon: const Icon(Icons.calendar_today),
                  errorText: field.errorText,
                ),
                child: Text(
                  selectedDate == null
                      ? hintText
                      : _formatDate(selectedDate!),
                  style: TextStyle(
                    color: selectedDate == null 
                        ? Theme.of(context).hintColor 
                        : Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show the date picker dialog
  Future<void> _showDatePicker(BuildContext context, FormFieldState<DateTime> field) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? _getDefaultInitialDate(),
      firstDate: firstDate ?? DateTime(1900),
      lastDate: lastDate ?? DateTime.now(),
      helpText: 'Select $labelText',
      cancelText: 'Cancel',
      confirmText: 'Select',
    );
    
    if (pickedDate != null) {
      onDateSelected(pickedDate);
      field.didChange(pickedDate);
    }
  }

  /// Get a reasonable default initial date
  DateTime _getDefaultInitialDate() {
    final now = DateTime.now();
    // For date of birth, default to 25 years ago
    if (labelText.toLowerCase().contains('birth')) {
      return DateTime(now.year - 25, now.month, now.day);
    }
    return now;
  }

  /// Format the date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// A specialized date picker for date of birth with appropriate defaults
class DateOfBirthPicker extends StatelessWidget {
  /// The currently selected date of birth
  final DateTime? selectedDate;
  
  /// Callback when a date is selected
  final Function(DateTime) onDateSelected;
  
  /// Optional validator function
  final String? Function(DateTime?)? validator;

  const DateOfBirthPicker({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return DatePickerWidget(
      selectedDate: selectedDate,
      onDateSelected: onDateSelected,
      labelText: 'Date of Birth',
      hintText: 'Select your date of birth',
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      validator: validator ?? _defaultValidator,
      isRequired: true,
    );
  }

  /// Default validator for date of birth
  String? _defaultValidator(DateTime? date) {
    if (date == null) {
      return 'Please select your date of birth';
    }
    
    final now = DateTime.now();
    final age = now.year - date.year;
    
    // Check if the person is at least 13 years old
    if (age < 13) {
      return 'You must be at least 13 years old';
    }
    
    // Check if the date is not in the future
    if (date.isAfter(now)) {
      return 'Date of birth cannot be in the future';
    }
    
    return null;
  }
}
