// lib/widgets/member_search_input_widget.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart'; // For buildAvatar

class MemberSearchInputWidget extends ConsumerStatefulWidget {
  final String labelText;
  final String hintText;

  const MemberSearchInputWidget({
    super.key,
    this.labelText = 'Search Members',
    this.hintText = 'Enter name or @username...',
  });

  @override
  ConsumerState<MemberSearchInputWidget> createState() =>
      _MemberSearchInputWidgetState();
}

class _MemberSearchInputWidgetState
    extends ConsumerState<MemberSearchInputWidget> {
  final TextEditingController _controller = TextEditingController();
  List<User> _suggestions = [];
  bool _isLoading = false;
  Timer? _debounce;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onSearchChanged);
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) {
      _debounce!.cancel();
    }
    final query = _controller.text;
    if (query == _currentQuery) {
      return; // Avoid search if query didn't actually change
    }

    _currentQuery = query; // Store current query

    if (query.length < 2) {
      // Min length to search
      if (mounted) setState(() => _suggestions = []);
      return;
    }

    if (mounted) {
      setState(() => _isLoading = true);
    }

    _debounce = Timer(const Duration(milliseconds: 400), () async {
      // Check if query is still the same after debounce and widget is mounted
      if (_controller.text == query && mounted) {
        print("Searching users for: $query");
        final result = await ref
            .read(userRepositoryProvider)
            .searchUsersByName(query); // No limit parameter passed from repo, use default 7
        // Check mounted again *after* await
        if (mounted && _controller.text == query) {
          // Check query hasn't changed during fetch
          result.fold((failure) {
            print("Name search error: $failure");
            setState(() {
              _suggestions = [];
              _isLoading = false;
            });
            // Optionally show a snackbar for specific errors?
          }, (users) {
            setState(() {
              _suggestions = users;
              _isLoading = false;
            });
          });
        } else if (mounted) {
          setState(
              () => _isLoading = false); // Query changed, just stop loading
        }
      } else if (mounted) {
        // Query changed during debounce time
        setState(() => _isLoading = false);
      }
    });
  }

  void _clearSearch() {
    _controller
        .clear(); // This will trigger listener -> _onSearchChanged -> clear suggestions
    FocusScope.of(context).unfocus();
  }

  void _navigateToProfile(String userId) {
    FocusScope.of(context).unfocus(); // Dismiss keyboard
    _clearSearch(); // Clear search after navigating
    context.beamToNamed('/profile/$userId');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: widget.labelText,
            hintText: widget.hintText,
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearSearch, // Use clear helper
                  )
                : null,
          ),
          textInputAction: TextInputAction.search,
          onFieldSubmitted: (_) => FocusScope.of(context).unfocus(),
          // onChanged triggers listener set up in initState
        ),
        // Suggestions List
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
                child: SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(strokeWidth: 2))),
          )
        else if (_suggestions.isNotEmpty)
          ConstrainedBox(
            constraints: const BoxConstraints(
                maxHeight: 220), // Increased height slightly
            child: Card(
              margin: const EdgeInsets.only(top: 4),
              elevation: 2,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final user = _suggestions[index];
                  return ListTile(
                    leading: buildAvatar(context, 20, user), // Use shared util
                    title: Text(user.fullName ?? user.username ?? 'Unknown User'),
                    subtitle: Text('@${user.username ?? 'unknown'}'),
                    onTap: () =>
                        _navigateToProfile(user.userId), // Navigate on tap
                  );
                },
              ),
            ),
          )
        else if (_controller.text.length >= 2 &&
            !_isLoading) // Show only if typed >= 2 chars
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12.0),
            child: Text('No members found matching "${_controller.text}".',
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(color: Colors.grey[600])),
          ),
      ],
    );
  }
}
