// lib/features/profile/widgets/full_name_input_widget.dart
import 'package:flutter/material.dart';

// This widget is STATELESS because the controller's state
// is managed by the PARENT widget that passes it in.
class FullNameInputWidget extends StatelessWidget {
  final TextEditingController controller; // Controller managed by parent
  final String? Function(String?)? validator; // Optional custom validator
  final Function(String)? onChanged; // Optional onChanged

  const FullNameInputWidget({
    super.key,
    required this.controller, // Require the controller from parent
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller, // Use the controller passed from parent
      decoration: const InputDecoration(
        labelText: 'Full Name',
        hintText: 'Enter your full name',
        prefixIcon: Icon(Icons.person),
        border: OutlineInputBorder(),
        counterText: "",
      ),
      maxLength: 70,
      keyboardType: TextInputType.name,
      textCapitalization: TextCapitalization.words, // Capitalize each word
      validator: (value) {
        // Basic required validation
        if (value == null || value.isEmpty) {
          return 'Please enter your full name';
        }
        // Optional additional validation from parent
        if (validator != null) {
          return validator!(value);
        }
        return null; // Return null if the input is valid
      },
      onChanged: onChanged, // Use onChanged from parent if provided
    );
  }
}
