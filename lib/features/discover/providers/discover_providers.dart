// lib/features/discover/providers/discover_providers.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/features/discover/viewmodels/discover_viewmodel.dart';

// This file might be empty if the ViewModel is the only provider for this feature
// The provider 'discoverViewModelProvider' is automatically generated
// by the @riverpod annotation on the DiscoverViewModel class.
// You'll import 'discover_viewmodel.provider.g.dart' where needed.
// If you add OTHER providers specific to discover, add them here with @riverpod.

