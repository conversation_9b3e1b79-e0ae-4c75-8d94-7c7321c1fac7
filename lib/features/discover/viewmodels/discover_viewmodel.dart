// lib/features/discover/viewmodels/discover_viewmodel.dart
import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/services/location_service.dart';
import 'package:watermelon_draft/core/services/user_repository.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/features/discover/state/discover_state.dart';
import 'package:watermelon_draft/features/profile/providers/profile_providers.dart';

part 'discover_viewmodel.g.dart';

// TODO: Remove this dummy data when connecting to real backend fetching
const bool _useDummyData = true; // Set to false to use real data fetching

// final List<User> _dummyNearbyUsers = [
//   User(
//     userId: 'dummy-1',
//     username: 'sf_hiker_jane',
//     email: '<EMAIL>', // Not displayed, but needed by model
//     fullName: '<PERSON>',
//     age: 28,
//     gender: 'Female',
//     city: 'San Francisco',
//     country: 'USA',
//     sharedActivities: ['activity_hiking', 'activity_coffee'], // Use IDs
//     myInterests: ['hiking', 'photography', 'coffee', 'ramen'],
//     location: GeoPoint(latitude: 37.775, longitude: -122.418), // Near center
//     profilePictureUrl:
//         'https://picsum.photos/seed/user1/200', // Placeholder image
//     avatarType: 'uploaded',
//     onboardingComplete: true,
//     discoverable: true,
//   ),
//   User(
//     userId: 'dummy-2',
//     username: 'dave_dev',
//     email: '<EMAIL>',
//     fullName: 'David Smith',
//     age: 32,
//     gender: 'Male',
//     city: 'Oakland', // Nearby city
//     country: 'USA',
//     sharedActivities: ['activity_coffee', 'activity_boardgames'],
//     myInterests: ['coding', 'startups', 'board games', 'sci-fi movies'],
//     location: GeoPoint(latitude: 37.804, longitude: -122.271), // Oakland coords
//     profilePictureUrl:
//         'assets/images/defaults/default_avatar.png', // Default avatar asset
//     avatarType: 'default',
//     onboardingComplete: true,
//     discoverable: true,
//   ),
//   User(
//     userId: 'dummy-3',
//     username: 'art_lover_sam',
//     email: '<EMAIL>',
//     fullName: 'Samantha Green',
//     age: 25,
//     gender: 'Other',
//     city: 'San Francisco',
//     country: 'USA',
//     sharedActivities: ['activity_museums', 'activity_movies'],
//     myInterests: ['art', 'museums', 'indie film', 'reading', 'cats'],
//     location: GeoPoint(
//         latitude: 37.779,
//         longitude: -122.422), // Slightly different SF location
//     profilePictureUrl: null, // Generated avatar
//     avatarType: 'generated',
//     generatedAvatarColor: 'FFFF9000',
//     onboardingComplete: true,
//     discoverable: true,
//   ),
//   User(
//     userId: 'dummy-4',
//     username: 'foodie_chris',
//     email: '<EMAIL>',
//     fullName: 'Chris Taylor',
//     age: 30,
//     gender: 'Male',
//     city: 'Berkeley',
//     country: 'USA',
//     sharedActivities: [
//       'activity_restaurants',
//       'activity_breweries',
//       'activity_brunch'
//     ],
//     myInterests: ['food trucks', 'craft beer', 'cooking', 'brunch', 'travel'],
//     location:
//         GeoPoint(latitude: 37.871, longitude: -122.275), // Berkeley coords
//     profilePictureUrl:
//         'https://picsum.photos/seed/user4/200', // Placeholder image
//     avatarType: 'uploaded',
//     onboardingComplete: true,
//     discoverable: true,
//   ),
// ];

@riverpod
class DiscoverViewModel extends _$DiscoverViewModel {
  late final LocationService _locationService;
  late final UserRepository _userRepository;
  Timer? _mapMoveDebounce; // Timer for debouncing map movements
  final double _defaultSearchRadiusMeters = 10000; // Define Default Radius

  @override
  FutureOr<DiscoverState> build() async {
    _locationService = ref.watch(locationServiceProvider);
    _userRepository = ref.watch(userRepositoryProvider);
    ref.onDispose(() {
      // Dispose timer
      _mapMoveDebounce?.cancel();
    });

    // Call helper to determine initial state
    return await _getInitialState();
  } // End build

// --- Helper for Initial State Logic ---
  Future<DiscoverState> _getInitialState() async {
    GeoPoint? initialCenterPoint;
    String? initialCityName;
    Failure? initialFailure;

    // 1. Try getting precise GPS location first
    final gpsLocationResult = await _locationService.getCurrentLocation();

    gpsLocationResult.fold((failure) {
      print("Build Info: GPS failed ($failure), will try profile city.");
      initialFailure = failure; // Store the failure
    }, (geoPoint) {
      print("Build Success: Using GPS Location $geoPoint");
      initialCenterPoint = geoPoint; // Got GPS location
      // We need to get the city name for this point
    });

    // 2. If GPS failed OR we need the city name for the GPS point, try profile/geocode
    if (initialCenterPoint == null) {
      // GPS failed, need to find center from Profile City
      print("Build Info: Attempting to use user profile city.");
      try {
        final user =
            await ref.watch(currentUserProfileProvider.future); // Await profile

        if (user != null) {
          if (user.city != null && user.city!.isNotEmpty) {
            print("Build Info: Profile loaded, geocoding city: ${user.city}");
            final cityLocationResult = await _locationService
                .getLocationAndCountryFromCity(user.city!);
            cityLocationResult.fold((geocodeFailure) {
              print(
                  "Build Warning: Could not geocode city '${user.city}': $geocodeFailure");
              // Couldn't get location from city, keep initialFailure (from GPS)
            }, (locationCountryTuple) {
              initialCenterPoint = locationCountryTuple.$1; // GeoPoint?
              initialCityName = user.city; // Use city name from profile
              if (initialCenterPoint != null) {
                print(
                    "Build Info: Using User's City Location $initialCenterPoint");
                initialFailure =
                    null; // Success finding location via city! Clear initial failure.
              } else {
                print(
                    "Build Warning: Geocoding for city '${user.city}' returned no coordinates.");
                // Keep initialFailure
              }
            });
          } else {
            print("Build Warning: User profile loaded, but city is missing.");
            // Keep initialFailure
          }
        }
      } catch (profileError) {
        print("Build Failed: Could not load user profile: $profileError");
        initialFailure ??= (profileError is Failure
            ? profileError
            : DatabaseFailure(profileError.toString()));
      }
    }

    // 3. If we have a center point but still no city name, reverse geocode
    if (initialCenterPoint != null && initialCityName == null) {
      print(
          "Build Info: Reverse geocoding initial center point $initialCenterPoint");
      final placemarkResult =
          await _locationService.reverseGeocode(initialCenterPoint!);
      placemarkResult.fold(
          (l) => print(
              "Build Warning: Reverse geocode for initial center failed: $l"),
          (p) {
        initialCityName = p?.locality ??
            p?.subAdministrativeArea ??
            p?.name ??
            "Area near you";
        print("Build Info: Derived initial city name: $initialCityName");
      });
    }

    // 4. Handle Final Failure or Success
    if (initialCenterPoint == null) {
      // All attempts failed, throw error (unless in debug mode with workaround)
      throw _handleBuildFailure(
          initialFailure ??
              LocationFailure("Could not determine initial location."),
          null);
    } else {
      // Success! We have a center point and potentially a city name
      // Trigger initial fetch in background
      Future.microtask(() => _fetchUsersForArea(
          center: initialCenterPoint!, radius: _defaultSearchRadiusMeters));
      // Return success state
      return DiscoverState(
          userInitialLocation: gpsLocationResult
              .getRight()
              .toNullable(), // Store original GPS result if available
          currentMapCenter: initialCenterPoint,
          displayCityName: initialCityName, // Use derived/profile city name
          displaySearchRadiusMeters:
              _defaultSearchRadiusMeters, // Use default radius
          fetchUsersError:
              null // Clear any error from failed GPS if we succeeded via city
          );
    }
  }

  // --- Helper to handle geocoding city ---
  // Future<DiscoverState> _geocodeCityAndBuildState(
  //     String city, GeoPoint? userProfileLocation) async {
  //   // ... (no changes needed here, it already returns Future<DiscoverState> or throws) ...
  //   final cityLocationResult =
  //       await _locationService.getLocationAndCountryFromCity(city);
  //   return cityLocationResult.fold((geocodeFailure) {
  //     print("Build Failed: Could not geocode city '$city': $geocodeFailure");
  //     throw _handleBuildFailure(geocodeFailure, geocodeFailure);
  //   }, (locationCountryTuple) {
  //     final cityLocation = locationCountryTuple.$1;
  //     if (cityLocation != null) {
  //       print("Build Success: Using User's City Location $cityLocation");
  //       Future.microtask(
  //           () => _fetchUsersForArea(center: cityLocation, radius: 50000));
  //       // Use city location for center, keep userInitialLocation null (since GPS failed)
  //       return DiscoverState(
  //           userInitialLocation: null, currentMapCenter: cityLocation);
  //     } else {
  //       print(
  //           "Build Warning: Geocoding for city '$city' returned no coordinates.");
  //       // Fallback: Use user's stored location from profile if available, otherwise fail
  //       if (userProfileLocation != null) {
  //         print(
  //             "Build Warning: Falling back to user's stored location from profile: $userProfileLocation");
  //         Future.microtask(() =>
  //             _fetchUsersForArea(center: userProfileLocation, radius: 50000));
  //         return DiscoverState(
  //             userInitialLocation: null, currentMapCenter: userProfileLocation);
  //       } else {
  //         throw _handleBuildFailure(
  //             LocationFailure("Could not find coordinates for city."),
  //             LocationFailure("Could not find coordinates for city."));
  //       }
  //     }
  //   });
  // }

  // --- Helper to handle final build failures ---
  // Helper to handle final build failures by throwing the error
  // This ensures the provider enters the AsyncError state
  Never _handleBuildFailure(Failure primaryFailure, Failure? secondaryFailure) {
    // ... (no changes needed here, it always throws) ...
    final displayFailure = primaryFailure;
    print("Build Failure: Primary Reason: $displayFailure");
    if (secondaryFailure != null && secondaryFailure != primaryFailure) {
      print("Build Failure: Initial GPS Failure was: $secondaryFailure");
    }
    // --- ALWAYS THROW ---
    // Throw the primary failure that caused the build to fail at this point
    // Riverpod will catch this and put the provider in AsyncError state
    throw displayFailure;
  }

  // ------------- METHODS FOR UI INTERACTION --------------
  // Called when the map view stops moving (with debouncing)
  void mapPositionChanged(GeoPoint center, double zoom) {
    final currentData = state.valueOrNull; // Safely get data or null
    if (currentData == null) {
      print("mapPositionChanged skipped: State is not AsyncData");
      return; // Do nothing if state is loading or error
    }

    // Basic calculation, refine later based on screen size/zoom
    final radius = _calculateRadiusFromZoom(zoom);
    print("ViewModel: Map moved, new Fetch Center: $center, Radius: $radius");

    // Update state ONLY for current view, clear explicit search display fields
    state = AsyncData(currentData.copyWith(
      currentMapCenter: center,
      currentZoom: zoom,
      currentSearchRadius: radius,
      // displayCityName: null, // Clear display city
      // displaySearchRadiusMeters: null // Clear display radius
    ));

    _mapMoveDebounce?.cancel();
    _mapMoveDebounce = Timer(const Duration(milliseconds: 750), () {
      // Use the radius calculated from zoom for the fetch
      _fetchUsersForArea(center: center, radius: radius);
    });
  }

  // Called when a user marker is tapped
  // void selectUserMarker(User user) {
  //   final currentData = state.valueOrNull;
  //   if (currentData == null) return;

  //   state = AsyncValue.data(state.value!.copyWith(
  //     selectedUser: user,
  //   ));
  //   print("ViewModel: User selected - ${user.username}");
  // }
  // void selectUserMarker(User user) {
  //   state.whenData((currentData) {
  //     // Use whenData for safety
  //     state = AsyncData(currentData.copyWith(
  //       selectedUser: user,
  //     ));
  //   });
  //   print("ViewModel: User selected - ${user.username}");
  // }

  // Called to dismiss the bottom sheet or deselect user
  // void deselectUser() {
  //   final currentData = state.valueOrNull;
  //   if (currentData == null) return;

  //   // Only change selectedUser, let sheet state be handled separately
  //   if (state.value!.selectedUser != null) {
  //     // Only update if needed
  //     state = AsyncData(state.value!.copyWith(selectedUser: null));
  //     print("ViewModel: User deselected");
  //   }
  // }

  // void deselectUser() {
  //   state.whenData((currentData) {
  //     if (currentData.selectedUser != null) {
  //       // Only update if needed
  //       state = AsyncData(currentData.copyWith(selectedUser: null));
  //       print("ViewModel: User deselected");
  //     }
  //   });
  // }

  // Called to manually expand/collapse bottom sheet (e.g., drag handle)
  // void setBottomSheetExpanded(bool isExpanded) {
  //   final currentData = state.valueOrNull;
  //   if (currentData == null) return;

  //   if (state.hasValue && state.value!.isBottomSheetExpanded != isExpanded) {
  //     // Only update if changed
  //     state =
  //         AsyncData(state.value!.copyWith(isBottomSheetExpanded: isExpanded));
  //     print("ViewModel: Bottom Sheet Expanded State = $isExpanded");
  //   }
  // }

  // SOLELY responsible for the expanded state (used by FABs)
  void setBottomSheetExpanded(bool isExpanded) {
    state.whenData((currentData) {
      if (currentData.isBottomSheetExpanded != isExpanded) {
        // Only update if changed
        state =
            AsyncData(currentData.copyWith(isBottomSheetExpanded: isExpanded));
        print("ViewModel: Bottom Sheet Expanded State = $isExpanded");
      }
    });
  }

  // Called after CitySearchPage returns a result
  Future<void> centerMapOnLocation(
      GeoPoint location, String cityName, double radiusMeters) async {
    final currentData = state.valueOrNull;
    if (currentData == null) {
      print("centerMapOnLocation skipped: State is not AsyncData");
      return;
    }

    print("ViewModel: Centering map on $cityName, Radius: $radiusMeters");
    // Update state: Set new center AND the display fields
    state = AsyncData(currentData.copyWith(
      currentMapCenter: location,
      displayCityName: cityName, // Update display
      displaySearchRadiusMeters: radiusMeters, // Update display
      currentSearchRadius: radiusMeters, // Also update the actual search radius
      isBottomSheetExpanded: false,
    ));
    // Fetch users for the new location/radius
    await _fetchUsersForArea(center: location, radius: radiusMeters);
  }

  Future<void> applyFiltersAndSearch({
    int? minAge,
    int? maxAge,
    String? gender,
  }) async {
    final currentData = state.valueOrNull;
    if (currentData?.currentMapCenter == null) {
      // Also check map center
      print(
          "applyFiltersAndSearch skipped: State is not AsyncData or map center is null");
      return;
    }

    // Update applied filters in state AND set loading
    state = AsyncData(currentData!.copyWith(
      isFetchingUsers: true,
      fetchUsersError: null,
      appliedMinAge: minAge, // Store applied filters
      appliedMaxAge: maxAge,
      appliedGender: gender,
    ));

    // Fetch with new filters
    await _fetchUsersForArea(
      center: state.value!.currentMapCenter!,
      radius: state.value?.currentSearchRadius ??
          50000, // Use current radius or default
      minAge: minAge,
      maxAge: maxAge,
      gender: gender,
    );
  }

  // --- Clear Filters ---
  Future<void> clearFiltersAndSearch() async {
    final currentData = state.valueOrNull;
    if (currentData?.currentMapCenter == null) return;

    // Clear applied filters in state AND set loading
    state = AsyncData(currentData!.copyWith(
      isFetchingUsers: true,
      fetchUsersError: null,
      appliedMinAge: null, // Clear filters
      appliedMaxAge: null,
      appliedGender: null,
    ));

    // Fetch without filters
    await _fetchUsersForArea(
      center: currentData.currentMapCenter!,
      radius: currentData.currentSearchRadius ?? 50000,
      minAge: null, // Pass nulls
      maxAge: null,
      gender: null,
    );
  }

  // -------------- INTERNAL HELPER METHODS --------------
  // Fetches users based on location and current filters
  Future<void> _fetchUsersForArea({
    required GeoPoint center,
    required double radius,
    int? minAge, // Pass filters through
    int? maxAge,
    String? gender,
  }) async {
    if (!state.hasValue) return; // Don't fetch if initial state failed

    // Update state to indicate loading FOR USER FETCH specifically
    state = AsyncValue.data(
        state.value!.copyWith(isFetchingUsers: true, fetchUsersError: null));

    // --- USE DUMMY DATA IF FLAG IS SET ---
    if (_useDummyData) {
      print(" MOCK: Using dummy user data for map.");
      await Future.delayed(
          const Duration(milliseconds: 500)); // Simulate network delay
      state = AsyncData(state.value!.copyWith(
        // nearbyUsers: _dummyNearbyUsers, // Use dummy list
        nearbyUsers: [], // Use dummy list
        isFetchingUsers: false,
        fetchUsersError: null,
        currentSearchRadius: radius,
      ));
      return; // Exit early
    }
    // --- END DUMMY DATA BLOCK ---

    final result = await _userRepository.searchUsersByLocation(
      latitude: center.latitude,
      longitude: center.longitude,
      radiusMeters: radius,
      minAge: minAge, // Pass filters
      maxAge: maxAge,
      gender: gender,
      limit: 20, // Add limit
    );

    if (!state.hasValue) {
      return; // Check again in case state changed during await
    }

    result.fold(
      (failure) {
        state = AsyncValue.data(state.value!.copyWith(
            isFetchingUsers: false,
            fetchUsersError: failure.message, // Store specific fetch error
            nearbyUsers: [] // Clear users on error? Or keep old ones? Clear for now.
            ));
      },
      (users) {
        state = AsyncValue.data(state.value!.copyWith(
          nearbyUsers: users,
          isFetchingUsers: false,
          fetchUsersError: null,
          currentSearchRadius:
              radius, // Update radius used for this successful search
        ));
      },
    );
  }

  // Very basic radius calculation - NEEDS REFINEMENT based on map projection/zoom level specifics
  double _calculateRadiusFromZoom(double zoom) {
    // This is a placeholder - real calculation is more complex!
    // Lower zoom = larger area = larger radius
    // Higher zoom = smaller area = smaller radius
    // Example: Map zoom 10 might be ~50km radius, zoom 14 might be ~5km
    if (zoom < 8) return 100000; // 100km
    if (zoom < 11) return 50000; // 50km
    if (zoom < 13) return 10000; // 10km
    if (zoom < 15) return 5000; // 5km
    return 1000; // 1km for very high zoom
  }
}

// ========= OLD BUILD METHOD(working) before custom AppBar Title =============
// @override
// FutureOr<DiscoverState> build() async {
//   _locationService = ref.watch(locationServiceProvider);
//   _userRepository = ref.watch(userRepositoryProvider);
//   ref.onDispose(() {
//     // Dispose timer
//     _mapMoveDebounce?.cancel();
//   });

//   // 1. Try getting precise location first
//   final gpsLocationResult = await _locationService.getCurrentLocation();

//   if (gpsLocationResult.isRight()) {
//     // GPS Success!
//     final locationPoint = gpsLocationResult.getRight().toNullable()!;

//     print("Build Success: Using GPS Location $locationPoint");
//     Future.microtask(
//         () => _fetchUsersForArea(center: locationPoint, radius: 50000));
//     return DiscoverState(
//         userInitialLocation: locationPoint, currentMapCenter: locationPoint);
//     // If getRight().toNullable() somehow resulted in null (shouldn't happen if isRight is true and GeoPoint isn't nullable itself)
//     // Fall through to the failure handling logic below
//   } else {
//     // Explicitly handle the Left case here
//     // --- GPS Failed - Try User's Profile City via Provider ---
//     final failureFromGps = gpsLocationResult.getLeft().toNullable()!;
//     print(
//         "Build Info: GPS failed ($failureFromGps), trying user profile via provider.");

//     try {
//       // 2. Await the User Profile Data. Watch the currentUserProfileProvider
//       // This pauses build until profile is loaded or fails
//       final User? user = await ref.watch(currentUserProfileProvider.future);

//       // 3. Process Profile Data (if loaded successfully)
//       if (user != null && user.city != null && user.city!.isNotEmpty) {
//         print(
//             "Build Info: User profile loaded, attempting to geocode city: ${user.city}");
//         // Returns Future<DiscoverState> or throws via _handleBuildFailure
//         return await _geocodeCityAndBuildState(user.city!, user.location);
//       } else {
//         // Profile loaded but user/city is missing
//         print(
//             "Build Warning: User profile loaded, but user or city is missing.");
//         throw _handleBuildFailure(LocationFailure("User or city not set."),
//             failureFromGps); // Throw final failure
//       }
//     } catch (error) {
//       // Catch errors from awaiting currentUserProfileProvider.future OR from _geocodeCityAndBuildState
//       print("Build Failed: Error during profile fetch/geocode: $error");
//       final displayError =
//           error is Failure ? error : DatabaseFailure(error.toString());
//       // Throw the failure via the helper to ensure consistent error state
//       throw _handleBuildFailure(displayError, failureFromGps);
//     }
//   } // End else (GPS Failed)
// } // End build
