// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$discoverViewModelHash() => r'd4cfa020d6bb246dfad40dc905c3eb78ef08f9db';

/// See also [DiscoverViewModel].
@ProviderFor(DiscoverViewModel)
final discoverViewModelProvider =
    AutoDisposeAsyncNotifierProvider<DiscoverViewModel, DiscoverState>.internal(
  DiscoverViewModel.new,
  name: r'discoverViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$discoverViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DiscoverViewModel = AutoDisposeAsyncNotifier<DiscoverState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
