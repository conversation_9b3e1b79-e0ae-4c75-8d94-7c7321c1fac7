// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discover_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$discoverViewModelHash() => r'c91c506e73eab1e6571fcbe1250a38efda9d4d4c';

/// See also [DiscoverViewModel].
@ProviderFor(DiscoverViewModel)
final discoverViewModelProvider =
    AutoDisposeAsyncNotifierProvider<DiscoverViewModel, DiscoverState>.internal(
  DiscoverViewModel.new,
  name: r'discoverViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$discoverViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DiscoverViewModel = AutoDisposeAsyncNotifier<DiscoverState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
