// lib/features/discover/state/discover_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/user.dart';

part 'discover_state.freezed.dart';

@freezed
abstract class DiscoverState with _$DiscoverState {
  const factory DiscoverState({
    // Map state
    GeoPoint? currentMapCenter,
    @Default(12.0) double currentZoom, // Default zoom level
    double? currentSearchRadius, // Radius used for the last search (in meters)

    // Initial/Explicit Search Context
    GeoPoint? userInitialLocation, // Result of initial GPS check
    String?
        displayCityName, // City name to DISPLAY in AppBar (from init or search)
    double?
        displaySearchRadiusMeters, // Radius to DISPLAY in AppBar (from init or search)

    // User/Data state
    @Default([]) List<User> nearbyUsers, // Users currently displayed/fetched

    // --- Applied Filter State ---
    int? appliedMinAge,
    int? appliedMaxAge,
    String? appliedGender,

    // UI state
    @Default(false) bool isBottomSheetExpanded,
    @Default(false) bool isFetchingUsers, // Specific loading for user fetch
    String? fetchUsersError, // Specific error for user fetch
  }) = _DiscoverState;
}
