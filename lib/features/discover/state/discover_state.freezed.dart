// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'discover_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DiscoverState {
// Map state
  GeoPoint? get currentMapCenter;
  double get currentZoom; // Default zoom level
  double?
      get currentSearchRadius; // Radius used for the last search (in meters)
// Initial/Explicit Search Context
  GeoPoint? get userInitialLocation; // Result of initial GPS check
  String?
      get displayCityName; // City name to DISPLAY in AppBar (from init or search)
  double?
      get displaySearchRadiusMeters; // Radius to DISPLAY in AppBar (from init or search)
// User/Data state
  List<User> get nearbyUsers; // Users currently displayed/fetched
// --- Applied Filter State ---
  int? get appliedMinAge;
  int? get appliedMaxAge;
  String? get appliedGender; // UI state
  bool get isBottomSheetExpanded;
  bool get isFetchingUsers; // Specific loading for user fetch
  String? get fetchUsersError;

  /// Create a copy of DiscoverState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DiscoverStateCopyWith<DiscoverState> get copyWith =>
      _$DiscoverStateCopyWithImpl<DiscoverState>(
          this as DiscoverState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DiscoverState &&
            (identical(other.currentMapCenter, currentMapCenter) ||
                other.currentMapCenter == currentMapCenter) &&
            (identical(other.currentZoom, currentZoom) ||
                other.currentZoom == currentZoom) &&
            (identical(other.currentSearchRadius, currentSearchRadius) ||
                other.currentSearchRadius == currentSearchRadius) &&
            (identical(other.userInitialLocation, userInitialLocation) ||
                other.userInitialLocation == userInitialLocation) &&
            (identical(other.displayCityName, displayCityName) ||
                other.displayCityName == displayCityName) &&
            (identical(other.displaySearchRadiusMeters,
                    displaySearchRadiusMeters) ||
                other.displaySearchRadiusMeters == displaySearchRadiusMeters) &&
            const DeepCollectionEquality()
                .equals(other.nearbyUsers, nearbyUsers) &&
            (identical(other.appliedMinAge, appliedMinAge) ||
                other.appliedMinAge == appliedMinAge) &&
            (identical(other.appliedMaxAge, appliedMaxAge) ||
                other.appliedMaxAge == appliedMaxAge) &&
            (identical(other.appliedGender, appliedGender) ||
                other.appliedGender == appliedGender) &&
            (identical(other.isBottomSheetExpanded, isBottomSheetExpanded) ||
                other.isBottomSheetExpanded == isBottomSheetExpanded) &&
            (identical(other.isFetchingUsers, isFetchingUsers) ||
                other.isFetchingUsers == isFetchingUsers) &&
            (identical(other.fetchUsersError, fetchUsersError) ||
                other.fetchUsersError == fetchUsersError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentMapCenter,
      currentZoom,
      currentSearchRadius,
      userInitialLocation,
      displayCityName,
      displaySearchRadiusMeters,
      const DeepCollectionEquality().hash(nearbyUsers),
      appliedMinAge,
      appliedMaxAge,
      appliedGender,
      isBottomSheetExpanded,
      isFetchingUsers,
      fetchUsersError);

  @override
  String toString() {
    return 'DiscoverState(currentMapCenter: $currentMapCenter, currentZoom: $currentZoom, currentSearchRadius: $currentSearchRadius, userInitialLocation: $userInitialLocation, displayCityName: $displayCityName, displaySearchRadiusMeters: $displaySearchRadiusMeters, nearbyUsers: $nearbyUsers, appliedMinAge: $appliedMinAge, appliedMaxAge: $appliedMaxAge, appliedGender: $appliedGender, isBottomSheetExpanded: $isBottomSheetExpanded, isFetchingUsers: $isFetchingUsers, fetchUsersError: $fetchUsersError)';
  }
}

/// @nodoc
abstract mixin class $DiscoverStateCopyWith<$Res> {
  factory $DiscoverStateCopyWith(
          DiscoverState value, $Res Function(DiscoverState) _then) =
      _$DiscoverStateCopyWithImpl;
  @useResult
  $Res call(
      {GeoPoint? currentMapCenter,
      double currentZoom,
      double? currentSearchRadius,
      GeoPoint? userInitialLocation,
      String? displayCityName,
      double? displaySearchRadiusMeters,
      List<User> nearbyUsers,
      int? appliedMinAge,
      int? appliedMaxAge,
      String? appliedGender,
      bool isBottomSheetExpanded,
      bool isFetchingUsers,
      String? fetchUsersError});
}

/// @nodoc
class _$DiscoverStateCopyWithImpl<$Res>
    implements $DiscoverStateCopyWith<$Res> {
  _$DiscoverStateCopyWithImpl(this._self, this._then);

  final DiscoverState _self;
  final $Res Function(DiscoverState) _then;

  /// Create a copy of DiscoverState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentMapCenter = freezed,
    Object? currentZoom = null,
    Object? currentSearchRadius = freezed,
    Object? userInitialLocation = freezed,
    Object? displayCityName = freezed,
    Object? displaySearchRadiusMeters = freezed,
    Object? nearbyUsers = null,
    Object? appliedMinAge = freezed,
    Object? appliedMaxAge = freezed,
    Object? appliedGender = freezed,
    Object? isBottomSheetExpanded = null,
    Object? isFetchingUsers = null,
    Object? fetchUsersError = freezed,
  }) {
    return _then(_self.copyWith(
      currentMapCenter: freezed == currentMapCenter
          ? _self.currentMapCenter
          : currentMapCenter // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      currentZoom: null == currentZoom
          ? _self.currentZoom
          : currentZoom // ignore: cast_nullable_to_non_nullable
              as double,
      currentSearchRadius: freezed == currentSearchRadius
          ? _self.currentSearchRadius
          : currentSearchRadius // ignore: cast_nullable_to_non_nullable
              as double?,
      userInitialLocation: freezed == userInitialLocation
          ? _self.userInitialLocation
          : userInitialLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      displayCityName: freezed == displayCityName
          ? _self.displayCityName
          : displayCityName // ignore: cast_nullable_to_non_nullable
              as String?,
      displaySearchRadiusMeters: freezed == displaySearchRadiusMeters
          ? _self.displaySearchRadiusMeters
          : displaySearchRadiusMeters // ignore: cast_nullable_to_non_nullable
              as double?,
      nearbyUsers: null == nearbyUsers
          ? _self.nearbyUsers
          : nearbyUsers // ignore: cast_nullable_to_non_nullable
              as List<User>,
      appliedMinAge: freezed == appliedMinAge
          ? _self.appliedMinAge
          : appliedMinAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedMaxAge: freezed == appliedMaxAge
          ? _self.appliedMaxAge
          : appliedMaxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedGender: freezed == appliedGender
          ? _self.appliedGender
          : appliedGender // ignore: cast_nullable_to_non_nullable
              as String?,
      isBottomSheetExpanded: null == isBottomSheetExpanded
          ? _self.isBottomSheetExpanded
          : isBottomSheetExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingUsers: null == isFetchingUsers
          ? _self.isFetchingUsers
          : isFetchingUsers // ignore: cast_nullable_to_non_nullable
              as bool,
      fetchUsersError: freezed == fetchUsersError
          ? _self.fetchUsersError
          : fetchUsersError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _DiscoverState implements DiscoverState {
  const _DiscoverState(
      {this.currentMapCenter,
      this.currentZoom = 12.0,
      this.currentSearchRadius,
      this.userInitialLocation,
      this.displayCityName,
      this.displaySearchRadiusMeters,
      final List<User> nearbyUsers = const [],
      this.appliedMinAge,
      this.appliedMaxAge,
      this.appliedGender,
      this.isBottomSheetExpanded = false,
      this.isFetchingUsers = false,
      this.fetchUsersError})
      : _nearbyUsers = nearbyUsers;

// Map state
  @override
  final GeoPoint? currentMapCenter;
  @override
  @JsonKey()
  final double currentZoom;
// Default zoom level
  @override
  final double? currentSearchRadius;
// Radius used for the last search (in meters)
// Initial/Explicit Search Context
  @override
  final GeoPoint? userInitialLocation;
// Result of initial GPS check
  @override
  final String? displayCityName;
// City name to DISPLAY in AppBar (from init or search)
  @override
  final double? displaySearchRadiusMeters;
// Radius to DISPLAY in AppBar (from init or search)
// User/Data state
  final List<User> _nearbyUsers;
// Radius to DISPLAY in AppBar (from init or search)
// User/Data state
  @override
  @JsonKey()
  List<User> get nearbyUsers {
    if (_nearbyUsers is EqualUnmodifiableListView) return _nearbyUsers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_nearbyUsers);
  }

// Users currently displayed/fetched
// --- Applied Filter State ---
  @override
  final int? appliedMinAge;
  @override
  final int? appliedMaxAge;
  @override
  final String? appliedGender;
// UI state
  @override
  @JsonKey()
  final bool isBottomSheetExpanded;
  @override
  @JsonKey()
  final bool isFetchingUsers;
// Specific loading for user fetch
  @override
  final String? fetchUsersError;

  /// Create a copy of DiscoverState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DiscoverStateCopyWith<_DiscoverState> get copyWith =>
      __$DiscoverStateCopyWithImpl<_DiscoverState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DiscoverState &&
            (identical(other.currentMapCenter, currentMapCenter) ||
                other.currentMapCenter == currentMapCenter) &&
            (identical(other.currentZoom, currentZoom) ||
                other.currentZoom == currentZoom) &&
            (identical(other.currentSearchRadius, currentSearchRadius) ||
                other.currentSearchRadius == currentSearchRadius) &&
            (identical(other.userInitialLocation, userInitialLocation) ||
                other.userInitialLocation == userInitialLocation) &&
            (identical(other.displayCityName, displayCityName) ||
                other.displayCityName == displayCityName) &&
            (identical(other.displaySearchRadiusMeters,
                    displaySearchRadiusMeters) ||
                other.displaySearchRadiusMeters == displaySearchRadiusMeters) &&
            const DeepCollectionEquality()
                .equals(other._nearbyUsers, _nearbyUsers) &&
            (identical(other.appliedMinAge, appliedMinAge) ||
                other.appliedMinAge == appliedMinAge) &&
            (identical(other.appliedMaxAge, appliedMaxAge) ||
                other.appliedMaxAge == appliedMaxAge) &&
            (identical(other.appliedGender, appliedGender) ||
                other.appliedGender == appliedGender) &&
            (identical(other.isBottomSheetExpanded, isBottomSheetExpanded) ||
                other.isBottomSheetExpanded == isBottomSheetExpanded) &&
            (identical(other.isFetchingUsers, isFetchingUsers) ||
                other.isFetchingUsers == isFetchingUsers) &&
            (identical(other.fetchUsersError, fetchUsersError) ||
                other.fetchUsersError == fetchUsersError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentMapCenter,
      currentZoom,
      currentSearchRadius,
      userInitialLocation,
      displayCityName,
      displaySearchRadiusMeters,
      const DeepCollectionEquality().hash(_nearbyUsers),
      appliedMinAge,
      appliedMaxAge,
      appliedGender,
      isBottomSheetExpanded,
      isFetchingUsers,
      fetchUsersError);

  @override
  String toString() {
    return 'DiscoverState(currentMapCenter: $currentMapCenter, currentZoom: $currentZoom, currentSearchRadius: $currentSearchRadius, userInitialLocation: $userInitialLocation, displayCityName: $displayCityName, displaySearchRadiusMeters: $displaySearchRadiusMeters, nearbyUsers: $nearbyUsers, appliedMinAge: $appliedMinAge, appliedMaxAge: $appliedMaxAge, appliedGender: $appliedGender, isBottomSheetExpanded: $isBottomSheetExpanded, isFetchingUsers: $isFetchingUsers, fetchUsersError: $fetchUsersError)';
  }
}

/// @nodoc
abstract mixin class _$DiscoverStateCopyWith<$Res>
    implements $DiscoverStateCopyWith<$Res> {
  factory _$DiscoverStateCopyWith(
          _DiscoverState value, $Res Function(_DiscoverState) _then) =
      __$DiscoverStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GeoPoint? currentMapCenter,
      double currentZoom,
      double? currentSearchRadius,
      GeoPoint? userInitialLocation,
      String? displayCityName,
      double? displaySearchRadiusMeters,
      List<User> nearbyUsers,
      int? appliedMinAge,
      int? appliedMaxAge,
      String? appliedGender,
      bool isBottomSheetExpanded,
      bool isFetchingUsers,
      String? fetchUsersError});
}

/// @nodoc
class __$DiscoverStateCopyWithImpl<$Res>
    implements _$DiscoverStateCopyWith<$Res> {
  __$DiscoverStateCopyWithImpl(this._self, this._then);

  final _DiscoverState _self;
  final $Res Function(_DiscoverState) _then;

  /// Create a copy of DiscoverState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentMapCenter = freezed,
    Object? currentZoom = null,
    Object? currentSearchRadius = freezed,
    Object? userInitialLocation = freezed,
    Object? displayCityName = freezed,
    Object? displaySearchRadiusMeters = freezed,
    Object? nearbyUsers = null,
    Object? appliedMinAge = freezed,
    Object? appliedMaxAge = freezed,
    Object? appliedGender = freezed,
    Object? isBottomSheetExpanded = null,
    Object? isFetchingUsers = null,
    Object? fetchUsersError = freezed,
  }) {
    return _then(_DiscoverState(
      currentMapCenter: freezed == currentMapCenter
          ? _self.currentMapCenter
          : currentMapCenter // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      currentZoom: null == currentZoom
          ? _self.currentZoom
          : currentZoom // ignore: cast_nullable_to_non_nullable
              as double,
      currentSearchRadius: freezed == currentSearchRadius
          ? _self.currentSearchRadius
          : currentSearchRadius // ignore: cast_nullable_to_non_nullable
              as double?,
      userInitialLocation: freezed == userInitialLocation
          ? _self.userInitialLocation
          : userInitialLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      displayCityName: freezed == displayCityName
          ? _self.displayCityName
          : displayCityName // ignore: cast_nullable_to_non_nullable
              as String?,
      displaySearchRadiusMeters: freezed == displaySearchRadiusMeters
          ? _self.displaySearchRadiusMeters
          : displaySearchRadiusMeters // ignore: cast_nullable_to_non_nullable
              as double?,
      nearbyUsers: null == nearbyUsers
          ? _self._nearbyUsers
          : nearbyUsers // ignore: cast_nullable_to_non_nullable
              as List<User>,
      appliedMinAge: freezed == appliedMinAge
          ? _self.appliedMinAge
          : appliedMinAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedMaxAge: freezed == appliedMaxAge
          ? _self.appliedMaxAge
          : appliedMaxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedGender: freezed == appliedGender
          ? _self.appliedGender
          : appliedGender // ignore: cast_nullable_to_non_nullable
              as String?,
      isBottomSheetExpanded: null == isBottomSheetExpanded
          ? _self.isBottomSheetExpanded
          : isBottomSheetExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingUsers: null == isFetchingUsers
          ? _self.isFetchingUsers
          : isFetchingUsers // ignore: cast_nullable_to_non_nullable
              as bool,
      fetchUsersError: freezed == fetchUsersError
          ? _self.fetchUsersError
          : fetchUsersError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
