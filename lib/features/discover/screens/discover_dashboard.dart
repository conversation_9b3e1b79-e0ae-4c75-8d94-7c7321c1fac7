// lib/features/discover/screens/discover_dashboard.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/app_location.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/constants/constants.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/utils/map_utils.dart';
import 'package:watermelon_draft/core/utils/utils.dart';
import 'package:watermelon_draft/features/discover/state/discover_state.dart';
import 'package:watermelon_draft/features/discover/viewmodels/discover_viewmodel.dart';
import 'package:beamer/beamer.dart';
import 'package:rxdart/rxdart.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
import 'package:watermelon_draft/features/profile/screens/profile_screen.dart';
import 'package:watermelon_draft/features/profile/widgets/user_profile_card.dart';
import 'package:watermelon_draft/features/search/screens/city_search_page.dart';
import 'package:watermelon_draft/features/search/screens/filter_page.dart';

class DiscoverDashboard extends ConsumerStatefulWidget {
  const DiscoverDashboard({super.key});

  @override
  ConsumerState<DiscoverDashboard> createState() => _DiscoverDashboardState();
}

class _DiscoverDashboardState extends ConsumerState<DiscoverDashboard>
    with OSMMixinObserver {
  late final MapController _mapController;
  final _regionSubject = BehaviorSubject<Region?>();
  // --- Define the listener callback function ---
  late VoidCallback _mapRegionListener; // Store the callback
  bool _mapReady = false; // Track if map is ready
  final Set<GeoPoint> _currentMarkerPoints = {};
  final DraggableScrollableController _sheetController =
      DraggableScrollableController();

  @override
  void initState() {
    super.initState();

    // Define the listener callback function
    _mapRegionListener = () {
      final region = _mapController.listenerRegionIsChanging.value;
      if (region != null) {
        _regionSubject.add(region);
      }
    };
    // Setup debounce listener
    _setupDebounceListener();
    // Listener for sheet size changes
    _sheetController.addListener(_handleSheetScroll);

    // --- Initialize MapController DEFINITIVELY here ---
    final defaultInitialPosition =
        GeoPoint(latitude: 37.7749, longitude: -122.4194);
    // Read initial state SYNCHRONOUSLY if possible (might be loading initially)
    final initialViewModelState =
        ref.read(discoverViewModelProvider).valueOrNull;
    final initialPosition = initialViewModelState
            ?.currentMapCenter // Use currentMapCenter from potentially loaded state
        ??
        initialViewModelState?.userInitialLocation ??
        defaultInitialPosition;

    _mapController = MapController(initPosition: initialPosition);

    // --- Add Listeners AFTER controller is created ---
    _mapController.listenerRegionIsChanging.addListener(_mapRegionListener);
    _mapController.addObserver(
        this); // <-- REGISTER OBSERVER to the instance created here

    // mapIsReady from the mixin will handle initial setup now
  }

  @override
  void dispose() {
    // --- Remove the listener ---
    _mapController.listenerRegionIsChanging.removeListener(_mapRegionListener);
    _mapController.removeObserver(this);
    _sheetController.removeListener(_handleSheetScroll);
    _regionSubject.close();
    _mapController.dispose();
    _sheetController.dispose();
    super.dispose();
  }

  // --- Implement required OSMMixinObserver methods ---
  @override
  Future<void> mapIsReady(bool isReady) async {
    // This replaces the onMapIsReady callback property
    if (isReady && !_mapReady) {
      _mapReady = true;
      print("Map is ready! (Mixin)");
      final initialPosition =
          ref.read(discoverViewModelProvider).value?.userInitialLocation ??
              _mapController.initPosition;
      if (initialPosition != null) {
        await _mapController.moveTo(initialPosition, animate: true);
        print("Moved map to initial position via Mixin: $initialPosition");
        // Update with initial state users
        final initialUsers =
            ref.read(discoverViewModelProvider).value?.nearbyUsers ?? [];
        _updateMapMarkers(initialUsers);
      }
      // Trigger initial fetch if necessary (ViewModel might handle this)
      _triggerFetchForCurrentMapView();
    }
  }

  @override
  void onSingleTap(GeoPoint position) {
    super.onSingleTap(position);
    // This handles taps on the MAP BACKGROUND only
    print("Map background tapped (onSingleTap): $position");

    // Collapse the results sheet if it's expanded
    if (_sheetController.isAttached && _sheetController.size > 0.15) {
      // Check if expanded
      print("Collapsing bottom sheet due to background tap.");
      _sheetController.animateTo(
        0.1, // Animate to initial/min size
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      // Let _handleSheetScroll update the ViewModel state when animation finishes
    }
  }

  // --- Listener for sheet controller ---
  void _handleSheetScroll() {
    final viewModel = ref.read(discoverViewModelProvider.notifier);
    // Determine if expanded based on size relative to min/initial
    final isExpanded = _sheetController.size > 0.15; // Threshold above initial
    // viewModel.setBottomSheetExpanded(isExpanded); // Update VM state for FABs
    // Read current state without watching to avoid loops if possible
    final currentStateExpanded = ref
            .read(discoverViewModelProvider)
            .valueOrNull
            ?.isBottomSheetExpanded ??
        false;
    if (isExpanded != currentStateExpanded) {
      viewModel.setBottomSheetExpanded(isExpanded);
    }
  }

  void _setupDebounceListener() {
    _regionSubject
        .debounceTime(const Duration(milliseconds: 750))
        .listen((region) async {
      // Use null-safe access for controller
      if (region != null && mounted) {
        final viewModel = ref.read(discoverViewModelProvider.notifier);
        try {
          // Get zoom AFTER map has likely settled
          double currentZoom =
              await _mapController.getZoom(); // Use ! safely here
          print(
              "Debounced Region Change: ${region.center}, Zoom: $currentZoom");
          viewModel.mapPositionChanged(region.center, currentZoom);
        } catch (e) {
          print("Error getting zoom after region change: $e");
          // Fallback zoom if getZoom fails
          viewModel.mapPositionChanged(region.center, 12.0); // Fallback zoom
        }
      }
    });
  }

  // --- Helper to show profile bottom sheet ---
  void _showUserProfileSheet(BuildContext context, User user) {
    // Consider using the modal_bottom_sheet package for better aesthetics/behavior
    // showMaterialModalBottomSheet(
    //   // Or standard showModalBottomSheet
    //   context: context,
    //   // expand: true, // uncomment to make it full screen potentially
    //   // backgroundColor: Colors.transparent, // For custom shape
    //   builder: (context) => Container(
    //     height: MediaQuery.of(context).size.height * 0.85, // Example height
    //     // Wrap with Navigator to handle potential nested navigation within profile
    //     child: Navigator(
    //         onGenerateRoute: (settings) => MaterialPageRoute(
    //             builder: (context) => ProfileScreen(userId: user.userId),
    //             settings: settings)),
    //   ),
    // );

    // --- OR Using standard bottom sheet ---
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows custom height
      builder: (context) => SizedBox(
          height: MediaQuery.of(context).size.height *
              0.9, // Take most screen height
          child: ProfileScreen(userId: user.userId) // Embed profile screen
          ),
    );
  }

  // Add this method INSIDE _DiscoverDashboardState class

  Future<void> _centerMapOnUserLocation() async {
    // Show a temporary loading indicator maybe? (Optional)
    if (!mounted) return; // Extra safety check
    // ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Getting current location...'), duration: Duration(seconds: 1)));

    final locationService = ref.read(locationServiceProvider);
    final currentLocationResult = await locationService.getCurrentLocation();

    // Check if the widget is still mounted AFTER the await
    if (!mounted) return;

    currentLocationResult.fold(
      (failure) {
        // Show specific error message based on Failure type
        String errorMessage = 'Could not get current location.'; // Default
        VoidCallback? action;
        String? actionLabel;

        if (failure is LocationServiceDisabledFailure) {
          errorMessage = failure.message;
          actionLabel = 'Settings';
          action = () => locationService.openAppSettings();
        } else if (failure is LocationPermissionDeniedFailure) {
          errorMessage = failure.message;
          if (failure.isPermanentlyDenied) {
            actionLabel = 'Settings';
            action = () => locationService.openAppSettings();
          }
        } else if (failure is LocationTimeoutFailure) {
          errorMessage = failure.message;
        } else {
          errorMessage = failure.message;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            action: (action != null && actionLabel != null)
                ? SnackBarAction(label: actionLabel, onPressed: action)
                : null,
          ),
        );
      },
      (point) {
        // Success - Move the map
        if (_mapReady) {
          _mapController.moveTo(point, animate: true);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final stateAsync = ref.watch(discoverViewModelProvider);
    final stateValue = stateAsync.valueOrNull; // Get state data safely

    // Listen for user fetch errors to show snackbar? Optional.
    ref.listen(
        discoverViewModelProvider.select((s) => s.valueOrNull?.fetchUsersError),
        (prev, next) {
      if (next != null && next != prev && mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text(next)));
      }
    });

    int activeFilterCount = 0;
    if (stateValue != null) {
      if (stateValue.appliedMinAge != null ||
          stateValue.appliedMaxAge != null) {
        // Consider age range active if either min or max is set (or not default)
        if (stateValue.appliedMinAge != defaultFilterMinAge.round() ||
            stateValue.appliedMaxAge != defaultFilterMaxAge.round()) {
          activeFilterCount++;
        }
      }
      // Check gender against default from filter_page.dart
      if (stateValue.appliedGender != null &&
          stateValue.appliedGender != defaultFilterGender) {
        activeFilterCount++;
      }
    }

    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        title: stateAsync.maybeWhen(
            data: (state) => _buildAppBarTitle(context, state), // Use helper
            orElse: () => const Text("Discover by city") // Fallback title
            ),
        actions: [
          // --- Filter Button ---
          Badge(
            label: Text(activeFilterCount.toString()), // Show count
            isLabelVisible: activeFilterCount > 0, // Show only if count > 0
            child: IconButton(
              icon: const Icon(Icons.filter_list),
              tooltip: 'Filter',
              onPressed: () async {
                // Capture context before await
                final currentContext = context;
                final viewModelNotifier =
                    ref.read(discoverViewModelProvider.notifier);
                final currentFilters = ref
                    .read(discoverViewModelProvider)
                    .valueOrNull; // Get current applied filters

                // Make async to await result
                // Navigate to FilterPage, passing CURRENT applied filters
                // final result = await context.beamToNamed('/filter', data: {
                //   'initialMinAge': stateValue?.appliedMinAge,
                //   'initialMaxAge': stateValue?.appliedMaxAge,
                //   'initialGender': stateValue?.appliedGender,
                // }); // TODO: Check how to pass/receive data with beamToNamed or use Navigator.push

                // TODO: TEMPORARY - Using Navigator.push to get result easily for now
                // Replace with Beamer's state management or query params later if needed

                // Navigate to FilterPage, passing CURRENT applied filters
                final filterResult =
                    await Navigator.push<Map<String, dynamic>?>(
                  currentContext, // Use captured context
                  MaterialPageRoute(
                      builder: (context) => FilterPage(
                            // Pass currently applied filters from ViewModel state
                            initialMinAge: currentFilters?.appliedMinAge,
                            initialMaxAge: currentFilters?.appliedMaxAge,
                            initialGender: currentFilters?.appliedGender,
                          )),
                );

                // Handle results returned from FilterPage
                if (filterResult != null && currentContext.mounted) {
                  // Check mounted
                  print("Filter Returned: $filterResult");
                  // Call ViewModel method to apply filters and trigger search
                  viewModelNotifier.applyFiltersAndSearch(
                    minAge: filterResult['minAge'], // Can be null
                    maxAge: filterResult['maxAge'], // Can be null
                    gender: filterResult[
                        'gender'], // Can be null ('Any' returns null)
                  );
                }
              },
            ),
          ),

          // --- Notifications Button ---
          IconButton(
            // TODO: Potentially wrap with a Badge using a notification count provider
            icon: const Icon(Icons.notifications_none),
            tooltip: 'Notifications',
            onPressed: () {
              // Navigate to the DiscoverNotificationsPage placeholder
              context.beamToNamed('/notifications/discover');
            },
          ),
        ],
      ),
      body: switch (stateAsync) {
        AsyncData(:final value) => // value is DiscoverState (Success)
          _buildMapContent(context, ref, value), // Build map content

        AsyncError(:final error) => Center(
            child: (error is LocationServiceDisabledFailure)
                ? _buildEnableLocationServicePrompt(context, ref) // Pass ref
                : (error is LocationPermissionDeniedFailure)
                    ? _buildGrantPermissionPrompt(
                        context, ref, error.isPermanentlyDenied) // Pass ref
                    // Display a generic error for other Failure types or unknown errors
                    : Text(
                        'Error loading map: ${error is Failure ? error.message : error.toString()}'),
          ),
        _ => const Center(child: CircularProgressIndicator()), // Loading state
      },
    );
  }

  // --- Helper methods for error prompts ---
  Widget _buildEnableLocationServicePrompt(
      BuildContext context, WidgetRef ref) {
    // Accept ref
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Icon(Icons.location_off, size: 50, color: Colors.grey),
        SizedBox(height: 16),
        Text('Location Services Disabled',
            style: Theme.of(context).textTheme.headlineSmall),
        SizedBox(height: 8),
        Text(
            'Please enable location services in your device settings to use the Discover feature.',
            textAlign: TextAlign.center),
        SizedBox(height: 16),
        ElevatedButton(
          onPressed: () =>
              ref.read(locationServiceProvider).openAppSettings(), // Use ref
          child: Text('Open Settings'),
        )
      ]),
    );
  }

  Widget _buildGrantPermissionPrompt(
      BuildContext context, WidgetRef ref, bool permanentlyDenied) {
    // Accept ref
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Icon(Icons.location_searching, size: 50, color: Colors.grey),
        SizedBox(height: 16),
        Text('Location Permission Required',
            style: Theme.of(context).textTheme.headlineSmall),
        SizedBox(height: 8),
        Text(
            permanentlyDenied
                ? 'You have permanently denied location permission. Please enable it in your app settings.'
                : 'Watermelon needs location permission to find nearby users and activities.',
            textAlign: TextAlign.center),
        SizedBox(height: 16),
        ElevatedButton(
          onPressed: permanentlyDenied
              ? () =>
                  ref.read(locationServiceProvider).openAppSettings() // Use ref
              : () async {
                  // Request permission again
                  final granted = await ref
                      .read(locationServiceProvider)
                      .requestPermission(); // Use ref
                  if (granted) {
                    ref.invalidate(
                        discoverViewModelProvider); // Refresh the view model
                  }
                },
          child: Text(permanentlyDenied ? 'Open Settings' : 'Grant Permission'),
        )
      ]),
    );
  }

  Widget _buildMapContent(
      BuildContext context, WidgetRef ref, DiscoverState state) {
    // Controller is initialized in initState
    // if (_mapController == null) {
    //   // Should ideally not happen now
    //   return const Center(child: Text("Initializing map..."));
    // }

    final List<User> nearbyUsers = state.nearbyUsers;

    return Stack(
      // Use Stack to layer map and bottom sheet
      children: [
        // --- Map Layer ---
        OSMFlutter(
          key: const ValueKey('discover_map'),
          controller: _mapController, // Use the initialized controller
          osmOption: OSMOption(
            userTrackingOption: UserTrackingOption(
              enableTracking: true,
              unFollowUser: false,
            ),
            zoomOption: ZoomOption(
              initZoom: state.currentZoom, // Use state value (double)
              minZoomLevel: 3.0,
              maxZoomLevel: 19.0, // Adjust as needed
              stepZoom: 1.0,
            ),
            showContributorBadgeForOSM: true,
            showDefaultInfoWindow: false,
          ),

          // Add marker tap listener HERE
          onGeoPointClicked: (tappedPoint) async {
            final BuildContext currentContext = context;
            // Check if mounted right away
            if (!currentContext.mounted) return;

            // Make async for distance calculation
            final nearbyUsers =
                ref.read(discoverViewModelProvider).value?.nearbyUsers ?? [];
            User? closestUser;
            double minDistanceKmSq =
                0.0025; // Threshold 50m squared (0.05km * 0.05km) - ADJUST AS NEEDED!
            double currentMinDistSq = double.infinity;

            print("Marker tap registered at (onGeoPointClicked): $tappedPoint");

            // Find the closest user marker within the threshold
            for (final user in nearbyUsers) {
              if (user.location != null) {
                try {
                  // Calculate distance using plugin helper (returns Future<double> in KM)
                  double distanceKm = await distance2point(tappedPoint,
                      AppLocationConverters.toGeoPoint(user.location!));
                  double distKmSq = distanceKm * distanceKm;

                  // Check if this marker is within threshold AND closer than previous closest
                  if (distKmSq < minDistanceKmSq &&
                      distKmSq < currentMinDistSq) {
                    currentMinDistSq = distKmSq;
                    closestUser = user;
                  }
                } catch (e) {
                  print("Error calculating distance for marker tap: $e");
                }
              }
            }

            // Check captured context mounted AFTER awaits
            if (!currentContext.mounted) return;

            // If a user was found within the threshold, show their profile
            if (closestUser != null) {
              print(
                  "Marker tap corresponds to user (proximity): ${closestUser.username}");
              // Show the modal profile sheet
              _showUserProfileSheet(currentContext, closestUser);
            } else {
              // No marker was close enough to the tap point.
              // Do nothing here - the background tap is handled by onSingleTap override.
              print(
                  "Tap registered by onGeoPointClicked, but no marker within threshold.");
            }
          }, // End onGeoPointClicked
        ),

        // --- Draggable Bottom Sheet Layer ---------------------------
        DraggableScrollableSheet(
          controller: _sheetController,
          initialChildSize: 0.1,
          minChildSize: 0.1,
          maxChildSize: .9,
          snap: true,
          snapSizes: [0.1, 0.2, 0.9],
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .scaffoldBackgroundColor, // Use theme background
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.0),
                    topRight: Radius.circular(16.0),
                  ),
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 10.0,
                      color: Colors.black.withValues(alpha: .1),
                    )
                  ],
                ),
                child: Column(
                  children: [
                    // --- Drag Handle ---
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 8.0),
                      height: 5,
                      width: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    Expanded(
                      child: nearbyUsers.isEmpty
                          ? Center(
                              // Show message if no users found
                              child: state.isFetchingUsers
                                  ? CircularProgressIndicator() // Show loading if fetch is in progress
                                  : Text("No users found nearby.",
                                      style: TextStyle(color: Colors.grey)),
                            )
                          : ListView.builder(
                              controller:
                                  scrollController, // STILL USE controller here
                              // ... itemBuilder creating UserProfileCard (medium) ...
                              itemCount: nearbyUsers.length,
                              itemBuilder: (BuildContext context, int index) {
                                final user = nearbyUsers[index];
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0, vertical: 4.0),
                                  // Use your UserProfileCard widget (Medium Size)
                                  child: UserProfileCard(
                                    user: user,
                                    size: CardSize
                                        .medium, // Assuming you have size enum
                                    onTap: () {
                                      // Navigate to full profile when list item is tapped
                                      context.beamToNamed(
                                          '/profile/${user.userId}');
                                    },
                                  ),
                                );
                              },
                            ),
                    )
                  ],
                ));
          },
        ),

        // --- Condensed Card Overlay ------------------------
        // Positioned(
        //   bottom: MediaQuery.of(context).size.height * 0.1 + 5.0,
        //   left: 15.0,
        //   right: 15.0,
        //   child: Visibility(
        //     // Show only if user selected
        //     visible:
        //         selectedUser != null, // Visibility controlled by selectedUser
        //     maintainState: true, // Keep state even when hidden
        //     maintainAnimation: true,
        //     child: selectedUser != null
        //         ? GestureDetector(
        //             onTap: () {
        //               context.beamToNamed('/profile/${selectedUser.userId}');
        //             },
        //             child: UserProfileCard(
        //               user: selectedUser,
        //               size: CardSize.condensed,
        //             ),
        //           )
        //         : const SizedBox.shrink(), // Don't show anything if null
        //   ),
        // ),

        // --- Floating Action Buttons ------------------------
        Positioned(
          bottom: MediaQuery.of(context).size.height * 0.1 +
              5.0, // Adjust position as needed
          right: 16,
          child: Visibility(
            // Hide FABs when sheet is expanded
            visible: !state.isBottomSheetExpanded, // Use ViewModel state
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              FloatingActionButton.small(
                // Smaller FAB
                heroTag: 'center_location_fab', // Unique HeroTag
                onPressed: _centerMapOnUserLocation,
                tooltip: 'My Location',
                child: const Icon(Icons.my_location),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: 'search_users_fab', // Unique HeroTag
                onPressed: () {
                  context.beamToNamed('/search'); // Navigate to SearchPage
                },
                tooltip: 'Search Users',
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.person_search),
                    Text("Search"),
                  ],
                ),
              ),
            ]),
          ),
        ),
      ],
    );
  }

  // --- Helper to build AppBar Title ---
  Widget _buildAppBarTitle(BuildContext context, DiscoverState state) {
    String titleText = "Discover Nearby"; // Default if nothing else is set
    String subtitleText = "";

    if (state.displayCityName != null) {
      titleText = state.displayCityName!;
      if (state.displaySearchRadiusMeters != null) {
        subtitleText =
            "within ${metersToMiles(state.displaySearchRadiusMeters!)}"; // Use helper
      }
    }
    // Else if only currentMapCenter available (after panning), show "Current Map Area"?
    // else if (state.currentMapCenter != null) {
    //    titleText = "Current Map Area";
    // }

    return GestureDetector(
      // Make title tappable
      onTap: () async {
        // 1. Capture context
        // Capture context BEFORE any awaits
        final BuildContext currentContext = context;
        final locationService =
            ref.read(locationServiceProvider); // Get service instance
        final viewModelNotifier =
            ref.read(discoverViewModelProvider.notifier); // Get notifier
        // final currentViewModelState =
        //     ref.read(discoverViewModelProvider).valueOrNull;

        // Determine current center/city to pass as initial values
        GeoPoint? centerToUse;
        String? currentCityName;

        try {
          print("DEBUG: Determining center point...");
          if (_mapReady) {
            // Check map readiness too
            print("DEBUG: Trying map controller center...");
            centerToUse = await _mapController.centerMap;
            print("DEBUG: Map controller center received: $centerToUse");
          }

          if (centerToUse == null) {
            // If map controller didn't provide center, use ViewModel state
            // final currentViewModelState =
            //     ref.read(discoverViewModelProvider).valueOrNull;
            centerToUse = state.currentMapCenter ?? state.userInitialLocation;
            print(
                "DEBUG: Falling back to ViewModel state center: $centerToUse");
          }

          // Now, 'centerToUse' holds GeoPoint?

          // 3. Reverse geocode if center exists
          if (centerToUse != null) {
            // Try reverse geocoding the determined center point
            final placemarkResult = await locationService
                .reverseGeocode(centerToUse); // Pass GeoPoint

            // Check mounted status of the captured context AFTER await
            if (!currentContext.mounted) {
              return; // Exit if widget was removed
            }

            placemarkResult.fold(
                (l) => print("Could not reverse geocode current center: $l"),
                (placemark) {
              if (placemark != null) {
                currentCityName = placemark.locality ??
                    placemark.subAdministrativeArea ??
                    placemark.name;
              }
            });
          }
        } catch (e) {
          print("Error getting initial data for city search: $e");
          // Optional: Show a message if no center could be found?
        }

        // --- 4. Calculate initial radius in miles and CLAMP it ---
        double initialRadiusMilesValue =
            Constants.defaultSearchRadiusMiles; // Default
        if (state.displaySearchRadiusMeters != null) {
          initialRadiusMilesValue =
              (state.displaySearchRadiusMeters! / Constants.metersPerMile);
        }
        // Clamp the value to ensure it's within the slider's bounds
        initialRadiusMilesValue = initialRadiusMilesValue.clamp(
            Constants.minSearchRadiusMiles, Constants.maxSearchRadiusMiles);

        // 5. Check mounted before pushing route
        // Check mounted status again BEFORE pushing route
        if (!currentContext.mounted) {
          print("DEBUG: Context unmounted before Navigator.push");
          return;
        }

        // 6. Navigate and wait for result
        // Use Navigator.push to pass initial data and receive results
        // Use captured context for Navigator.push
        final result = await Navigator.push<Map<String, dynamic>?>(
          currentContext, // Use captured context
          MaterialPageRoute(
            builder: (context) => CitySearchPage(
              // Pass current map center and derived name
              initialGeoPoint: centerToUse,
              initialCityName: currentCityName,
              // Pass clamped value
              initialRadiusMiles: initialRadiusMilesValue,
            ),
          ),
        );
        print("DEBUG: Navigator.push completed. Result: $result");

        // 7. Handle results returned from CitySearchPage
        // Check captured context mounted AFTER push returns
        if (result != null && currentContext.mounted) {
          final GeoPoint? selectedPoint = result['geoPoint'];
          final int? radiusMetersInt =
              result['radiusMeters']; // Comes back as int
          final String? selectedCity = result['cityName'];

          if (selectedPoint != null &&
              radiusMetersInt != null &&
              selectedCity != null) {
            print(
                "City Search Returned: City=$selectedCity, Point=$selectedPoint, RadiusMeters=$radiusMetersInt");

            // Adjust Map View ---
            if (_mapReady) {
              try {
                // 1. Calculate Bounding Box
                final double radiusMeters = radiusMetersInt.toDouble();
                final BoundingBox searchBounds =
                    calculateBoundingBox(selectedPoint, radiusMeters);

                // 2. Zoom Map to Bounding Box
                print("Zooming map to bounds: $searchBounds");
                await _mapController.zoomToBoundingBox(searchBounds,
                    paddinInPixel: 60); // Add padding
              } catch (e) {
                print(
                    "Error zooming to bounding box: $e. Falling back to moveTo.");
                // Fallback: just move center if zoomToBoundingBox fails
                await _mapController.moveTo(selectedPoint, animate: true);
              }
            }

            // 3. Update ViewModel state to center map and trigger fetch for new location
            viewModelNotifier.centerMapOnLocation(selectedPoint, selectedCity,
                radiusMetersInt.toDouble() // Convert int radius to double
                );
          } else {
            print("City Search returned incomplete data: $result");
          }
        }
      },
      child: Container(
        // Search bar look
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white54, // Use light grey background
          // Use theme color
          // color: Theme.of(context).colorScheme.primary.withValues(alpha: .1),
          border: Border.all(
            color: Colors.grey.withValues(alpha: .5),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: .1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          /* ... Icon + Column ... */
          children: [
            Icon(Icons.search_outlined, size: 24, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Flexible(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(titleText,
                      style: Theme.of(context).textTheme.bodyLarge,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1), // Adjusted style
                  if (subtitleText.isNotEmpty)
                    Text(subtitleText,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(color: Colors.grey[700]),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1), // Adjusted style
                ],
              ),
            ),
            // Optional: Add a small dropdown arrow?
            // Icon(Icons.arrow_drop_down, color: Colors.grey[600])
          ],
        ),
      ),
    );
  }

  // --- Helper to trigger fetch based on current map view ---
  Future<void> _triggerFetchForCurrentMapView() async {
    if (!_mapReady) {
      // Added map ready check
      print("WARN: Map controller not ready for fetch trigger.");
      return;
    }
    try {
      // Use Future.wait to get both potentially async values concurrently
      final results = await Future.wait([
        _mapController
            .centerMap, // Assuming this is Future<GeoPoint> or GeoPoint
        _mapController.getZoom() // Assuming this is Future<double> or double
        // Verify actual return types for v1.3.6 - might need separate awaits if not Future
      ]);

      // Ensure results are correct type (add specific casting if needed)
      final GeoPoint center = results[0] as GeoPoint;
      final double zoom = (results[1] as num).toDouble(); // Cast num to double

      print("Triggering fetch for Center: $center, Zoom: $zoom");
      ref
          .read(discoverViewModelProvider.notifier)
          .mapPositionChanged(center, zoom);
    } catch (e) {
      // Log the error but DO NOT trigger a search with default/incorrect location
      print("Error getting map center/zoom for triggering fetch: $e");
      // Optionally show a SnackBar?
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
              'Could not update results for the current map view. Please try moving the map again.'),
          duration: Duration(seconds: 3),
        ));
      }
    }
  }

  Future<void> _updateMapMarkers(List<User> users) async {
    if (!_mapReady || !mounted) return; // Add !mounted check

    print("Updating ${users.length} markers programmatically.");

    // This set now primarily helps us avoid trying to remove markers that don't exist
    // or add markers that already do, though the controller might handle this gracefully.
    final Set<GeoPoint> newPoints = users
        .where((u) => u.location != null)
        .map((u) => AppLocationConverters.toGeoPoint(u.location!))
        .toSet();

    final Map<GeoPoint, MarkerIcon> newMarkersMap = {};
    for (final user in users) {
      if (user.location != null) {
        newMarkersMap[AppLocationConverters.toGeoPoint(user.location!)] =
            MarkerIcon(
                iconWidget: CircleAvatar(
          // Use avatar function
          radius: 24,
          backgroundColor: Colors.transparent, // Make outer transparent
          child: CircleAvatar(
            radius: 22,
            backgroundColor: Colors.white,
            backgroundImage: (user.profilePictureUrl != null &&
                    user.avatarType == 'uploaded')
                ? CachedNetworkImageProvider(user.profilePictureUrl!)
                : (user.avatarType == 'default' &&
                        user.profilePictureUrl != null)
                    ? AssetImage(user.profilePictureUrl!) as ImageProvider
                    : null, // No background image if generated
            child: (user.profilePictureUrl ==
                    null) // Show initials if no image URL
                ? generateAvatar(
                    user.fullName ?? user.username ?? '?',
                    radius: 22, // Adjust radius based on container
                    // Pass the stored color for consistency
                    color: (user.generatedAvatarColor != null)
                        ? Color(int.parse('0x${user.generatedAvatarColor!}'))
                        : null, // Use stored color or let generateAvatar pick random
                  )
                : null,
          ),
        ));
      }
    }

    try {
      // Remove markers that are in the current set but not in the new set
      final pointsToRemove = _currentMarkerPoints.difference(newPoints);
      for (final point in pointsToRemove) {
        await _mapController.removeMarker(point);
        print("Removed marker at: $point");
      }

      // Add markers that are in the new set but not in the current set
      final pointsToAdd = newPoints.difference(_currentMarkerPoints);
      for (final point in pointsToAdd) {
        final icon = newMarkersMap[point];
        if (icon != null) {
          await _mapController.addMarker(point, markerIcon: icon);
          print("Added marker at: $point");
        }
      }

      // Update the internal tracking set - NO setState needed
      _currentMarkerPoints.clear();
      _currentMarkerPoints.addAll(newPoints);
    } catch (e) {
      print("Error updating map markers programmatically: $e");
    }
  }
}
