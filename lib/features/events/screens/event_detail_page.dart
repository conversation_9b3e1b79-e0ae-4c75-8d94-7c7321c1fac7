import 'dart:math';
import 'package:flutter/material.dart';

class EventDetailPage extends StatelessWidget {
  final String eventId; // Add this

  const EventDetailPage({
    super.key,
    required this.eventId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Event Details - ID: ${eventId.substring(0, min(eventId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Event Detail Page Placeholder for Event ID: $eventId'),
      ),
    );
  }
}
