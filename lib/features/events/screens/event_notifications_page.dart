import 'dart:math';
import 'package:flutter/material.dart';

class EventNotificationsPage extends StatelessWidget {
  final String eventId; // Add this

  const EventNotificationsPage({
    super.key,
    required this.eventId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Event Notifications - ID: ${eventId.substring(0, min(eventId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Event Notifications Placeholder for Event ID: $eventId'),
      ),
    );
  }
}
