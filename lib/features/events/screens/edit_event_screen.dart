import 'dart:math';
import 'package:flutter/material.dart';

class EditEventScreen extends StatelessWidget {
  final String eventId; // Add this

  const EditEventScreen({
    super.key,
    required this.eventId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Edit Event - ID: ${eventId.substring(0, min(eventId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Edit Event Screen Placeholder for Event ID: $eventId'),
      ),
    );
  }
}
