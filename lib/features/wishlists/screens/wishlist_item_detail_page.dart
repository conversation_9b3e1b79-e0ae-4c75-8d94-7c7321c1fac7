import 'dart:math';
import 'package:flutter/material.dart';

class WishlistItemDetailPage extends StatelessWidget {
  final String wishlistItemId; // Add this

  const WishlistItemDetailPage({
    super.key,
    required this.wishlistItemId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Wishlist Item - ID: ${wishlistItemId.substring(0, min(wishlistItemId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Wishlist Item Detail Placeholder for ID: $wishlistItemId'),
      ),
    );
  }
}
