import 'dart:math';
import 'package:flutter/material.dart';

class WishlistItemNotificationsPage extends StatelessWidget {
  final String wishlistItemId; // Add this

  const WishlistItemNotificationsPage({
    super.key,
    required this.wishlistItemId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Wishlist Notifications - ID: ${wishlistItemId.substring(0, min(wishlistItemId.length, 8))}...'),
      ),
      body: Center(
        child:
            Text('Wishlist Notifications Placeholder for ID: $wishlistItemId'),
      ),
    );
  }
}
