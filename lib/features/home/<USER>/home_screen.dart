// lib/features/home/<USER>/home_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/constants/constants.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:beamer/beamer.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int selectedIndex = 0;

  onItemClicked(int index) {
    // Handle sign out button (index 5)
    if (index == 5) {
      _handleSignOut();
      return;
    }

    setState(() {
      selectedIndex = index;
      // tabController!.index = selectedIndex;
    });
  }

  void _handleSignOut() async {
    try {
      final authRepository = ref.read(authRepositoryProvider);
      await authRepository.signOut();
      if (mounted) {
        context.beamToNamed('/welcome');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Sign out failed: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Constants.bottomNavBarItems[selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.location_searching),
            label: 'Discover',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.event),
            label: 'Events',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.group),
            label: 'Friends',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bookmark_border),
            label: 'Wishlists',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Me',
          ),
          // Temporary sign out button for testing
          BottomNavigationBarItem(
            icon: Icon(Icons.logout),
            label: 'Sign Out',
          ),
        ],
        // unselectedItemColor: kDarkGreyColor,
        selectedItemColor: Color(0xFF00AA80),
        // backgroundColor: kBackgroundColor,
        type: BottomNavigationBarType.fixed,
        // unselectedLabelStyle: const TextStyle(fontSize: 11),
        // selectedLabelStyle: const TextStyle(fontSize: 11),
        selectedFontSize: 11,
        unselectedFontSize: 11,
        showUnselectedLabels: true,
        currentIndex: selectedIndex,
        onTap: onItemClicked,
      ),
    );
  }
}
