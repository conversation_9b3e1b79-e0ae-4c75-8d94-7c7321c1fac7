import 'dart:math';
import 'package:flutter/material.dart';

class ChatScreen extends StatelessWidget {
  final String chatRoomId; // Add this

  const ChatScreen({
    super.key,
    required this.chatRoomId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Chat - ID: ${chatRoomId.substring(0, min(chatRoomId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Chat Screen Placeholder for ChatRoom ID: $chatRoomId'),
      ),
    );
  }
}
