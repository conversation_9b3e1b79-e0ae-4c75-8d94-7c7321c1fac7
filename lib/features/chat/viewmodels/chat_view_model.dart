// Future<void> sendMessage(
//     String senderUserId, String chatRoomId, String messageText) async {
//   final currentUser = _supabaseService.currentUser;
//   if (currentUser == null) return; //Should not happen

//   // 1.  Get other user id
//   final otherUserIdResult =
//       await _chatRepository.getOtherUserIdInChatRoom(chatRoomId, senderUserId);

//   otherUserIdResult.fold((failure) {
//     print(
//         "Failed to get other user ID for notification: $failure"); // same as before.
//   }, (otherUserId) async {
//     if (otherUserId == null) {
//       // Should no happen
//       return;
//     }
//     // 2. Call ChatRepository to send the message
//     final sendResult = await _chatRepository.sendMessage(
//         senderUserId, otherUserId, messageText, chatRoomId);

//     sendResult.fold((failure) {
//       // Handle message sending failure (e.g., show an error message in the UI)
//       // state = AsyncError(failure); // You'd likely have a more specific error state
//     }, (_) async {
//       // Success
//       // Message sent successfully. Now create the notification.
//       final areFriends = await _supabaseService.areUsersFriends(
//           senderUserId, otherUserId); // Get friend
//       final senderUser =
//           await _supabaseService.getUser(senderUserId); // Get sender info
//       if (senderUser != null) {
//         await ref.read(notificationRepositoryProvider).createNotification(
//               // Use await and named parameters
//               userId: otherUserId,
//               notificationType: areFriends
//                   ? 'new_message'
//                   : 'new_message_nonfriend', // new message type.
//               content:
//                   'New message from ${senderUser['full_name'] ?? senderUser['username']}: ${messageText.substring(0, min(messageText.length, 50))}...', // Include a snippet,
//               relatedUserId: senderUserId, // Include sender's ID for navigation
//               chatRoomId: chatRoomId, // Pass the chatRoomId
//             );
//       }
//     });
//   });
// }
