// lib/features/profile/widgets/shared_activities_selector_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart'; // For firstWhereOrNull if needed
import 'package:reorderables/reorderables.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/providers.dart'; // For sharedActivitiesProvider
import 'package:watermelon_draft/core/utils/ui_utils.dart'; // Import for showSharedActivitySelectionSheet
import 'package:watermelon_draft/widgets/activity_search_input_widget.dart'; // Import new reusable widget

class SharedActivitiesSelectorWidget extends ConsumerStatefulWidget {
  final List<String>
      initialActivities; // List of initially selected activity IDs
  final Function(List<String> updatedActivities) onActivitiesChanged;
  // Profile context enforces Min 1, Max 5 by default
  final int maxActivities;
  final int minActivities;

  const SharedActivitiesSelectorWidget({
    super.key,
    required this.initialActivities,
    required this.onActivitiesChanged,
    this.maxActivities = 5,
    this.minActivities = 1, // Minimum required for profile
  });

  @override
  ConsumerState<SharedActivitiesSelectorWidget> createState() =>
      _SharedActivitiesSelectorWidgetState();
}

class _SharedActivitiesSelectorWidgetState
    extends ConsumerState<SharedActivitiesSelectorWidget> {
  // Local state to manage the selection within this widget instance
  late List<String> _selectedActivityIds;
  List<SharedActivity> _allActivities = []; // Cache fetched activities
  bool _isLoadingActivities = true;

  @override
  void initState() {
    super.initState();
    // Initialize local state from the parent's initial values
    _selectedActivityIds = List.from(widget.initialActivities);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _fetchActivities();
      }
    });
  }

  Future<void> _fetchActivities() async {
    if (mounted) {
      setState(() => _isLoadingActivities = true);
    }

    try {
      final activities = await ref.read(sharedActivitiesProvider.future);
      if (!mounted) {
        return;
      }

      setState(() {
        _allActivities = activities;
        // Ensure initial selections are valid based on fetched activities
        _selectedActivityIds = widget.initialActivities
            .where((id) => _allActivities.any((act) => act.activityId == id))
            .toList();
        _isLoadingActivities = false;
      });
    } catch (e, s) {
      print("Error fetching activities in selector: $e \n$s");
      if (!mounted) {
        return;
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
              "Error loading activities: ${e is Failure ? e.message : 'Unknown error'}")));
      setState(() {
        _isLoadingActivities = false;
      });
    }
  }

  // Find activity name helper
  String _getActivityName(String id) {
    return _allActivities
            .firstWhereOrNull((a) => a.activityId == id)
            ?.activityName ??
        'Unknown';
  }

  // --- Activity Management Logic ---

  // Called by ActivitySearchInputWidget when suggestion is tapped
  void _addActivity(SharedActivity activity) {
    if (_selectedActivityIds.length < widget.maxActivities &&
        !_selectedActivityIds.contains(activity.activityId)) {
      if (mounted) {
        setState(() {
          _selectedActivityIds.add(activity.activityId);
        });
        widget.onActivitiesChanged(
            List.from(_selectedActivityIds)); // Notify parent
      }
    } else if (_selectedActivityIds.length >= widget.maxActivities) {
      _showMaxActivitiesError();
    }
    // If already contains, do nothing silently
  }

  // Called by SelectedItemsChipsWidget when 'x' on a chip is tapped
  void _removeActivity(String activityId) {
    // --- Enforce Minimum for Profile Context ---
    if (_selectedActivityIds.length > widget.minActivities) {
      if (mounted) {
        setState(() {
          _selectedActivityIds.remove(activityId);
        });
        widget.onActivitiesChanged(
            List.from(_selectedActivityIds)); // Notify parent
      }
    } else {
      // Show error message if trying to remove below the minimum
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'You must select at least ${widget.minActivities} activity.')),
        );
      }
    }
  }

  void _showMaxActivitiesError() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content:
              Text("Maximum ${widget.maxActivities} activities allowed.")));
    }
  }

  // --- Reorder Handler ---
  void _onReorder(int oldIndex, int newIndex) {
    if (mounted) {
      setState(() {
        if (oldIndex < 0 ||
            oldIndex >= _selectedActivityIds.length ||
            newIndex < 0) {
          return; // Bounds check
        }

        final String item = _selectedActivityIds.removeAt(oldIndex);
        // Adjust index based on ReorderableWrap behavior
        if (newIndex > _selectedActivityIds.length) {
          newIndex = _selectedActivityIds.length; // Clamp
        }

        if (newIndex > oldIndex) {
          newIndex -= 1; // Adjust if moving down
        }
        // Clamp again just in case
        newIndex = newIndex.clamp(0, _selectedActivityIds.length);

        _selectedActivityIds.insert(newIndex, item);
      });
      widget.onActivitiesChanged(List.from(_selectedActivityIds));
    }
  }

  // --- Trigger for Bottom Sheet ---
  Future<void> _openActivityList() async {
    // Call the reusable utility function
    final List<String>? result = await showSharedActivitySelectionSheet(
      context: context,
      ref: ref, // Pass ref
      initialSelectedIds: _selectedActivityIds,
      maxSelection: widget.maxActivities,
      minSelection: widget.minActivities, // Pass minimum
      title: 'Select Activities to Share',
    );

    // Update state if user pressed "Done" and list changed
    if (result != null && mounted) {
      // Check if list content actually changed before updating state/notifying parent
      if (!const DeepCollectionEquality()
          .equals(result, _selectedActivityIds)) {
        setState(() {
          _selectedActivityIds = result;
        });
        widget.onActivitiesChanged(List.from(_selectedActivityIds));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the provider to get the full list names for chip display
    // final allActivitiesAsync = ref.watch(sharedActivitiesProvider);

    if (_isLoadingActivities) {
      return const Center(
          child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select ${widget.minActivities}-${widget.maxActivities} activities you\'d like to share. Drag to reorder priority.',
          style: Theme.of(context)
              .textTheme
              .bodySmall
              ?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 10),

        // --- Display Selected Chips (Using ReorderableWrap directly) ---
        if (_selectedActivityIds.isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text('No activities selected yet.',
                style: TextStyle(color: Colors.grey)),
          )
        else
          ReorderableWrap(
            spacing: 8.0,
            runSpacing: 4.0,
            needsLongPressDraggable: false,
            onReorder: _onReorder, // Pass the handler
            children: _selectedActivityIds.map((activityId) {
              final activityName = _getActivityName(activityId); // Get name
              return Chip(
                key: ValueKey(activityId), // Use stable ID for key
                label: Text(activityName),
                deleteIcon: const Icon(Icons.close, size: 16),
                onDeleted: () => _removeActivity(activityId),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              );
            }).toList(),
          ),
        const SizedBox(height: 16),

        // --- Autocomplete Input ---
        ActivitySearchInputWidget(
          onActivitySelected: _addActivity, // Pass the add helper
          currentSelectionIds:
              _selectedActivityIds, // Pass current IDs to filter suggestions
        ),

        // --- "See All" Button ---
        const SizedBox(height: 8),
        Align(
          // Align button to the right or center
          alignment: Alignment.centerRight,
          child: TextButton(
            onPressed:
                _openActivityList, // Call the helper that uses the util function
            child: const Text('Select from Full List...'),
          ),
        ),
      ],
    );
  }
}
