// lib/features/profile/widgets/interests_selector_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reorderables/reorderables.dart'; // Keep for reordering chips
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/models/keyword.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/widgets/keyword_search_input_widget.dart'; // Import new widget
import 'package:watermelon_draft/widgets/custom_keyword_input_widget.dart'; // Import new widget
import 'package:collection/collection.dart'; // For firstWhereOrNull

class InterestsSelectorWidget extends ConsumerStatefulWidget {
  final List<String> initialKeywords;
  final Function(List<String> updatedKeywords) onKeywordsChanged;
  final int maxTotalKeywords;
  final int maxCustomKeywords;

  const InterestsSelectorWidget({
    super.key,
    required this.initialKeywords,
    required this.onKeywordsChanged,
    this.maxTotalKeywords = 5,
    this.maxCustomKeywords = 2,
  });

  @override
  ConsumerState<InterestsSelectorWidget> createState() =>
      _InterestsSelectorWidgetState();
}

class _InterestsSelectorWidgetState
    extends ConsumerState<InterestsSelectorWidget> {
  // State managed here: the lists of selected IDs/Names
  List<String> _selectedPredefinedKeywords = [];
  List<String> _selectedCustomKeywords = [];
  List<Keyword> _allKeywords = []; // Cache all keywords
  bool _isLoadingKeywords = true;

  @override
  void initState() {
    super.initState();
    // Use post-frame callback for safety when calling async from initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeKeywordsAndSelection(); // Changed method name
      }
    });
  }

  Future<void> _initializeKeywordsAndSelection() async {
    if (!mounted) {
      print(
          "InterestsSelector: _initializeKeywordsAndSelection - NOT MOUNTED at start");
      return;
    }

    setState(() {
      _isLoadingKeywords = true;
    }); // Set loading true

    try {
      print("InterestsSelector: Awaiting keywordsProvider.future...");
      // Await the future directly from the provider
      final keywords = await ref.read(keywordsProvider.future);
      print(
          "InterestsSelector: keywordsProvider.future COMPLETED. Keywords count: ${keywords.length}");

      if (!mounted) {
        print(
            "InterestsSelector: _initializeKeywordsAndSelection - NOT MOUNTED after await");
        return; // Check mounted AFTER await
      }

      // Call helper to update local state
      _updateLocalKeywordsState(keywords);
      print(
          "InterestsSelector: _updateLocalKeywordsState called. _isLoadingKeywords should be false.");
    } catch (error, stackTrace) {
      print(
          "InterestsSelector: ERROR in _initializeKeywordsAndSelection: $error\n$stackTrace"); // Log error
      if (!mounted) {
        return;
      }
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(
            "Error loading interests: ${error is Failure ? error.message : 'Could not load interests'}"),
        backgroundColor: Colors.red,
      ));
      // Still set loading false on error so UI doesn't hang
      setState(() {
        _isLoadingKeywords = false;
      });
    }
  }

  // --- Helper to set state from fetched keywords ---
  void _updateLocalKeywordsState(List<Keyword> allKeywords) {
    print(
        "InterestsSelector: _updateLocalKeywordsState - Setting state with ${allKeywords.length} total keywords.");
        
    // NOTE: Wrap in setState or else UI won't update and loading indicator stays
    setState(() {
      _allKeywords = allKeywords;
      // Separate initial keywords based on the fetched list
      _selectedPredefinedKeywords = widget.initialKeywords
          .where((kw) => _allKeywords
              .any((k) => k.keywordText.toLowerCase() == kw.toLowerCase()))
          .toList();
      _selectedCustomKeywords = widget.initialKeywords
          .where((kw) => !_allKeywords
              .any((k) => k.keywordText.toLowerCase() == kw.toLowerCase()))
          .toList();
      _isLoadingKeywords = false; // Set loading false
    });
  }

  // --- Keyword Management Logic ---

  void _addPredefinedKeyword(Keyword keyword) {
    if (_getTotalSelectedCount() < widget.maxTotalKeywords &&
        !_selectedPredefinedKeywords.contains(keyword.keywordText) &&
        !_selectedCustomKeywords.contains(keyword.keywordText)) {
      // Also check custom
      if (mounted) {
        setState(() {
          _selectedPredefinedKeywords.add(keyword.keywordText);
        });
        _notifyParent();
      }
    } else {
      _showMaxKeywordError(false);
    }
  }

  void _addCustomKeywordResult(String keyword, bool isCustom) {
    if (isCustom) {
      if (_getTotalSelectedCount() < widget.maxTotalKeywords &&
          _selectedCustomKeywords.length < widget.maxCustomKeywords &&
          !_selectedPredefinedKeywords.contains(keyword) && // Check normalized
          !_selectedCustomKeywords.contains(keyword)) {
        if (mounted) {
          setState(() {
            _selectedCustomKeywords.add(keyword);
          });
          _notifyParent();
          // Optional: Add to frequently used table
          _addFrequentKeyword(keyword);
        }
      } else {
        // Error message was likely shown by CustomKeywordInputWidget
        print("Custom keyword add blocked by limits or duplicate check");
      }
    } else {
      // It was a predefined keyword suggested and confirmed
      final predefinedKeyword =
          _allKeywords.firstWhereOrNull((k) => k.keywordText == keyword);
      if (predefinedKeyword != null) {
        _addPredefinedKeyword(predefinedKeyword); // Add it
      }
    }
  }

  void _removeKeyword(String keyword) {
    bool removed = false;
    if (mounted) {
      setState(() {
        if (_selectedPredefinedKeywords.contains(keyword)) {
          _selectedPredefinedKeywords.remove(keyword);
          removed = true;
        } else if (_selectedCustomKeywords.contains(keyword)) {
          _selectedCustomKeywords.remove(keyword);
          removed = true;
        }
      });
      if (removed) _notifyParent();
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    if (mounted) {
      setState(() {
        // Combine, reorder, then split back
        final List<String> combinedList = [
          ..._selectedPredefinedKeywords,
          ..._selectedCustomKeywords
        ];
        if (oldIndex < 0 ||
            oldIndex >= combinedList.length ||
            newIndex < 0 ||
            newIndex > combinedList.length) {
          return; // Bounds check
        }

        final String item = combinedList.removeAt(oldIndex);
        // Sometimes newIndex needs adjustment if item moved down
        if (newIndex > combinedList.length) {
          newIndex = combinedList.length; // Clamp
        }
        // Adjust newIndex if item was moved from before the target index
        if (newIndex > oldIndex) {
          newIndex -= 1; // Adjust if moving down
        }
        // Clamp again just in case
        newIndex = newIndex.clamp(0, combinedList.length);

        combinedList.insert(newIndex, item);

        // Separate back (case-insensitive comparison just in case)
        _selectedPredefinedKeywords = combinedList
            .where((kw) => _allKeywords
                .any((k) => k.keywordText.toLowerCase() == kw.toLowerCase()))
            .toList();
        _selectedCustomKeywords = combinedList
            .where((kw) => !_allKeywords
                .any((k) => k.keywordText.toLowerCase() == kw.toLowerCase()))
            .toList();
      });
      _notifyParent();
    }
  }

  int _getTotalSelectedCount() {
    return _selectedPredefinedKeywords.length + _selectedCustomKeywords.length;
  }

  void _notifyParent() {
    widget.onKeywordsChanged([
      ..._selectedPredefinedKeywords,
      ..._selectedCustomKeywords,
    ]);
  }

  void _showMaxKeywordError(bool isCustomCheck) {
    if (mounted) {
      String message =
          "Maximum ${widget.maxTotalKeywords} total interests allowed.";
      if (isCustomCheck &&
          _selectedCustomKeywords.length >= widget.maxCustomKeywords) {
        message =
            "Maximum ${widget.maxCustomKeywords} custom interests allowed.";
      }
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(message)));
    }
  }

  // Optional: Add to frequently used keywords table
  Future<void> _addFrequentKeyword(String keyword) async {
    try {
      // Could implement a check to only add after X uses or time period
      // await ref.read(keywordsRepositoryProvider).addFrequentlyUsedKeyword(keyword);
      print(
          "INFO: Custom keyword '$keyword' added (frequent use tracking TODO).");
    } catch (e) {
      print("Error adding frequent keyword: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the main provider to react if the underlying list changes
    // final allKeywordsAsync = ref.watch(keywordsProvider);

    // Use the loading flag to show indicator before keywords are ready
    if (_isLoadingKeywords) {
      return const Center(
          child: Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(),
      ));
    }

    // Get combined list for ReorderableWrap children
    final List<String> combinedSelectedKeywords = [
      ..._selectedPredefinedKeywords,
      ..._selectedCustomKeywords
    ];

    // --- Build UI only AFTER keywords are loaded ---
    // Pass the _allKeywords list down to the child input widgets
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select up to ${widget.maxTotalKeywords} interests (max ${widget.maxCustomKeywords} custom). Drag chips to reorder.',
          style: Theme.of(context)
              .textTheme
              .bodySmall
              ?.copyWith(color: Colors.grey[600]),
        ),
        const SizedBox(height: 16),

        // --- Display Selected Chips (Using ReorderableWrap directly) ---
        // If no keywords selected, show the placeholder text
        if (combinedSelectedKeywords.isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text('No interests selected yet.',
                style: TextStyle(color: Colors.grey)),
          )
        else
          ReorderableWrap(
            spacing: 8.0,
            runSpacing: 4.0,
            needsLongPressDraggable: false, // Allow immediate drag
            onReorder: _onReorder, // Use the existing handler
            children: combinedSelectedKeywords.map((keywordText) {
              // Determine if it's predefined or custom for key generation (optional)
              bool isCustom = !_allKeywords.any((k) =>
                  k.keywordText.toLowerCase() == keywordText.toLowerCase());
              return Chip(
                // Use a key that includes its type and text for stability during reorder
                key: ValueKey(
                    '${isCustom ? 'custom' : 'predefined'}_$keywordText'),
                label: Text(keywordText),
                deleteIcon: Icon(Icons.close, size: 16), // Smaller delete icon
                onDeleted: () => _removeKeyword(keywordText), // Unified remove
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              );
            }).toList(),
          ),
        const SizedBox(height: 16),

        // --- Predefined Keywords Input ---
        KeywordSearchInputWidget(
          // Pass the fetched list to the child widget
          allKeywords: _allKeywords,
          onKeywordSelected: (kw) =>
              _addPredefinedKeyword(kw), // Use specific add
          currentSelectionNames: [
            ..._selectedPredefinedKeywords,
            ..._selectedCustomKeywords
          ],
        ),
        const SizedBox(height: 16),

        // --- Custom Keywords Input ---
        CustomKeywordInputWidget(
          allKeywords: _allKeywords,
          currentSelectedKeywords: [
            ..._selectedPredefinedKeywords,
            ..._selectedCustomKeywords
          ],
          onKeywordAdded: _addCustomKeywordResult, // Use specific handler
          maxCustomKeywords: widget.maxCustomKeywords,
          maxTotalKeywords: widget.maxTotalKeywords,
        ),
      ],
    );
  }
}
