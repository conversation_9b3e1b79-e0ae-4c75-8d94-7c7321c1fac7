// lib/features/profile/widgets/user_profile_card.dart
import 'dart:math'; // For min function
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart'; // For firstWhereOrNull

// Define the enum (can be in this file or elsewhere)
enum CardSize { condensed, medium }

class UserProfileCard extends ConsumerStatefulWidget {
  // Stateful for heart icon
  final User user;
  final CardSize size;
  // General onTap for navigation (used by condensed, potentially parts of medium)
  final VoidCallback? onTap;

  const UserProfileCard({
    super.key,
    required this.user,
    required this.size,
    this.onTap,
  });

  @override
  ConsumerState<UserProfileCard> createState() => _UserProfileCardState();
}

class _UserProfileCardState extends ConsumerState<UserProfileCard> {
  bool _isSaved = false;
  bool _isProcessingSave = false;

  @override
  void initState() {
    super.initState();
    // Don't await in initState directly, use a post-frame callback or FutureBuilder/Consumer
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Check mounted after async gap
        _checkInitialSaveStatus();
      }
    });
  }

  Future<void> _checkInitialSaveStatus() async {
    final currentUserId = ref.read(supabaseServiceProvider).currentUser?.id;
    if (currentUserId == null || !mounted) return;

    // Set loading? Maybe not needed for initial check
    // setState(() => _isProcessingSave = true);
    final result = await ref
        .read(userRepositoryProvider)
        .isUserSaved(currentUserId, widget.user.userId);
    // setState(() => _isProcessingSave = false); // Reset loading

    if (!mounted) return; // Check again after await

    result.fold(
      (l) =>
          print("Error checking saved status for ${widget.user.username}: $l"),
      (isSaved) {
        setState(() {
          _isSaved = isSaved;
        });
      },
    );
  }

  Future<void> _toggleSave() async {
    if (_isProcessingSave) return;

    final currentUserId = ref.read(supabaseServiceProvider).currentUser?.id;
    if (currentUserId == null || !mounted) return;

    setState(() => _isProcessingSave = true);

    final repo = ref.read(userRepositoryProvider);
    final result = _isSaved
        ? await repo.unsaveUser(currentUserId, widget.user.userId)
        : await repo.saveUser(currentUserId, widget.user.userId);

    if (!mounted) return; // Check mounted after await

    result.fold(
      (failure) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Error: ${failure.message}'),
            backgroundColor: Colors.red));
      },
      (_) {
        // Success (Unit)
        setState(() {
          _isSaved = !_isSaved; // Toggle local state on success
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(_isSaved ? 'Profile Saved!' : 'Profile Unsaved'),
          duration: Duration(seconds: 1),
        ));
      },
    );
    setState(() => _isProcessingSave = false);
  }

  // --- Helper Build Methods ---
  // Widget _buildAvatar(BuildContext context, double radius) {
  //   // Uses generateAvatar from avatar_utils.dart
  //   if (widget.user.avatarType == 'uploaded' &&
  //       widget.user.profilePictureUrl != null) {
  //     return CircleAvatar(
  //       radius: radius,
  //       backgroundImage:
  //           CachedNetworkImageProvider(widget.user.profilePictureUrl!),
  //       backgroundColor: Colors.grey[200], // Placeholder color
  //     );
  //   } else if (widget.user.avatarType == 'default' &&
  //       widget.user.profilePictureUrl != null) {
  //     return CircleAvatar(
  //       radius: radius,
  //       backgroundImage: AssetImage(widget.user.profilePictureUrl!),
  //       backgroundColor: Colors.grey[200],
  //     );
  //   }  else {
  //     // Generated or fallback
  //     Color? storedColor;
  //     if (widget.user.avatarType == 'generated' &&
  //         widget.user.generatedAvatarColor != null) {
  //       try {
  //         // Parse hex string back to Color
  //         storedColor =
  //             Color(int.parse('0x${widget.user.generatedAvatarColor!}'));
  //       } catch (e) {
  //         print("Error parsing stored avatar color: $e");
  //         // Fallback to default random if parsing fails
  //       }
  //     }
  //     return generateAvatar(
  //       widget.user.fullName ?? widget.user.username,
  //       radius: radius,
  //       color: storedColor, // Pass the STORED color if available
  //     );
  //   }
  // }

  Widget _buildActivityChips(List<SharedActivity> allActivities,
      List<String>? selectedIds, int maxToShow) {
    if (selectedIds == null || selectedIds.isEmpty) {
      return const Text("No shared activities listed.",
          style: TextStyle(fontSize: 10, color: Colors.grey));
    }
    final selectedActivities = selectedIds
        .map((id) {
          // Find activity object by ID
          // Use firstWhereOrNull from collection package
          final activity =
              allActivities.firstWhereOrNull((act) => act.activityId == id);
          // Fallback to showing ID if activity name not found (shouldn't happen ideally)
          return activity?.activityName ??
              '${id.substring(0, min(id.length, 8))}...';
        })
        .take(maxToShow)
        .toList();

    return Wrap(
      spacing: 4.0,
      runSpacing: 4.0, // Add vertical spacing
      children: selectedActivities
          .map((name) => Chip(
                label: Text(name),
                labelStyle: const TextStyle(fontSize: 10),
                padding: const EdgeInsets.symmetric(
                    horizontal: 6, vertical: 0), // Adjust padding
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: const VisualDensity(
                    horizontal: -2, vertical: -2), // Make chip smaller
              ))
          .toList(),
    );
  }

  Widget _buildInterestChips(List<String>? interestKeywords, int maxToShow) {
    if (interestKeywords == null || interestKeywords.isEmpty) {
      return const Text("No interests listed.",
          style: TextStyle(fontSize: 10, color: Colors.grey));
    }
    return Wrap(
      spacing: 4.0,
      runSpacing: 4.0, // Add vertical spacing
      children: interestKeywords
          .take(maxToShow)
          .map((keyword) => Chip(
                label: Text(keyword),
                labelStyle: const TextStyle(fontSize: 10),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity:
                    const VisualDensity(horizontal: -2, vertical: -2),
              ))
          .toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Watch the shared activities provider
    final allActivitiesAsync = ref.watch(sharedActivitiesProvider);

    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 2.0,
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 0),
      child: InkWell(
        onTap: widget.onTap, // Use the general onTap for navigation
        child: switch (widget.size) {
          // Use switch expression for layout
          CardSize.medium =>
            _buildMediumLayout(context, ref, allActivitiesAsync),
          CardSize.condensed =>
            _buildCondensedLayout(context, ref, allActivitiesAsync),
        },
      ),
    );
  }

  // --- Layout Builders ---

  Widget _buildMediumLayout(BuildContext context, WidgetRef ref,
      AsyncValue<List<SharedActivity>> allActivitiesAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // --- 1. Image Carousel Placeholder & Heart Icon ---
        Stack(
          alignment: Alignment.topRight, // Align heart icon to top right
          children: [
            // TODO: Replace Container with actual Image Carousel Widget
            Container(
              height: 250, // Adjust height for medium card
              width: double.infinity, // Take full width
              decoration: BoxDecoration(
                color: Colors.grey[300],
                // Optional: Add border radius if card isn't clipped
                // borderRadius: BorderRadius.vertical(top: Radius.circular(4.0)),
              ),
              child: const Center(
                  child: Icon(Icons.photo_camera,
                      size: 50, color: Colors.grey)), // Placeholder Icon
            ),
            Positioned(
                // Position heart icon
                top: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.4),
                    shape: BoxShape.circle,
                  ),
                  // Add margin if needed
                  child: IconButton(
                    icon: _isProcessingSave
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                                strokeWidth: 2, color: Colors.white))
                        : Icon(
                            _isSaved ? Icons.favorite : Icons.favorite_border,
                            color: _isSaved ? Colors.redAccent : Colors.white,
                          ),
                    iconSize: 24, // Adjust size
                    tooltip: _isSaved ? 'Unsave Profile' : 'Save Profile',
                    onPressed: _isProcessingSave ? null : _toggleSave,
                  ),
                )),
            // TODO: Add indicator dots ("breadcrumbs") below the placeholder
          ],
        ),

        // --- 2. Info Column ---
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.user.fullName ?? 'N/A',
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                // Gender and Age
                children: [
                  if (widget.user.gender != null &&
                      widget.user.gender != 'Prefer not to say') ...[
                    // Optional: Add gender icon?
                    Text(widget.user.gender!,
                        style: Theme.of(context).textTheme.bodyMedium),
                    const Text(' • ',
                        style: TextStyle(color: Colors.grey)), // Separator
                  ],
                  if (widget.user.age != null)
                    Text('${widget.user.age} years old',
                        style: Theme.of(context).textTheme.bodyMedium),
                ],
              ),
              if (widget.user.city != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.location_on_outlined,
                        size: 14, color: Colors.grey[600]),
                    SizedBox(width: 4),
                    Expanded(
                      // Allow city to wrap if needed
                      child: Text(
                        widget.user.city!,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(color: Colors.grey[700]),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 12),
              Text("Activities to Share:",
                  style: Theme.of(context).textTheme.labelLarge),
              const SizedBox(height: 6),
              // Use pattern matching for activities
              switch (allActivitiesAsync) {
                AsyncData(:final value) =>
                  _buildActivityChips(value, widget.user.sharedActivities, 5),
                AsyncError(:final error) => Text('Error loading activities',
                    style: TextStyle(fontSize: 10, color: Colors.red)),
                _ => const SizedBox(
                    height: 20,
                    child: Center(
                        child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                                strokeWidth: 1)))), // Loading placeholder
              },
              const SizedBox(height: 12),
              Text("My Interests:",
                  style: Theme.of(context).textTheme.labelLarge),
              const SizedBox(height: 6),
              _buildInterestChips(widget.user.myInterests, 5),
            ],
          ),
        ),
      ],
    );
  }

Widget _buildCondensedLayout(BuildContext context, WidgetRef ref,
      AsyncValue<List<SharedActivity>> allActivitiesAsync) {
    final theme = Theme.of(context);

    // Calculate image width (e.g., based on typical card height or a fixed value)
    const double estimatedCardHeight =
        100.0; // Estimate height of condensed card
    const double imageWidth =
        estimatedCardHeight * 1.1; // Make image slightly wider than tall

    return Row(
      // No vertical padding needed inside if the parent InkWell has padding
      children: [
        // --- Image Container (Left Side) ---
        SizedBox(
          width: imageWidth,
          height: estimatedCardHeight, // Match height or adjust aspect ratio
          child: ClipRRect(
            // Clip the image to rounded corners to match Card
            // Use the Card's default border radius if possible, or define one
            borderRadius: const BorderRadius.only(
              topLeft:
                  Radius.circular(12.0), // Standard Card radius is often 12
              bottomLeft: Radius.circular(12.0),
            ),
            // Use _buildAvatar but maybe force a larger radius/non-circular background?
            // Or simpler: directly build the image here
            child: (widget.user.avatarType == 'uploaded' &&
                    widget.user.profilePictureUrl != null)
                ? CachedNetworkImage(
                    imageUrl: widget.user.profilePictureUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        Container(color: Colors.grey[200]),
                    errorWidget: (context, url, error) => Container(
                        color: Colors.grey[300],
                        child: Icon(Icons.error_outline)),
                  )
                : (widget.user.avatarType == 'default' &&
                        widget.user.profilePictureUrl != null)
                    ? Image.asset(
                        widget.user.profilePictureUrl!,
                        fit: BoxFit.cover,
                      )
                    : generateAvatar(
                        // Generate avatar fills the SizedBox
                        widget.user.fullName ?? widget.user.username ?? '?',
                        radius:
                            imageWidth / 2, // Adjust radius based on container
                        // Pass the stored color for consistency
                        color: (widget.user.generatedAvatarColor != null)
                            ? Color(int.parse(
                                '0x${widget.user.generatedAvatarColor!}'))
                            : null, // Use stored color or let generateAvatar pick random
                      ),
          ),
        ),
        const SizedBox(width: 10), // Spacing

        // --- Info Column (Right Side) ---
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment:
                MainAxisAlignment.center, // Center text vertically
            children: [
              Text(
                widget.user.fullName ?? widget.user.username ?? '?',
                style: theme.textTheme.titleSmall
                    ?.copyWith(fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Row(
                // Gender and Age
                children: [
                  if (widget.user.gender != null &&
                      widget.user.gender != 'Prefer not to say') ...[
                    Text(widget.user.gender!,
                        style: theme.textTheme.bodySmall
                            ?.copyWith(fontSize: 11)), // Smaller
                    const Text(', ', style: TextStyle(fontSize: 11)),
                  ],
                  if (widget.user.age != null)
                    Text('${widget.user.age}',
                        style: theme.textTheme.bodySmall
                            ?.copyWith(fontSize: 11)), // Smaller
                ],
              ),
              if (widget.user.city != null) ...[
                // City
                const SizedBox(height: 2),
                Text(
                  widget.user.city!,
                  style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600], fontSize: 11), // Smaller
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 4),
              // Show first 1 or 2 activity chips (space is limited)
              switch (allActivitiesAsync) {
                AsyncData(:final value) => _buildActivityChips(
                    value, widget.user.sharedActivities, 2), // Limit to 2
                AsyncError() => const SizedBox(height: 18), // Placeholder space
                _ => const SizedBox(height: 18), // Placeholder space
              }
            ],
          ),
        ),
        const SizedBox(width: 4), // Padding before potential (removed) chevron
      ],
    );
  }
}
