import 'dart:math';
import 'package:flutter/material.dart';

class ProfileScreen extends StatelessWidget {
  final String userId; // Add this

  const ProfileScreen({
    super.key,
    required this.userId, // Add this
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // Display the user ID temporarily for verification
        title: Text(
            'Profile - User: ${userId.substring(0, min(userId.length, 8))}...'),
      ),
      body: Center(
        child: Text('Profile Screen Placeholder for User ID: $userId'),
      ),
    );
  }
}
