// lib/features/profile/providers/profile_providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/providers.dart';

part 'profile_providers.g.dart';

// Provider to fetch and cache the currently logged-in user's FULL profile
@Riverpod(keepAlive: true) // keepAlive ensures the data persists
Future<User?> currentUserProfile(Ref ref) async {
  // Watch dependencies - if they change, this provider might rerun (e.g., on logout/login)
  final authRepository = ref.watch(authRepositoryProvider);
  final userRepository = ref.watch(userRepositoryProvider);

  // Get the current Supabase Auth user
  final authUserEither = await authRepository.currentUser();

  return await authUserEither.fold(
    (failure) {
      // Not authenticated or error getting auth user
      print("currentUserProfileProvider: Auth error - $failure");
      return null; // Return null if not authenticated
    },
    (authUser) async {
      if (authUser == null) {
        print("currentUserProfileProvider: No authenticated user.");
        return null; // Return null if not authenticated
      }

      // Authenticated, now fetch the profile from your Users table
      final profileEither = await userRepository.getUser(authUser.id);

      return profileEither.fold(
        (failure) {
          // Failed to get profile from DB (this might be serious)
          print("currentUserProfileProvider: Profile fetch error - $failure");
          // Decide how to handle - return null or throw? Throwing puts provider in error state.
          throw failure; // Throw to put provider in AsyncError state
        },
        (userProfile) {
          // Successfully fetched profile
          print("currentUserProfileProvider: Profile loaded successfully.");
          return userProfile; // Return the User object (can be null if getUser allows it)
        },
      );
    },
  );
}
