// lib/features/auth/screens/reset_password_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/auth/viewmodels/reset_password_viewmodel.dart';
import 'package:watermelon_draft/core/errors.dart';

class ResetPasswordScreen extends ConsumerStatefulWidget {
  // Use ConsumerStatefulWidget
  const ResetPasswordScreen({super.key});

  @override
  ConsumerState<ResetPasswordScreen> createState() =>
      _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends ConsumerState<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(resetPasswordViewModelProvider, (previous, next) {
      if (next is AsyncError) {
        // If error
        final error = next.error;
        if (error is Failure && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Error on reset password ${error.message}"),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    });
    final state = ref.watch(
        resetPasswordViewModelProvider); // Use ref.watch to get the loading state.
    final viewModel =
        ref.read(resetPasswordViewModelProvider.notifier); // Use .notifier

    return Scaffold(
      appBar: AppBar(title: const Text('Reset Password')),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                    labelText: 'Email',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder()),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: state.isLoading
                    ? null // Disable button while loading
                    : () async {
                        if (_formKey.currentState!.validate()) {
                          await viewModel.resetPassword(
                              email: _emailController.text);
                        }
                      },
                child: state.isLoading
                    ? const CircularProgressIndicator() // Show loading indicator
                    : const Text('Reset Password'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
