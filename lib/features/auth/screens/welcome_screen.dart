// lib/features/auth/screens/welcome_screen.dart
import 'package:flutter/material.dart';
import 'package:beamer/beamer.dart'; // Import Beamer

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome'),
      ),
      body: Center(
        child: Column(
          // Add a Column for multiple buttons
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Welcome Screen Placeholder'),
            Sized<PERSON><PERSON>(height: 20), // Add some spacing
            ElevatedButton(
              onPressed: () {
                context.beamToNamed('/login'); // Navigate to LoginScreen
              },
              child: Text('Login'),
            ),
            Sized<PERSON>ox(height: 10),
            ElevatedButton(
              onPressed: () {
                context.beamToNamed('/signup'); // Navigate to SignupScreen
              },
              child: Text('Sign Up'),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 10),
            ElevatedButton(
              onPressed: () {
                context.beamToNamed(
                    '/reset-password'); // Navigate to Reset Password Screen
              },
              child: Text('Reset Password'),
            ),
            SizedBox(height: 20),

            // --- TEMPORARY BUTTON FOR ONBOARDING ---
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange), // Different color
              onPressed: () {
                context
                    .beamToReplacementNamed('/home'); // Navigate to onboarding route
              },
              child: Text('TEMP: Go to Onboarding'),
            ),
            // --- END TEMPORARY BUTTON ---
          ],
        ),
      ),
    );
  }
}
