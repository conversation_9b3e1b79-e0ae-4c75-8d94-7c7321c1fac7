// lib/features/auth/screens/login_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:watermelon_draft/features/auth/viewmodels/login_viewmodel.dart'; // Import
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/errors.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true; // For hiding/showing password

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // ref.listen(loginViewModelProvider, (previous, next) {
    //   if (next is AsyncData) {
    //     //If success
    //     if (mounted) {
    //       context.beamToNamed("/onboarding"); // Go to home page
    //     }
    //   } else if (next is AsyncError) {
    //     // If error
    //     final error = next.error;
    //     if (error is Failure && mounted) {
    //       ScaffoldMessenger.of(context).showSnackBar(
    //         SnackBar(
    //           content: Text("Error on sign up ${error.message}"),
    //           backgroundColor: Colors.red,
    //         ),
    //       ); // Show error message
    //     }
    //   }
    // });

    ref.listen(loginViewModelProvider, (previous, next) {
      next.whenOrNull(data: (user) {
        // Check if user is not null and widget is still mounted
        if (user != null && mounted) {
          print("Login Success: User ${user.id} authenticated");
          print(
              "Navigating to /home - AuthWrapper will handle onboarding check");
          // Navigate to home - AuthWrapper will check onboarding status from database
          context.beamToReplacementNamed('/home');
        }
      }, error: (error, stack) {
        if (error is Failure && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Error on sign up ${error.message}"),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    });

    final state =
        ref.watch(loginViewModelProvider); // Use to show loading indicator
    final viewModel = ref.read(loginViewModelProvider.notifier);

    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    // Basic email validation
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                    labelText: 'Password',
                    prefixIcon: Icon(Icons.lock),
                    border: OutlineInputBorder(),
                    suffixIcon: IconButton(
                        icon: Icon(_obscurePassword
                            ? Icons.visibility
                            : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        })),
                obscureText: _obscurePassword, // Use a variable for obscureText
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your password';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: state.isLoading
                    ? null
                    : () async {
                        // Disable button while loading
                        if (_formKey.currentState!.validate()) {
                          // Validate the form
                          await viewModel.signIn(
                            email: _emailController.text,
                            password: _passwordController.text,
                          );
                        }
                      },
                child: state.isLoading
                    ? CircularProgressIndicator()
                    : Text('Login'), // Show loading indicator
              ),
              TextButton(
                onPressed: () {
                  context.beamToNamed('/signup'); // Navigate to signup page
                },
                child: const Text("Don't have an account? Sign up"),
              )
            ],
          ),
        ),
      ),
    );
  }
}
