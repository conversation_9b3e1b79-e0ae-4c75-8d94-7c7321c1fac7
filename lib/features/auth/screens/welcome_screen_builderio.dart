import 'package:flutter/material.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 480),
          width: double.infinity,
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 198),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Welcome to text
              RichText(
                textAlign: TextAlign.center,
                text: const TextSpan(
                  style: TextStyle(
                    color: Color(0xFF191919),
                    fontSize: 20,
                    height: 28 / 20,
                    fontFamily: 'Oxygen',
                  ),
                  children: [
                    TextSpan(
                      text: 'Welcome\n',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 40,
                        color: Color(0xFF191919),
                      ),
                    ),
                    TextSpan(
                      text: 'to',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                        height: 36 / 24,
                        color: Color(0xFF191919),
                      ),
                    ),
                  ],
                ),
              ),

              // Logo image
              const SizedBox(height: 17),
              Image.network(
                'https://cdn.builder.io/api/v1/image/assets/d1b4083304574eaf95b54c6d62268e5f/ce73d6fb66fe543a73766d84264fa92a22548208?placeholderIfAbsent=true',
                width: 253,
                fit: BoxFit.contain,
              ),

              // First text line with Oxygen font
              const SizedBox(height: 32),
              const Text(
                'Discover and meet new people.',
                style: TextStyle(
                  color: Color(0xFF191919),
                  fontSize: 18,
                  fontFamily: 'Oxygen',
                  height: 2,
                ),
                textAlign: TextAlign.center,
              ),

              // Second text line with Manrope font
              const SizedBox(height: 8),
              const Text(
                'Discover and meet new people.',
                style: TextStyle(
                  color: Color(0xFF191919),
                  fontSize: 18,
                  fontFamily: 'Manrope',
                  height: 2,
                ),
                textAlign: TextAlign.center,
              ),

              // Third text line with Inter font
              const SizedBox(height: 8),
              const Text(
                'Discover and meet new people.',
                style: TextStyle(
                  color: Color(0xFF191919),
                  fontSize: 18,
                  fontFamily: 'Inter',
                  height: 2,
                ),
                textAlign: TextAlign.center,
              ),

              // Sign up button
              const SizedBox(height: 36),
              _buildButton(
                text: 'Sign up',
                iconUrl:
                    'https://cdn.builder.io/api/v1/image/assets/d1b4083304574eaf95b54c6d62268e5f/83f461a6e3f4770164ed7753e90494a199d5b64c?placeholderIfAbsent=true',
                iconWidth: 20,
              ),

              // Log in button
              const SizedBox(height: 24),
              _buildButton(
                text: 'Log in',
                iconUrl:
                    'https://cdn.builder.io/api/v1/image/assets/d1b4083304574eaf95b54c6d62268e5f/6ea631dcadf6d776ca93e34fc3b5980464d7bed1?placeholderIfAbsent=true',
                iconWidth: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required String iconUrl,
    required double iconWidth,
  }) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: const Color(0xFF009A6E),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network(
            iconUrl,
            width: iconWidth,
            height: iconWidth,
            fit: BoxFit.contain,
          ),
          const SizedBox(width: 14),
          Text(
            text,
            style: const TextStyle(
              color: Color(0xFF009A6E),
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'Oxygen',
            ),
          ),
        ],
      ),
    );
  }
}
