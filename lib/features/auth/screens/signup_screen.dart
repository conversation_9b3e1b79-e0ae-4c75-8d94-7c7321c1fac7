// lib/features/auth/screens/signup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/auth/viewmodels/signup_viewmodel.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/errors.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use ref.listen for side effects (navigation, errors)
    ref.listen(signUpViewModelProvider, (previous, next) {
      next.whenOrNull(// Use whenOrNull as we only care about data/error
          data: (user) {
        if (user != null && mounted) {
          // Navigate to Onboarding on SUCCESSFUL signup AND user object received
          // We need to pass user id here.
          context.beamToNamed('/onboarding');
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content:
                Text("Signup successful! Please check your email to verify."),
            backgroundColor: Colors.green,
          ));
        }
      }, error: (error, stack) {
        if (error is Failure && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Error on sign up: ${error.message}"),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    });

    final state = ref.watch(signUpViewModelProvider);
    final viewModel = ref.read(signUpViewModelProvider.notifier);

    return Scaffold(
      appBar: AppBar(title: const Text('Sign Up')),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'Password',
                  prefixIcon: Icon(Icons.lock),
                  border: OutlineInputBorder(),
                  suffixIcon: IconButton(
                    // Show/hide password
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                obscureText: _obscurePassword,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters long';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: state.isLoading
                    ? null
                    : () async {
                        if (_formKey.currentState!.validate()) {
                          await viewModel.signUp(
                            email: _emailController.text,
                            password: _passwordController.text,
                          );
                        }
                      },
                child: state.isLoading
                    ? const SizedBox(
                        // Use SizedBox for consistent button size
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(
                            strokeWidth: 2, color: Colors.white),
                      )
                    : const Text('Sign Up'),
              ),
              TextButton(
                onPressed: () {
                  context.beamToNamed('/login');
                },
                child: const Text('Already have an account? Log in'),
              )
            ],
          ),
        ),
      ),
    );
  }
}
