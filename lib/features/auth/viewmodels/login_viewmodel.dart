// lib/features/auth/viewmodels/login_viewmodel.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/services/auth_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'login_viewmodel.g.dart';

@riverpod
class LoginViewModel extends _$LoginViewModel {
  late final AuthRepository _authRepository; // Use late final

  @override
  FutureOr<supabase.User?> build() {
    _authRepository = ref.watch(
        authRepositoryProvider); // Get the AuthRepository instance using ref.read
    return null; // Initial state: null (no user logged in)
  }

  Future<Either<Failure, supabase.User?>> signIn({
    required String email,
    required String password,
  }) async {
    final result =
        await _authRepository.signIn(email: email, password: password);
    return result.fold(
      (failure) {
        state = AsyncValue.error(failure, StackTrace.current);
        return left(failure); // Return error
      },
      (user) {
        state = AsyncValue.data(user); // Set data to AsyncData
        return right(user); //Return user
      },
    );
  }
}
