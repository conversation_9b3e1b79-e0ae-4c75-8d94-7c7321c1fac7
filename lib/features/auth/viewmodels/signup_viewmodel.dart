// lib/features/auth/viewmodels/signup_viewmodel.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/services/auth_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'signup_viewmodel.g.dart';

@riverpod
class SignUpViewModel extends _$SignUpViewModel {
  late final AuthRepository _authRepository;

  @override
  FutureOr<supabase.User?> build() {
    _authRepository = ref.watch(authRepositoryProvider); // Use watch
    return null; // Initial state: null
  }

  // --- UPDATED: Removed username and fullName parameters ---
  Future<Either<Failure, supabase.User?>> signUp({
    required String email,
    required String password,
  }) async {
    state = AsyncLoading(); // Set loading state
    final result = await _authRepository.signUp(
      email: email,
      password: password,
      // Removed username and fullName from the call
    );

    return result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current); // Set error state
        return left(failure);
      },
      (user) {
        state = AsyncData(user); // Set data state
        return right(user);
      },
    );
  }
}
