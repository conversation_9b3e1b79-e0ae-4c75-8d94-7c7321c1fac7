// lib/features/auth/viewmodels/reset_password_viewmodel.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/services/auth_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reset_password_viewmodel.g.dart';

@riverpod
class ResetPasswordViewModel extends _$ResetPasswordViewModel {
  late final AuthRepository _authRepository;

  @override
  FutureOr<void> build() {
    //Initial state is empty, since we only call reset password
    _authRepository = ref.read(authRepositoryProvider);
    return null; // Return null
  }

  Future<Either<Failure, Unit>> resetPassword({required String email}) async {
    final result = await _authRepository.resetPassword(email: email);

    return result.fold((failure) {
      state = AsyncValue.error(failure, StackTrace.current);
      return left(failure);
    }, (_) {
      state = AsyncValue.data(state.value);
      return right(unit);
    });
  }
}
