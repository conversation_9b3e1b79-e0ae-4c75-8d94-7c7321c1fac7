// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginViewModelHash() => r'4aa54b3a15343fe75e4c596c24c925cf89ffe57d';

/// See also [LoginViewModel].
@ProviderFor(LoginViewModel)
final loginViewModelProvider =
    AutoDisposeAsyncNotifierProvider<LoginViewModel, supabase.User?>.internal(
  LoginViewModel.new,
  name: r'loginViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginViewModel = AutoDisposeAsyncNotifier<supabase.User?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
