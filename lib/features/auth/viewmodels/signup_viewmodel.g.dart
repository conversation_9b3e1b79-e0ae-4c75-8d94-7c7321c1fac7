// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signup_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signUpViewModelHash() => r'53e3fe13102ba78ed8ad7955a2858a0488767011';

/// See also [SignUpViewModel].
@ProviderFor(SignUpViewModel)
final signUpViewModelProvider =
    AutoDisposeAsyncNotifierProvider<SignUpViewModel, supabase.User?>.internal(
  SignUpViewModel.new,
  name: r'signUpViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signUpViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignUpViewModel = AutoDisposeAsyncNotifier<supabase.User?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
