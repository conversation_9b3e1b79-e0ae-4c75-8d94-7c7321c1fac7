// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_password_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$resetPasswordViewModelHash() =>
    r'32b7e56dd1d1e06cac91ef8217d19be8772ae783';

/// See also [ResetPasswordViewModel].
@ProviderFor(ResetPasswordViewModel)
final resetPasswordViewModelProvider =
    AutoDisposeAsyncNotifierProvider<ResetPasswordViewModel, void>.internal(
  ResetPasswordViewModel.new,
  name: r'resetPasswordViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$resetPasswordViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ResetPasswordViewModel = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
