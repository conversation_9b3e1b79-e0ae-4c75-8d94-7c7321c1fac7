// lib/features/search/screens/city_search_page.dart
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart'; // For SearchInfo, GeoPoint
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/constants/constants.dart';
import 'package:watermelon_draft/core/providers.dart'; // For LocationService, SharedPreferences

// Constants for Distance Slider (Miles)
const double _minDistanceMiles = 1.0;
const double _maxDistanceMiles = 20.0;
const double _defaultDistanceMiles = 5.0;
const int _maxRecentSearches = 3; // Max recent searches to store/show
const String _prefsRecentCitiesKey = 'recent_search_cities';

class CitySearchPage extends ConsumerStatefulWidget {
  // Accept initial center/radius from DiscoverDashboard
  final GeoPoint? initialGeoPoint;
  final String? initialCityName;
  final double? initialRadiusMiles;

  const CitySearchPage({
    super.key,
    this.initialGeoPoint,
    this.initialCityName,
    this.initialRadiusMiles,
  });

  @override
  ConsumerState<CitySearchPage> createState() => _CitySearchPageState();
}

class _CitySearchPageState extends ConsumerState<CitySearchPage> {
  final TextEditingController _cityController = TextEditingController();
  List<SearchInfo> _suggestions = [];
  List<String> _recentSearches = [];
  GeoPoint? _selectedLocation; // Location of the selected/entered city
  String? _selectedCityName; // Name of the selected/entered city
  double _selectedRadiusMiles = Constants.defaultSearchRadiusMiles;
  bool _isLoadingSuggestions = false;
  bool _isLoadingCoords = false; // For loading coords for recent city
  bool _hasChanges = false; // Track unsaved changes
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    // Initialize with passed-in data
    _selectedLocation = widget.initialGeoPoint;
    _selectedCityName = widget.initialCityName;
    _cityController.text = widget.initialCityName ?? ''; // Set initial text
    // Always start with default radius? Or pass initial? Let's use initial if provided.
    _selectedRadiusMiles =
        widget.initialRadiusMiles ?? Constants.defaultSearchRadiusMiles;
    _loadRecentSearches(); // Load recent searches
    // Add listener for text changes
    // _cityController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    // _cityController.removeListener(_onSearchChanged);
    _cityController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _markChanged() {
    if (!_hasChanges) {
      setState(() => _hasChanges = true);
    }
  }

  // --- Recent Searches ---
  Future<void> _loadRecentSearches() async {
    try {
      final prefs =
          await ref.read(sharedPreferencesProvider.future); // Wait for prefs
      final savedList = prefs.getStringList(_prefsRecentCitiesKey) ?? [];
      if (mounted) {
        setState(() {
          _recentSearches = savedList;
        });
      }
    } catch (e) {
      print("Error loading recent searches: $e");
      // Ignore error, proceed with empty list
    }
  }

  Future<void> _saveRecentSearch(String cityName) async {
    if (cityName.trim().isEmpty) {
      return;
    }

    try {
      final prefs = await ref.read(sharedPreferencesProvider.future);
      // Make copy, remove existing instance if present, add to front, limit size
      List<String> updatedList = List.from(_recentSearches);
      updatedList
          .removeWhere((item) => item.toLowerCase() == cityName.toLowerCase());
      updatedList.insert(0, cityName);
      if (updatedList.length > _maxRecentSearches) {
        updatedList = updatedList.sublist(0, _maxRecentSearches);
      }
      await prefs.setStringList(_prefsRecentCitiesKey, updatedList);
      if (mounted) {
        setState(() {
          _recentSearches = updatedList; // Update local state
        });
      }
    } catch (e) {
      print("Error saving recent search: $e");
    }
  }

  // --- Suggestions ---
  void _handleInputChanged(String value) {
    // Call fetch suggestions when text changes (debounced)
    _fetchSuggestions(value);
    _markChanged();
  }

  void _fetchSuggestions(String query) {
    _debounce?.cancel();
    if (query.length < 2) {
      if (mounted) setState(() => _suggestions = []);
      return;
    }
    if (mounted) setState(() => _isLoadingSuggestions = true);

    _debounce = Timer(const Duration(milliseconds: 400), () async {
      if (!mounted) {
        return;
      }

      final result =
          await ref.read(locationServiceProvider).getCitySuggestions(query);
      // Check if query hasn't changed again before updating
      if (mounted && query == _cityController.text) {
        result.fold((l) {
          print("Suggestion Error: $l");
          setState(() {
            _suggestions = [];
            _isLoadingSuggestions = false;
          });
        },
            (r) => setState(() {
                  _suggestions = r;
                  _isLoadingSuggestions = false;
                }));
      } else if (mounted) {
        // Query changed during debounce, reset loading state if it was set
        setState(() => _isLoadingSuggestions = false);
      }
    });
  }

  // --- Selection ---
  // When a suggestion or recent search is tapped
  void _selectCity(String cityName, GeoPoint? location) {
    if (!mounted) {
      return;
    }

    setState(() {
      _cityController.text = cityName;
      _selectedCityName = cityName;
      _selectedLocation = location;
      _suggestions = []; // Clear suggestions
      _hasChanges = true;
      FocusScope.of(context).unfocus(); // Hide keyboard
    });
    if (location != null) {
      _saveRecentSearch(cityName);
    } else {
      // If location is null (e.g., couldn't geocode recent), prompt user?
      print("WARN: Selected city '$cityName' but location is null.");
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Could not determine exact location for '$cityName'")));
    }
  }

  // Handles tapping an autocomplete suggestion
  void _handleSuggestionTap(SearchInfo suggestion) {
    String? city = suggestion.address?.city ?? suggestion.address?.name;
    if (city != null) {
      _selectCity(city, suggestion.point);
    }
  }

  // Handles tapping a recent search item
  Future<void> _handleRecentTap(String cityName) async {
    if (!mounted) {
      return;
    }

    setState(() => _isLoadingCoords = true); // Show loading for coords fetch
    final locationResult = await ref
        .read(locationServiceProvider)
        .getLocationAndCountryFromCity(cityName);

    if (!mounted) {
      return;
    }

    setState(() => _isLoadingCoords = false);

    locationResult.fold(
        (l) => ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Could not find location for $cityName"))),
        (r) {
      _selectCity(cityName, r.$1); // Pass GeoPoint (r.$1)
    });
  }

  // --- Actions ---
  void _resetSearch() {
    setState(() {
      _cityController.clear();
      _suggestions = [];
      // Reset to initial values passed in
      _selectedLocation = widget.initialGeoPoint; // widget.initialGeoPoint;
      _selectedCityName = widget.initialCityName; // widget.initialCityName;
      _selectedRadiusMiles = Constants.defaultSearchRadiusMiles;
      _hasChanges = false; // Reset changes flag after resetting
    });
    FocusScope.of(context).unfocus();
  }

  void _applySearch() {
    // Validate: Ensure a city/location has been selected
    if (_selectedLocation == null ||
        _selectedCityName == null ||
        _selectedCityName!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text(
                'Please select a valid city from suggestions or recent searches.')),
      );
      return;
    }

    // Convert miles to meters for returning
    final radiusMeters = (_selectedRadiusMiles * Constants.metersPerMile).round();

    final resultMap = {
      'geoPoint': _selectedLocation,
      'radiusMeters': radiusMeters,
      'cityName': _selectedCityName, // Optionally return city name
    };
    Navigator.pop(context, resultMap);
  }

  // Unsaved Changes Check
  // --- Dialog for Discarding Changes ---
  Future<bool> _showDiscardDialog() async {
    if (!_hasChanges) {
      return true; // Allow exit if no changes
    }
    // Capture context before await
    final currentContext = context;
    // Check mounted BEFORE showing dialog (good practice)
    if (!currentContext.mounted) {
      return false;
    }

    final shouldDiscard = await showDialog<bool>(
      context: currentContext, // Use captured context
      builder: (dialogContext) => AlertDialog(
        // Use different context name for builder
        title: const Text('Discard Changes?'),
        content: const Text(
            'Your search criteria haven\'t been applied. Discard changes?'),
        actions: [
          TextButton(
            onPressed: () =>
                Navigator.of(dialogContext).pop(false), // Don't discard
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true), // Discard
            child: const Text('Discard'),
          ),
        ],
      ),
    );
    return shouldDiscard ?? false; // Return true only if user confirmed discard
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasChanges,
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        // This is called AFTER a pop attempt
        if (didPop) {
          // Pop was successful (either no changes, or user confirmed discard via AppBar button)
          return;
        }
        final currentContext = context;
        final bool shouldDiscard = await _showDiscardDialog();
        if (shouldDiscard && currentContext.mounted) {
          Navigator.pop(currentContext);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Search Location'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () async {
              // Close button logic
              if (!_hasChanges) {
                Navigator.pop(context); // No changes, just pop
              } else {
                // Capture context BEFORE await
                final currentContext = context;
                // Has changes, show dialog first
                final shouldDiscard = await _showDiscardDialog();
                // Check context mounted AFTER await
                if (shouldDiscard && currentContext.mounted) {
                  Navigator.pop(currentContext); // Use captured context
                }
              }
            },
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // --- City Input ---
                    TextFormField(
                      controller: _cityController,
                      decoration: InputDecoration(
                        labelText: 'City Name',
                        hintText: 'Enter city...',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.search),
                        suffixIcon: _cityController.text.isNotEmpty
                            ? IconButton(
                                icon: Icon(Icons.clear),
                                onPressed: () {
                                  _cityController.clear();
                                  setState(() => _suggestions = []);
                                  _markChanged();
                                },
                              )
                            : null,
                      ),
                      onChanged: _handleInputChanged,
                      textInputAction: TextInputAction.search,
                      onFieldSubmitted: (_) => FocusScope.of(context)
                          .unfocus(), // Dismiss keyboard on submit
                    ),
                    const SizedBox(height: 4),

                    // --- Suggestions / Loading ---
                    if (_isLoadingSuggestions)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      )
                    else if (_suggestions.isNotEmpty)
                      ConstrainedBox(
                        constraints:
                            BoxConstraints(maxHeight: 150), // Limit height
                        child: Card(
                          // Put suggestions in a card
                          elevation: 2,
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: _suggestions.length,
                            itemBuilder: (context, index) {
                              final suggestion = _suggestions[index];
                              return ListTile(
                                dense: true, // Make tiles smaller
                                title: Text(suggestion.address?.toString() ??
                                    'Unknown location'),
                                onTap: () {
                                  _handleSuggestionTap(suggestion);
                                  // setState(() => _suggestions = []);
                                  // _markChanged();
                                },
                              );
                            },
                          ),
                        ),
                      ),

                    // --- Recent Searches ---
                    if (_suggestions.isEmpty &&
                        !_isLoadingSuggestions &&
                        _recentSearches.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Text("Recent Searches",
                          style: Theme.of(context).textTheme.titleSmall),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics:
                            NeverScrollableScrollPhysics(), // Disable scroll for this list
                        itemCount: _recentSearches.length,
                        itemBuilder: (context, index) {
                          final city = _recentSearches[index];
                          return ListTile(
                            dense: true,
                            leading: Icon(Icons.history, size: 18),
                            title: Text(city),
                            onTap: () {
                              _handleRecentTap(city);
                              // setState(() => _suggestions = []);
                              // _markChanged();
                            },
                          );
                        },
                      ),
                    ],
                    const SizedBox(height: 36),

                    // --- Distance Slider ---
                    // const Divider(),
                    Text(
                      'Search Radius: ${_selectedRadiusMiles.round()} Miles',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _selectedRadiusMiles,
                      min: _minDistanceMiles,
                      max: _maxDistanceMiles,
                      divisions:
                          (_maxDistanceMiles - _minDistanceMiles).round(),
                      label: '${_selectedRadiusMiles.round()} mi',
                      onChanged: (double value) {
                        setState(() {
                          _selectedRadiusMiles = value;
                        });
                        _markChanged();
                      },
                    ),
                  ],
                ),
              ),
            ),

            // --- Action Buttons ---
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SafeArea(
                child: Row(
                  children: [
                    OutlinedButton(
                        onPressed: _resetSearch, child: Text('Reset')),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        // Disable if no valid city/location is selected
                        onPressed: (_selectedLocation != null &&
                                _selectedCityName != null &&
                                _selectedCityName!.isNotEmpty)
                            ? _applySearch
                            : null,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child:
                            _isLoadingCoords // Show loading indicator when fetching coords for recent search
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2, color: Colors.white))
                                : const Text('Show Results'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
