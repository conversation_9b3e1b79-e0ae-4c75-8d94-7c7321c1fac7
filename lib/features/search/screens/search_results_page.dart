// lib/features/search/screens/search_results_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/features/profile/widgets/user_profile_card.dart'; // Import card
import 'package:watermelon_draft/features/search/models/search_criteria.dart'; // Import criteria model
import 'package:watermelon_draft/features/search/viewmodels/search_viewmodel.dart'; // Import Search VM Provider
import 'package:watermelon_draft/features/search/viewmodels/search_results_viewmodel.dart'; // Import Results VM Provider
import 'package:beamer/beamer.dart'; // For navigation

class SearchResultsPage extends ConsumerWidget {
  const SearchResultsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 1. Get the current search criteria from the SearchViewModel state
    // We watch the whole state here to rebuild if criteria change, though less likely on this screen
    final searchState = ref.watch(searchViewModelProvider).valueOrNull;

    // Create the criteria object - Handle null state safely
    final SearchCriteria currentCriteria = SearchCriteria(
      activities: searchState?.selectedActivities,
      keywords: searchState?.selectedKeywords,
      distanceFilter: searchState?.selectedDistanceFilter,
      minAge: searchState?.appliedMinAge,
      maxAge: searchState?.appliedMaxAge,
      gender: searchState?.appliedGender,
    );

    // 2. Watch the results provider, passing the current criteria
    final resultsAsync =
        ref.watch(searchResultsViewModelProvider(currentCriteria));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Matching Users'),
        // Back button is added automatically by Navigator/Beamer
      ),
      body: RefreshIndicator(
        // Add pull-to-refresh
        onRefresh: () async {
          // Invalidate the provider to force a refetch with the SAME criteria
          ref.invalidate(searchResultsViewModelProvider(currentCriteria));
          // Wait for the provider to rebuild (optional, improves UX)
          await ref
              .read(searchResultsViewModelProvider(currentCriteria).future);
        },
        child: switch (resultsAsync) {
          AsyncLoading() => const Center(child: CircularProgressIndicator()),
          AsyncError(:final error) => Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading results: ${error is Failure ? error.message : error.toString()}\nPull down to retry.',
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          AsyncData(:final value) => value
                  .isEmpty // Check if the user list is empty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'No users found matching your criteria.\nTry adjusting your search or filters.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                )
              : ListView.builder(
                  // Display results
                  padding: const EdgeInsets.all(8.0), // Padding around the list
                  itemCount: value.length, // value is List<User>
                  itemBuilder: (context, index) {
                    final user = value[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: UserProfileCard(
                        user: user,
                        size: CardSize.medium, // Use medium card
                        onTap: () {
                          // Navigate to profile on tap
                          context.beamToNamed('/profile/${user.userId}');
                        },
                      ),
                    );
                  },
                  // TODO: Add pagination logic here later (detect scroll end, call viewModel.loadMore())
                ),
                 _ => const SizedBox.shrink(),
        },
      ),
    );
  }
}
