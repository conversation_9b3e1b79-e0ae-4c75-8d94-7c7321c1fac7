// lib/features/search/screens/search_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/features/search/state/search_state.dart';
import 'package:watermelon_draft/features/search/viewmodels/search_viewmodel.dart';
import 'package:watermelon_draft/features/search/screens/filter_page.dart'; // Import FilterPage
import 'package:watermelon_draft/widgets/activity_search_input_widget.dart';
import 'package:watermelon_draft/widgets/selected_items_chips_widget.dart';
import 'package:watermelon_draft/core/utils/ui_utils.dart'; // For the bottom sheet function
import 'package:collection/collection.dart'; // For firstWhereOrNull
import 'package:watermelon_draft/widgets/keyword_search_input_widget.dart';
import 'package:watermelon_draft/widgets/member_search_input_widget.dart';

class SearchPage extends ConsumerStatefulWidget {
  const SearchPage({super.key});

  @override
  ConsumerState<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends ConsumerState<SearchPage> {
  void _resetSearchCriteria() {
    ref.read(searchViewModelProvider.notifier).clearInterestCriteria();
    // Optionally reset name search too?
    // _userSearchController.clear();
    FocusScope.of(context).unfocus();
  }

  void _applyInterestSearch() {
    // Trigger search in VM
    final viewModel = ref.read(searchViewModelProvider.notifier);
    viewModel.triggerInterestSearch().then((_) {
      // Check mounted after async gap
      if (!mounted) return;
      final state =
          ref.read(searchViewModelProvider).valueOrNull; // Read latest state
      if (state?.interestSearchError == null) {
        context.beamToNamed('/search/results'); // Navigate on success
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(state!.interestSearchError!),
            backgroundColor: Colors.red));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // --- Watch the SearchViewModel state ---
    final stateAsync = ref.watch(searchViewModelProvider);
    // Get state data or default
    final state = stateAsync.valueOrNull ?? const SearchState();
    final viewModel = ref.read(searchViewModelProvider.notifier);
    // --- Watch dependent providers ---
    final allActivitiesAsync = ref.watch(sharedActivitiesProvider);
    final allKeywordsAsync = ref.watch(keywordsProvider);
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Members'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            // Directly pop without checking for changes
            Navigator.pop(context);
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.fromLTRB(
          16.0,
          16.0,
          16.0,
          bottomInset + 16.0,
        ), // Pad top/sides, not bottom
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // --- Section 1: Member Search (Name/Username) ---
            Text('Search Directly',
                style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            const MemberSearchInputWidget(),

            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),

            // --- Section 2: Discover by Interests/Activities ---
            Text('Discover by Interests & Activities',
                style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),

            // Applied Filters Display
            Row(children: [
              Text('Filters Applied:',
                  style: Theme.of(context).textTheme.labelLarge),
              IconButton(
                icon: Badge(
                    // Show badge count
                    label: Text(_calculateActiveFilterCount(state).toString()),
                    isLabelVisible: _calculateActiveFilterCount(state) > 0,
                    child: const Icon(Icons.filter_list)),
                tooltip: 'Edit Filters',
                onPressed: () async {
                  // Navigate to FilterPage
                  final filterResult =
                      await Navigator.push<Map<String, dynamic>?>(
                    context,
                    MaterialPageRoute(
                        builder: (context) => FilterPage(
                              initialMinAge: state.appliedMinAge,
                              initialMaxAge: state.appliedMaxAge,
                              initialGender: state.appliedGender,
                            )),
                  );
                  if (filterResult != null && mounted) {
                    viewModel.updateAgeGenderFilters(
                      minAge: filterResult['minAge'],
                      maxAge: filterResult['maxAge'],
                      gender: filterResult['gender'],
                    );
                  }
                },
              ),
            ]),
            const SizedBox(height: 4),
            _buildActiveFilterChips(
                context, state, viewModel), // Helper builds chips

            const SizedBox(height: 16),

            // --- Shared Activities Section ---
            Text("Filter by Shared Activities:",
                style: Theme.of(context).textTheme.labelLarge),
            const SizedBox(height: 8),

            // Display Selected Activity Chips
            switch (allActivitiesAsync) {
              AsyncData(:final value) => SelectedItemsChipsWidget(
                  items: state.selectedActivities.map((id) {
                    // Map IDs to Names
                    final activity =
                        value.firstWhereOrNull((a) => a.activityId == id);
                    return activity?.activityName ?? 'Unknown';
                  }).toList(),
                  onItemDeleted: (itemName) {
                    // Find ID from name to remove
                    final activity = value
                        .firstWhereOrNull((a) => a.activityName == itemName);
                    if (activity != null) {
                      viewModel.removeSelectedActivity(activity.activityId);
                    }
                  },
                  noItemsText: 'Any Activity', // Text when none selected
                ),
              AsyncLoading() => const SizedBox(
                  height: 30,
                  child: Center(
                      child: SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(strokeWidth: 2)))),
              AsyncError(:final error, :final stackTrace) => () {
                  // Capture stackTrace for logging
                  print(
                      "SearchPage: Error loading shared activities for chips: $error\n$stackTrace"); // Log actual error
                  String displayMessage = "Error displaying activities";
                  if (error is Failure) {
                    displayMessage = error.message;
                  } else {
                    displayMessage = error.toString();
                  }
                  return Text(
                    'Cannot display chips: $displayMessage',
                    style: TextStyle(color: Colors.red, fontSize: 10),
                  );
                }(),
              _ => const SizedBox.shrink(), // Handle potential unexpected states
            },
            const SizedBox(height: 8),

            // Autocomplete Input for Activities
            ActivitySearchInputWidget(
              onActivitySelected: (activity) {
                viewModel.addSelectedActivity(activity);
              },
              currentSelectionIds:
                  state.selectedActivities, // Filter out selected
            ),
            const SizedBox(height: 8),

            // Button to open full list selector
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () async {
                  // Call the reusable utility function
                  final List<String>? result =
                      await showSharedActivitySelectionSheet(
                    context: context,
                    ref: ref,
                    initialSelectedIds: state.selectedActivities,
                    maxSelection: 10, // Allow more for search
                    minSelection: 0, // Allow zero selection for search
                    title: 'Filter by Activities',
                  );
                  // Update the main ViewModel state if user pressed "Done"
                  if (result != null && mounted) {
                    // Check if changed before updating
                    if (!const DeepCollectionEquality()
                        .equals(result, state.selectedActivities)) {
                      viewModel.updateSelectedActivities(result);
                    }
                  }
                },
                child: const Text('Select from Full List...'),
              ),
            ),

            const SizedBox(height: 16),

            // --- Interests Section ---
            Text("Filter by Interests:",
                style: Theme.of(context).textTheme.labelLarge),
            const SizedBox(height: 8),

            // Display Selected Keyword Chips
            SelectedItemsChipsWidget(
              items: state.selectedKeywords,
              onItemDeleted: (keyword) {
                viewModel.removeSelectedKeyword(keyword);
              },
              noItemsText: 'Any Interest',
            ),
            const SizedBox(height: 8),

            // Autocomplete Input for Keywords
            switch (allKeywordsAsync) {
              // Use switch on the AsyncValue
              AsyncData(:final value) => KeywordSearchInputWidget(
                  allKeywords: value, // Pass loaded list
                  onKeywordSelected: (keyword) {
                    print(
                        "SearchPage: onKeywordSelected callback for ${keyword.keywordText}");
                    viewModel.addSelectedKeyword(keyword.keywordText);
                  },
                  currentSelectionNames: state.selectedKeywords,
                ),
              AsyncLoading() => const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Center(
                      child: SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(strokeWidth: 2))),
                ),
              AsyncError(:final error, :final stackTrace) => () {
                  // Immediately invoked function expression
                  print(
                      "SearchPage: Error loading keywords for input: $error\n$stackTrace");
                  String displayMessage = "Could not load interests";
                  if (error is Failure) {
                    displayMessage = error.message;
                  } else {
                    displayMessage = error.toString();
                  }
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text("$displayMessage: ${error.toString()}",
                        style: TextStyle(color: Colors.red, fontSize: 12)),
                  );
                }(), // Immediately invoke
              _ => const SizedBox.shrink(),
            },
            const SizedBox(height: 24),

            // --- Section 3: Distance Filter ---
            Text('Distance', style: Theme.of(context).textTheme.titleMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround, // Or use Wrap
              children: ['local', 'regional', 'national', 'global']
                  .map((option) => Expanded(
                          // Use Expanded for equal space
                          child: RadioListTile<String>(
                        title: Text(option.capitalize()), // Use extension
                        value: option,
                        groupValue: state.selectedDistanceFilter,
                        onChanged: (value) {
                          if (value != null) {
                            viewModel.updateDistanceFilter(value);
                          }
                        },
                        contentPadding: EdgeInsets.zero, // Reduce padding
                        visualDensity: VisualDensity.compact,
                      )))
                  .toList(),
            ),

            const SizedBox(height: 40), // Space before buttons

            // --- Bottom Action Buttons ---
            Row(
              children: [
                OutlinedButton(
                  onPressed: _resetSearchCriteria,
                  child: const Text('Clear Criteria'), // Changed label
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: state.isLoadingInterestSearch
                        ? SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                                strokeWidth: 2, color: Colors.white))
                        : Icon(Icons.search),
                    label: const Text('Show Users'), // Changed label
                    onPressed: state.isLoadingInterestSearch
                        ? null // Disable if loading
                        : _applyInterestSearch,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // --- Helper to calculate active filters ---
  int _calculateActiveFilterCount(SearchState state) {
    int count = 0;
    if (state.appliedMinAge != null || state.appliedMaxAge != null) {
      if (state.appliedMinAge != defaultFilterMinAge.round() ||
          state.appliedMaxAge != defaultFilterMaxAge.round()) {
        count++;
      }
    }
    if (state.appliedGender != null &&
        state.appliedGender != defaultFilterGender) {
      count++;
    }
    return count;
  }

  // --- Helper to build active filter chips ---
  Widget _buildActiveFilterChips(
      BuildContext context, SearchState state, SearchViewModel viewModel) {
    List<Widget> chips = [];
    // Age Chip
    if (state.appliedMinAge != null || state.appliedMaxAge != null) {
      if (state.appliedMinAge != defaultFilterMinAge.round() ||
          state.appliedMaxAge != defaultFilterMaxAge.round()) {
        chips.add(Chip(
          label: Text(
              'Age: ${state.appliedMinAge ?? defaultFilterMinAge.round()}-${state.appliedMaxAge ?? defaultFilterMaxAge.round()}'),
          onDeleted: () => viewModel.updateAgeGenderFilters(
              minAge: null,
              maxAge: null,
              gender: state.appliedGender), // Reset only age
        ));
      }
    }
    // Gender Chip
    if (state.appliedGender != null &&
        state.appliedGender != defaultFilterGender) {
      chips.add(Chip(
        label: Text('Gender: ${state.appliedGender}'),
        onDeleted: () => viewModel.updateAgeGenderFilters(
            minAge: state.appliedMinAge,
            maxAge: state.appliedMaxAge,
            gender: null), // Reset only gender
      ));
    }

    if (chips.isEmpty) {
      return const SizedBox.shrink(); // Return nothing if no filters applied
    }

    return Wrap(
      spacing: 6.0,
      runSpacing: 0.0,
      children: chips,
    );
  }
}

//Add extension
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
