// lib/features/search/screens/filter_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Use ConsumerStatefulWidget

// Define default age range values
const double defaultFilterMinAge = 16.0;
const double defaultFilterMaxAge = 99.0;
const String defaultFilterGender = 'Any';
final List<String> filterGenders = [
  'Any',
  'Male',
  'Female'
]; // Gender options list

class FilterPage extends ConsumerStatefulWidget {
  final int? initialMinAge;
  final int? initialMaxAge;
  final String? initialGender;

  const FilterPage({
    super.key,
    this.initialMinAge,
    this.initialMaxAge,
    this.initialGender,
  });

  @override
  ConsumerState<FilterPage> createState() => _FilterPageState();
}

class _FilterPageState extends ConsumerState<FilterPage> {
  late RangeValues _currentRangeValues;
  late String _selectedGender;
  bool _hasChanges = false; // Track unsaved changes

  @override
  void initState() {
    super.initState();
    _currentRangeValues = RangeValues(
        widget.initialMinAge?.toDouble() ?? defaultFilterMinAge,
        widget.initialMaxAge?.toDouble() ?? defaultFilterMaxAge);
    _selectedGender = widget.initialGender ?? defaultFilterGender;
  }

  void _markChanged() {
    if (!_hasChanges) {
      setState(() => _hasChanges = true);
    }
  }

  void _resetFilters() {
    setState(() {
      _currentRangeValues =
          const RangeValues(defaultFilterMinAge, defaultFilterMaxAge);
      _selectedGender = defaultFilterGender;
      _hasChanges = true; // Mark as changed after reset
    });
  }

  void _applyFilters() {
    final resultMap = {
      'minAge': _currentRangeValues.start.round(),
      'maxAge': _currentRangeValues.end.round(),
      // Return null for gender if 'Any' is selected, otherwise return the value
      'gender': _selectedGender == defaultFilterGender ? null : _selectedGender,
    };
    // Pop the screen and return the selected values
    Navigator.pop(context, resultMap);
  }

  // Unsaved Changes Check
  // --- Dialog for Discarding Changes ---
  Future<bool> _showDiscardDialog() async {
    // Capture context before await
    final currentContext = context;
    // Check mounted BEFORE showing dialog (good practice)
    if (!mounted) return false;

    final shouldDiscard = await showDialog<bool>(
      context: currentContext, // Use captured context
      builder: (dialogContext) => AlertDialog(
        // Use different context name for builder
        title: const Text('Discard Changes?'),
        content: const Text(
            'You have unsaved filter changes. Are you sure you want to discard them?'),
        actions: [
          TextButton(
            onPressed: () =>
                Navigator.of(dialogContext).pop(false), // Don't discard
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true), // Discard
            child: const Text('Discard'),
          ),
        ],
      ),
    );
    return shouldDiscard ?? false; // Return true only if user confirmed discard
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasChanges, // Prevent popping IF there are changes
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        // This is called AFTER a pop attempt
        if (didPop) {
          // Pop was successful (either no changes, or user confirmed discard via AppBar button)
          return;
        }
        // Pop was prevented because canPop was false (due to _hasChanges)
        // This happens on system back gesture or if Navigator.maybePop was called
        print("Pop prevented due to changes, showing dialog...");
        // Capture context BEFORE await for dialog
        final currentContext = context;
        final bool shouldDiscard = await _showDiscardDialog();
        // Check context mounted AFTER await
        if (shouldDiscard && currentContext.mounted) {
          Navigator.pop(currentContext); // Use captured context
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Filter'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () async {
              // Close button logic
              if (!_hasChanges) {
                Navigator.pop(context); // No changes, just pop
              } else {
                // Capture context BEFORE await
                final currentContext = context;
                // Has changes, show dialog first
                final shouldDiscard = await _showDiscardDialog();
                // Check context mounted AFTER await
                if (shouldDiscard && currentContext.mounted) {
                  Navigator.pop(currentContext); // Use captured context
                }
              }
            },
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // --- Age Range Section ---
              Text(
                'Age Range',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              RangeSlider(
                values: _currentRangeValues,
                min: defaultFilterMinAge, // Use constant
                max: defaultFilterMaxAge, // Use constant
                divisions: (defaultFilterMaxAge - defaultFilterMinAge).round(),
                labels: RangeLabels(
                  _currentRangeValues.start.round().toString(),
                  _currentRangeValues.end.round().toString(),
                ),
                onChanged: (RangeValues values) {
                  setState(() {
                    _currentRangeValues = values;
                  });
                  _markChanged(); // Mark as changed on slider move
                },
              ),
              // Display current selected range
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Min: ${_currentRangeValues.start.round()}'),
                  Text('Max: ${_currentRangeValues.end.round()}'),
                ],
              ),
              const SizedBox(height: 24),

              // --- Gender Section ---
              Text(
                'Gender',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              Column(
                // Keep RadioListTiles in Column
                children: filterGenders
                    .map((gender) => RadioListTile<String>(
                          title: Text(gender),
                          value: gender,
                          groupValue: _selectedGender,
                          onChanged: (String? value) {
                            if (value != null) {
                              setState(() {
                                _selectedGender = value;
                              });
                              _markChanged(); // Mark changes
                            }
                          },
                        ))
                    .toList(),
              ),

              const Spacer(), // Pushes buttons to the bottom

              // --- Action Buttons at Bottom ---
              SafeArea(
                // Ensure buttons aren't obscured by notches/indicators
                child: Row(
                  children: [
                    OutlinedButton(
                        onPressed: _resetFilters, child: Text('Reset')),
                    SizedBox(width: 16),
                    Expanded(
                      // Make Apply button take available space
                      child: OutlinedButton(
                        onPressed: _applyFilters,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Apply Filters'),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10), // Spacing at bottom
            ],
          ),
        ),
      ),
    );
  }
}
