// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_results_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchResultsViewModelHash() =>
    r'33c1f879a198621220ff7bd5daaf58478eb7b1e5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$SearchResultsViewModel
    extends BuildlessAutoDisposeAsyncNotifier<List<User>> {
  late final SearchCriteria criteria;

  FutureOr<List<User>> build(
    SearchCriteria criteria,
  );
}

/// See also [SearchResultsViewModel].
@ProviderFor(SearchResultsViewModel)
const searchResultsViewModelProvider = SearchResultsViewModelFamily();

/// See also [SearchResultsViewModel].
class SearchResultsViewModelFamily extends Family<AsyncValue<List<User>>> {
  /// See also [SearchResultsViewModel].
  const SearchResultsViewModelFamily();

  /// See also [SearchResultsViewModel].
  SearchResultsViewModelProvider call(
    SearchCriteria criteria,
  ) {
    return SearchResultsViewModelProvider(
      criteria,
    );
  }

  @override
  SearchResultsViewModelProvider getProviderOverride(
    covariant SearchResultsViewModelProvider provider,
  ) {
    return call(
      provider.criteria,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'searchResultsViewModelProvider';
}

/// See also [SearchResultsViewModel].
class SearchResultsViewModelProvider
    extends AutoDisposeAsyncNotifierProviderImpl<SearchResultsViewModel,
        List<User>> {
  /// See also [SearchResultsViewModel].
  SearchResultsViewModelProvider(
    SearchCriteria criteria,
  ) : this._internal(
          () => SearchResultsViewModel()..criteria = criteria,
          from: searchResultsViewModelProvider,
          name: r'searchResultsViewModelProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$searchResultsViewModelHash,
          dependencies: SearchResultsViewModelFamily._dependencies,
          allTransitiveDependencies:
              SearchResultsViewModelFamily._allTransitiveDependencies,
          criteria: criteria,
        );

  SearchResultsViewModelProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.criteria,
  }) : super.internal();

  final SearchCriteria criteria;

  @override
  FutureOr<List<User>> runNotifierBuild(
    covariant SearchResultsViewModel notifier,
  ) {
    return notifier.build(
      criteria,
    );
  }

  @override
  Override overrideWith(SearchResultsViewModel Function() create) {
    return ProviderOverride(
      origin: this,
      override: SearchResultsViewModelProvider._internal(
        () => create()..criteria = criteria,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        criteria: criteria,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<SearchResultsViewModel, List<User>>
      createElement() {
    return _SearchResultsViewModelProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SearchResultsViewModelProvider &&
        other.criteria == criteria;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, criteria.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SearchResultsViewModelRef
    on AutoDisposeAsyncNotifierProviderRef<List<User>> {
  /// The parameter `criteria` of this provider.
  SearchCriteria get criteria;
}

class _SearchResultsViewModelProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<SearchResultsViewModel,
        List<User>> with SearchResultsViewModelRef {
  _SearchResultsViewModelProviderElement(super.provider);

  @override
  SearchCriteria get criteria =>
      (origin as SearchResultsViewModelProvider).criteria;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
