// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchViewModelHash() => r'c165c2d24ddb1c48618fdc38b411a62405cbd18a';

/// See also [SearchViewModel].
@ProviderFor(SearchViewModel)
final searchViewModelProvider =
    AutoDisposeAsyncNotifierProvider<SearchViewModel, SearchState>.internal(
  SearchViewModel.new,
  name: r'searchViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchViewModel = AutoDisposeAsyncNotifier<SearchState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
