// lib/features/search/viewmodels/search_viewmodel.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/models/app_location.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/features/profile/providers/profile_providers.dart';
import 'package:watermelon_draft/core/services/user_repository.dart';
import 'package:watermelon_draft/features/search/state/search_state.dart';

part 'search_viewmodel.g.dart';

@riverpod
class SearchViewModel extends _$SearchViewModel {
  late final UserRepository _userRepository;
  Timer? _nameSearchDebounce;

  @override
  FutureOr<SearchState> build() async {
    _userRepository = ref.watch(userRepositoryProvider);
    ref.onDispose(() {
      _nameSearchDebounce?.cancel();
    });

    // --- TEMP MODE ---
    User? currentUser; // Default to null
    try {
      // Attempt to fetch current user profile
      currentUser = await ref.watch(currentUserProfileProvider.future);
    } catch (e, s) {
      print(
          "SearchViewModel.build: Error fetching current user profile: $e\n$s");
      // In watermelon_draft (kDebugMode), we'll proceed with currentUser as null
      // to allow SearchPage UI testing. In production, this error should be handled.
      if (!kDebugMode) {
        // Only rethrow if not in debug mode
        rethrow;
      }
      print(
          "⚠️ SearchViewModel.build: Proceeding with null current user due to error (DEBUG MODE).");
    }

    // Return an initial state, even if currentUser is null
    return SearchState(
      currentUserLocation: currentUser?.location != null
          ? AppLocationConverters.toGeoPoint(currentUser!.location!)
          : null,
      currentUserCountry: currentUser?.country,
      // other fields will use their @Default values
    );
    // --- END TEMP MODE ---

    // Fetch current user profile to get initial location/country for filters
    // This relies on currentUserProfileProvider being kept alive
    // -------- uncomment this after temp mode ----------
    // final currentUserProfile =
    //     await ref.watch(currentUserProfileProvider.future);

    // return SearchState(
    //   currentUserLocation: currentUserProfile?.location,
    //   currentUserCountry: currentUserProfile?.country,
    // );
  }

  // --- Name/Username Search ---
  void searchUsersByName(String query) {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          userQuery: query,
          nameSearchError: null,
          isLoadingNameSearch: query.length >= 2));
    });

    _nameSearchDebounce?.cancel();
    if (query.length < 2) {
      // Minimum length for name search
      state = AsyncData(state.value!
          .copyWith(userSuggestions: [], isLoadingNameSearch: false));
      return;
    }

    _nameSearchDebounce = Timer(const Duration(milliseconds: 400), () async {
      // Read current state value right before the async call
      final currentQueryInState = state.value?.userQuery;
      if (query != currentQueryInState || !state.hasValue) {
        return; // Check if query changed
      }

      final result =
          await _userRepository.searchUsersByName(query); // use default limit 7

      // Check if query or state changed during await
      if (query == state.value?.userQuery && state.hasValue) {
        result.fold((failure) {
          state = AsyncData(state.value!.copyWith(
              nameSearchError: failure.message,
              isLoadingNameSearch: false,
              userSuggestions: []));
        }, (users) {
          state = AsyncData(state.value!.copyWith(
              userSuggestions: users,
              isLoadingNameSearch: false,
              nameSearchError: null));
        });
      }
    });
  }

  // --- Interest Search Criteria Updates ---

  // Methods to manage selected search activities
  void addSelectedActivity(SharedActivity activity) {
    state.whenData((currentData) {
      // Check limit if needed for search (e.g., 10)
      // const int searchMaxActivities = 10;
      // if (currentData.selectedActivities.length >= searchMaxActivities) return;

      if (!currentData.selectedActivities.contains(activity.activityId)) {
        final updatedList = List<String>.from(currentData.selectedActivities)
          ..add(activity.activityId);
        state = AsyncData(currentData.copyWith(
            selectedActivities: updatedList,
            // Clear results when criteria change
            interestSearchResults: [],
            interestSearchError: null));
      }
    });
  }

  void removeSelectedActivity(String activityId) {
    state.whenData((currentData) {
      final updatedList = List<String>.from(currentData.selectedActivities)
        ..remove(activityId);
      // No minimum check needed for search criteria
      state = AsyncData(currentData.copyWith(
          selectedActivities: updatedList,
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  void updateSelectedActivities(List<String> activityIds) {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          selectedActivities: activityIds,
          // Clear previous interest results when criteria change
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  // Methods to manage selected search keywords
  void addSelectedKeyword(String keyword) {
    print("SearchViewModel: Attempting to add keyword - '$keyword'");
    final currentData = state.valueOrNull;
    if (currentData == null) {
      print("SearchViewModel: Cannot add keyword, current state data is null.");
      return;
    }

    if (!currentData.selectedKeywords.contains(keyword)) {
      final updatedList = List<String>.from(currentData.selectedKeywords)
        ..add(keyword);
      state = AsyncData(currentData.copyWith(
          selectedKeywords: updatedList,
          interestSearchResults: [],
          interestSearchError: null));
      print(
          "SearchViewModel: Added keyword - '$keyword'. New list: $updatedList");
    } else {
      print("SearchViewModel: Keyword '$keyword' already selected.");
    }
  }

  void removeSelectedKeyword(String keyword) {
    state.whenData((currentData) {
      final updatedList = List<String>.from(currentData.selectedKeywords)
        ..remove(keyword);
      state = AsyncData(currentData.copyWith(
          selectedKeywords: updatedList,
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  void updateKeywords(List<String> keywords) {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          selectedKeywords: keywords,
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  void updateDistanceFilter(String filter) {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          selectedDistanceFilter: filter,
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  void updateAgeGenderFilters({int? minAge, int? maxAge, String? gender}) {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          appliedMinAge: minAge,
          appliedMaxAge: maxAge,
          appliedGender: gender,
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  void clearInterestCriteria() {
    state.whenData((currentData) {
      state = AsyncData(currentData.copyWith(
          selectedActivities: [],
          selectedKeywords: [],
          selectedDistanceFilter: defaultDistanceFilter, // Reset distance
          // Keep age/gender filters as per user request
          interestSearchResults: [],
          interestSearchError: null));
    });
  }

  // --- Trigger Interest Search ---
  Future<void> triggerInterestSearch() async {
    final currentData = state.valueOrNull;
    if (currentData == null) return; // Need current state

    // Basic validation: Need at least one activity OR keyword
    if (currentData.selectedActivities.isEmpty &&
        currentData.selectedKeywords.isEmpty) {
      state = AsyncData(currentData.copyWith(
          interestSearchError:
              "Please select at least one activity or interest.",
          isLoadingInterestSearch: false,
          interestSearchResults: []));
      return;
    }

    // Check if location/country needed for filter is available
    if ((currentData.selectedDistanceFilter == 'local' ||
            currentData.selectedDistanceFilter == 'regional') &&
        currentData.currentUserLocation == null) {
      state = AsyncData(currentData.copyWith(
          interestSearchError:
              "Your location is needed for local/regional search.",
          isLoadingInterestSearch: false,
          interestSearchResults: []));
      return;
    }
    if (currentData.selectedDistanceFilter == 'national' &&
        currentData.currentUserCountry == null) {
      state = AsyncData(currentData.copyWith(
          interestSearchError: "Your country is needed for national search.",
          isLoadingInterestSearch: false,
          interestSearchResults: []));
      return;
    }

    state = AsyncData(currentData.copyWith(
        isLoadingInterestSearch: true, interestSearchError: null));

    final result = await _userRepository.searchUsersByInterests(
        sharedActivities: currentData.selectedActivities,
        keywords: currentData.selectedKeywords,
        distanceFilter: currentData.selectedDistanceFilter,
        currentUserLocation: currentData.currentUserLocation,
        currentUserCountry: currentData.currentUserCountry,
        minAge: currentData.appliedMinAge,
        maxAge: currentData.appliedMaxAge,
        gender: currentData.appliedGender);

    if (state.hasValue) {
      // Check state hasn't changed during await
      result.fold((failure) {
        state = AsyncData(state.value!.copyWith(
            interestSearchError: failure.message,
            isLoadingInterestSearch: false,
            interestSearchResults: []));
      }, (users) {
        state = AsyncData(state.value!.copyWith(
            interestSearchResults: users,
            isLoadingInterestSearch: false,
            interestSearchError: null));
      });
    }
  }
}
