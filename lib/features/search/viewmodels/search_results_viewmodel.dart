// lib/features/search/viewmodels/search_results_viewmodel.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/features/profile/providers/profile_providers.dart'; // For currentUserProfileProvider
import 'package:watermelon_draft/core/services/user_repository.dart';
import 'package:watermelon_draft/features/search/models/search_criteria.dart'; // Import criteria model
import 'package:watermelon_draft/core/errors.dart'; // For Failure
import 'package:fpdart/fpdart.dart';

part 'search_results_viewmodel.g.dart';

@riverpod
class SearchResultsViewModel extends _$SearchResultsViewModel {
  // The build method now takes the criteria as an argument
  @override
  Future<List<User>> build(SearchCriteria criteria) async {
    print(
        "SearchResultsViewModel build executing with criteria: $criteria"); // Debug log
    final userRepository = ref.watch(userRepositoryProvider);
    final currentUserProfileAsync = ref.watch(currentUserProfileProvider);

    // We need the current user's location/country for distance/national filters
    // Use the already loaded profile data
    final currentUser = currentUserProfileAsync.valueOrNull;
    // TODO: Consider how to handle if currentUserProfileProvider is loading/error here.
    // For now, proceed if available, otherwise distance filters might not apply correctly.
    if (currentUser == null &&
        (criteria.distanceFilter == 'local' ||
            criteria.distanceFilter == 'regional' ||
            criteria.distanceFilter == 'national')) {
      print(
          "WARN: Current user profile not loaded, cannot apply distance/national filters accurately.");
      // Decide: throw error, return empty, or search without location context?
      // Let's search without location context for now, global effectively.
      final result = await userRepository.searchUsersByInterests(
        // query: null, // This is interest search, not name search
        sharedActivities: criteria.activities,
        keywords: criteria.keywords,
        distanceFilter:
            'global', // Fallback to global if user location unavailable
        currentUserLocation: null,
        currentUserCountry: null,
        minAge: criteria.minAge,
        maxAge: criteria.maxAge,
        gender: criteria.gender,
      );
      return result.fold(
        (failure) => throw failure, // Throw error from repo
        (users) => users,
      );
    }

    // Proceed with search using fetched criteria and user location/country
    final result = await userRepository.searchUsersByInterests(
      // query: null, // Interest search only
      sharedActivities: criteria.activities,
      keywords: criteria.keywords,
      distanceFilter: criteria.distanceFilter,
      currentUserLocation: currentUser?.location, // Pass location if available
      currentUserCountry: currentUser?.country, // Pass country if available
      minAge: criteria.minAge,
      maxAge: criteria.maxAge,
      gender: criteria.gender,
    );

    return result.fold(
      (failure) => throw failure, // Let Riverpod handle error state
      (users) => users, // Return the list of users on success
    );
  }

  // TODO: Implement loadMore() method for pagination later
  // Future<void> loadMore() async { ... }
}
