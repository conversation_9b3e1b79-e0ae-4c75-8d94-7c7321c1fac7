// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_criteria.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchCriteria implements DiagnosticableTreeMixin {
// Interest-based criteria
  List<String>? get activities;
  List<String>? get keywords;
  String? get distanceFilter; // 'local', 'regional', 'national', 'global'
// Profile filters
  int? get minAge;
  int? get maxAge;
  String? get gender;

  /// Create a copy of SearchCriteria
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchCriteriaCopyWith<SearchCriteria> get copyWith =>
      _$SearchCriteriaCopyWithImpl<SearchCriteria>(
          this as SearchCriteria, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'SearchCriteria'))
      ..add(DiagnosticsProperty('activities', activities))
      ..add(DiagnosticsProperty('keywords', keywords))
      ..add(DiagnosticsProperty('distanceFilter', distanceFilter))
      ..add(DiagnosticsProperty('minAge', minAge))
      ..add(DiagnosticsProperty('maxAge', maxAge))
      ..add(DiagnosticsProperty('gender', gender));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchCriteria &&
            const DeepCollectionEquality()
                .equals(other.activities, activities) &&
            const DeepCollectionEquality().equals(other.keywords, keywords) &&
            (identical(other.distanceFilter, distanceFilter) ||
                other.distanceFilter == distanceFilter) &&
            (identical(other.minAge, minAge) || other.minAge == minAge) &&
            (identical(other.maxAge, maxAge) || other.maxAge == maxAge) &&
            (identical(other.gender, gender) || other.gender == gender));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(activities),
      const DeepCollectionEquality().hash(keywords),
      distanceFilter,
      minAge,
      maxAge,
      gender);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SearchCriteria(activities: $activities, keywords: $keywords, distanceFilter: $distanceFilter, minAge: $minAge, maxAge: $maxAge, gender: $gender)';
  }
}

/// @nodoc
abstract mixin class $SearchCriteriaCopyWith<$Res> {
  factory $SearchCriteriaCopyWith(
          SearchCriteria value, $Res Function(SearchCriteria) _then) =
      _$SearchCriteriaCopyWithImpl;
  @useResult
  $Res call(
      {List<String>? activities,
      List<String>? keywords,
      String? distanceFilter,
      int? minAge,
      int? maxAge,
      String? gender});
}

/// @nodoc
class _$SearchCriteriaCopyWithImpl<$Res>
    implements $SearchCriteriaCopyWith<$Res> {
  _$SearchCriteriaCopyWithImpl(this._self, this._then);

  final SearchCriteria _self;
  final $Res Function(SearchCriteria) _then;

  /// Create a copy of SearchCriteria
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activities = freezed,
    Object? keywords = freezed,
    Object? distanceFilter = freezed,
    Object? minAge = freezed,
    Object? maxAge = freezed,
    Object? gender = freezed,
  }) {
    return _then(_self.copyWith(
      activities: freezed == activities
          ? _self.activities
          : activities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      keywords: freezed == keywords
          ? _self.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      distanceFilter: freezed == distanceFilter
          ? _self.distanceFilter
          : distanceFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      minAge: freezed == minAge
          ? _self.minAge
          : minAge // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAge: freezed == maxAge
          ? _self.maxAge
          : maxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _SearchCriteria with DiagnosticableTreeMixin implements SearchCriteria {
  const _SearchCriteria(
      {final List<String>? activities,
      final List<String>? keywords,
      this.distanceFilter,
      this.minAge,
      this.maxAge,
      this.gender})
      : _activities = activities,
        _keywords = keywords;

// Interest-based criteria
  final List<String>? _activities;
// Interest-based criteria
  @override
  List<String>? get activities {
    final value = _activities;
    if (value == null) return null;
    if (_activities is EqualUnmodifiableListView) return _activities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _keywords;
  @override
  List<String>? get keywords {
    final value = _keywords;
    if (value == null) return null;
    if (_keywords is EqualUnmodifiableListView) return _keywords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? distanceFilter;
// 'local', 'regional', 'national', 'global'
// Profile filters
  @override
  final int? minAge;
  @override
  final int? maxAge;
  @override
  final String? gender;

  /// Create a copy of SearchCriteria
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchCriteriaCopyWith<_SearchCriteria> get copyWith =>
      __$SearchCriteriaCopyWithImpl<_SearchCriteria>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'SearchCriteria'))
      ..add(DiagnosticsProperty('activities', activities))
      ..add(DiagnosticsProperty('keywords', keywords))
      ..add(DiagnosticsProperty('distanceFilter', distanceFilter))
      ..add(DiagnosticsProperty('minAge', minAge))
      ..add(DiagnosticsProperty('maxAge', maxAge))
      ..add(DiagnosticsProperty('gender', gender));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchCriteria &&
            const DeepCollectionEquality()
                .equals(other._activities, _activities) &&
            const DeepCollectionEquality().equals(other._keywords, _keywords) &&
            (identical(other.distanceFilter, distanceFilter) ||
                other.distanceFilter == distanceFilter) &&
            (identical(other.minAge, minAge) || other.minAge == minAge) &&
            (identical(other.maxAge, maxAge) || other.maxAge == maxAge) &&
            (identical(other.gender, gender) || other.gender == gender));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_activities),
      const DeepCollectionEquality().hash(_keywords),
      distanceFilter,
      minAge,
      maxAge,
      gender);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SearchCriteria(activities: $activities, keywords: $keywords, distanceFilter: $distanceFilter, minAge: $minAge, maxAge: $maxAge, gender: $gender)';
  }
}

/// @nodoc
abstract mixin class _$SearchCriteriaCopyWith<$Res>
    implements $SearchCriteriaCopyWith<$Res> {
  factory _$SearchCriteriaCopyWith(
          _SearchCriteria value, $Res Function(_SearchCriteria) _then) =
      __$SearchCriteriaCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<String>? activities,
      List<String>? keywords,
      String? distanceFilter,
      int? minAge,
      int? maxAge,
      String? gender});
}

/// @nodoc
class __$SearchCriteriaCopyWithImpl<$Res>
    implements _$SearchCriteriaCopyWith<$Res> {
  __$SearchCriteriaCopyWithImpl(this._self, this._then);

  final _SearchCriteria _self;
  final $Res Function(_SearchCriteria) _then;

  /// Create a copy of SearchCriteria
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activities = freezed,
    Object? keywords = freezed,
    Object? distanceFilter = freezed,
    Object? minAge = freezed,
    Object? maxAge = freezed,
    Object? gender = freezed,
  }) {
    return _then(_SearchCriteria(
      activities: freezed == activities
          ? _self._activities
          : activities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      keywords: freezed == keywords
          ? _self._keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      distanceFilter: freezed == distanceFilter
          ? _self.distanceFilter
          : distanceFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      minAge: freezed == minAge
          ? _self.minAge
          : minAge // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAge: freezed == maxAge
          ? _self.maxAge
          : maxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
