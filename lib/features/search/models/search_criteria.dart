// lib/features/search/models/search_criteria.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart'; // For @required

part 'search_criteria.freezed.dart';
// part 'search_criteria.g.dart'; // Add if you need JSON serialization later (optional now)

@freezed
abstract class SearchCriteria with _$SearchCriteria {
  const factory SearchCriteria({
    // Interest-based criteria
    List<String>? activities,
    List<String>? keywords,
    String? distanceFilter, // 'local', 'regional', 'national', 'global'

    // Profile filters
    int? minAge,
    int? maxAge,
    String? gender, // Null if 'Any'

    // Note: Current user location/country needed for distance/national filter
    // will be read within the SearchResultsViewModel, not passed here.
  }) = _SearchCriteria;

  // Optional: Add fromJson factory if you serialize this (e.g., for saving searches)
  // factory SearchCriteria.fromJson(Map<String, dynamic> json) => _$SearchCriteriaFromJson(json);
}
