// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SearchState {
// Name/Username Search State
  String get userQuery;
  List<User> get userSuggestions;
  bool get isLoadingNameSearch;
  String? get nameSearchError; // Interest Search Criteria
  List<String> get selectedActivities; // List of activity IDs
  List<String> get selectedKeywords;
  String
      get selectedDistanceFilter; // 'local', 'regional', 'national', 'global'
  int? get appliedMinAge; // Filters applied via FilterPage
  int? get appliedMaxAge;
  String? get appliedGender; // Interest Search Results State
  List<User> get interestSearchResults;
  bool get isLoadingInterestSearch;
  String?
      get interestSearchError; // Data needed for filtering based on current user
  GeoPoint? get currentUserLocation;
  String? get currentUserCountry;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchStateCopyWith<SearchState> get copyWith =>
      _$SearchStateCopyWithImpl<SearchState>(this as SearchState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchState &&
            (identical(other.userQuery, userQuery) ||
                other.userQuery == userQuery) &&
            const DeepCollectionEquality()
                .equals(other.userSuggestions, userSuggestions) &&
            (identical(other.isLoadingNameSearch, isLoadingNameSearch) ||
                other.isLoadingNameSearch == isLoadingNameSearch) &&
            (identical(other.nameSearchError, nameSearchError) ||
                other.nameSearchError == nameSearchError) &&
            const DeepCollectionEquality()
                .equals(other.selectedActivities, selectedActivities) &&
            const DeepCollectionEquality()
                .equals(other.selectedKeywords, selectedKeywords) &&
            (identical(other.selectedDistanceFilter, selectedDistanceFilter) ||
                other.selectedDistanceFilter == selectedDistanceFilter) &&
            (identical(other.appliedMinAge, appliedMinAge) ||
                other.appliedMinAge == appliedMinAge) &&
            (identical(other.appliedMaxAge, appliedMaxAge) ||
                other.appliedMaxAge == appliedMaxAge) &&
            (identical(other.appliedGender, appliedGender) ||
                other.appliedGender == appliedGender) &&
            const DeepCollectionEquality()
                .equals(other.interestSearchResults, interestSearchResults) &&
            (identical(
                    other.isLoadingInterestSearch, isLoadingInterestSearch) ||
                other.isLoadingInterestSearch == isLoadingInterestSearch) &&
            (identical(other.interestSearchError, interestSearchError) ||
                other.interestSearchError == interestSearchError) &&
            (identical(other.currentUserLocation, currentUserLocation) ||
                other.currentUserLocation == currentUserLocation) &&
            (identical(other.currentUserCountry, currentUserCountry) ||
                other.currentUserCountry == currentUserCountry));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      userQuery,
      const DeepCollectionEquality().hash(userSuggestions),
      isLoadingNameSearch,
      nameSearchError,
      const DeepCollectionEquality().hash(selectedActivities),
      const DeepCollectionEquality().hash(selectedKeywords),
      selectedDistanceFilter,
      appliedMinAge,
      appliedMaxAge,
      appliedGender,
      const DeepCollectionEquality().hash(interestSearchResults),
      isLoadingInterestSearch,
      interestSearchError,
      currentUserLocation,
      currentUserCountry);

  @override
  String toString() {
    return 'SearchState(userQuery: $userQuery, userSuggestions: $userSuggestions, isLoadingNameSearch: $isLoadingNameSearch, nameSearchError: $nameSearchError, selectedActivities: $selectedActivities, selectedKeywords: $selectedKeywords, selectedDistanceFilter: $selectedDistanceFilter, appliedMinAge: $appliedMinAge, appliedMaxAge: $appliedMaxAge, appliedGender: $appliedGender, interestSearchResults: $interestSearchResults, isLoadingInterestSearch: $isLoadingInterestSearch, interestSearchError: $interestSearchError, currentUserLocation: $currentUserLocation, currentUserCountry: $currentUserCountry)';
  }
}

/// @nodoc
abstract mixin class $SearchStateCopyWith<$Res> {
  factory $SearchStateCopyWith(
          SearchState value, $Res Function(SearchState) _then) =
      _$SearchStateCopyWithImpl;
  @useResult
  $Res call(
      {String userQuery,
      List<User> userSuggestions,
      bool isLoadingNameSearch,
      String? nameSearchError,
      List<String> selectedActivities,
      List<String> selectedKeywords,
      String selectedDistanceFilter,
      int? appliedMinAge,
      int? appliedMaxAge,
      String? appliedGender,
      List<User> interestSearchResults,
      bool isLoadingInterestSearch,
      String? interestSearchError,
      GeoPoint? currentUserLocation,
      String? currentUserCountry});
}

/// @nodoc
class _$SearchStateCopyWithImpl<$Res> implements $SearchStateCopyWith<$Res> {
  _$SearchStateCopyWithImpl(this._self, this._then);

  final SearchState _self;
  final $Res Function(SearchState) _then;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userQuery = null,
    Object? userSuggestions = null,
    Object? isLoadingNameSearch = null,
    Object? nameSearchError = freezed,
    Object? selectedActivities = null,
    Object? selectedKeywords = null,
    Object? selectedDistanceFilter = null,
    Object? appliedMinAge = freezed,
    Object? appliedMaxAge = freezed,
    Object? appliedGender = freezed,
    Object? interestSearchResults = null,
    Object? isLoadingInterestSearch = null,
    Object? interestSearchError = freezed,
    Object? currentUserLocation = freezed,
    Object? currentUserCountry = freezed,
  }) {
    return _then(_self.copyWith(
      userQuery: null == userQuery
          ? _self.userQuery
          : userQuery // ignore: cast_nullable_to_non_nullable
              as String,
      userSuggestions: null == userSuggestions
          ? _self.userSuggestions
          : userSuggestions // ignore: cast_nullable_to_non_nullable
              as List<User>,
      isLoadingNameSearch: null == isLoadingNameSearch
          ? _self.isLoadingNameSearch
          : isLoadingNameSearch // ignore: cast_nullable_to_non_nullable
              as bool,
      nameSearchError: freezed == nameSearchError
          ? _self.nameSearchError
          : nameSearchError // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedActivities: null == selectedActivities
          ? _self.selectedActivities
          : selectedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedKeywords: null == selectedKeywords
          ? _self.selectedKeywords
          : selectedKeywords // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedDistanceFilter: null == selectedDistanceFilter
          ? _self.selectedDistanceFilter
          : selectedDistanceFilter // ignore: cast_nullable_to_non_nullable
              as String,
      appliedMinAge: freezed == appliedMinAge
          ? _self.appliedMinAge
          : appliedMinAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedMaxAge: freezed == appliedMaxAge
          ? _self.appliedMaxAge
          : appliedMaxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedGender: freezed == appliedGender
          ? _self.appliedGender
          : appliedGender // ignore: cast_nullable_to_non_nullable
              as String?,
      interestSearchResults: null == interestSearchResults
          ? _self.interestSearchResults
          : interestSearchResults // ignore: cast_nullable_to_non_nullable
              as List<User>,
      isLoadingInterestSearch: null == isLoadingInterestSearch
          ? _self.isLoadingInterestSearch
          : isLoadingInterestSearch // ignore: cast_nullable_to_non_nullable
              as bool,
      interestSearchError: freezed == interestSearchError
          ? _self.interestSearchError
          : interestSearchError // ignore: cast_nullable_to_non_nullable
              as String?,
      currentUserLocation: freezed == currentUserLocation
          ? _self.currentUserLocation
          : currentUserLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      currentUserCountry: freezed == currentUserCountry
          ? _self.currentUserCountry
          : currentUserCountry // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _SearchState implements SearchState {
  const _SearchState(
      {this.userQuery = '',
      final List<User> userSuggestions = const [],
      this.isLoadingNameSearch = false,
      this.nameSearchError,
      final List<String> selectedActivities = const [],
      final List<String> selectedKeywords = const [],
      this.selectedDistanceFilter = defaultDistanceFilter,
      this.appliedMinAge,
      this.appliedMaxAge,
      this.appliedGender,
      final List<User> interestSearchResults = const [],
      this.isLoadingInterestSearch = false,
      this.interestSearchError,
      this.currentUserLocation,
      this.currentUserCountry})
      : _userSuggestions = userSuggestions,
        _selectedActivities = selectedActivities,
        _selectedKeywords = selectedKeywords,
        _interestSearchResults = interestSearchResults;

// Name/Username Search State
  @override
  @JsonKey()
  final String userQuery;
  final List<User> _userSuggestions;
  @override
  @JsonKey()
  List<User> get userSuggestions {
    if (_userSuggestions is EqualUnmodifiableListView) return _userSuggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userSuggestions);
  }

  @override
  @JsonKey()
  final bool isLoadingNameSearch;
  @override
  final String? nameSearchError;
// Interest Search Criteria
  final List<String> _selectedActivities;
// Interest Search Criteria
  @override
  @JsonKey()
  List<String> get selectedActivities {
    if (_selectedActivities is EqualUnmodifiableListView)
      return _selectedActivities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedActivities);
  }

// List of activity IDs
  final List<String> _selectedKeywords;
// List of activity IDs
  @override
  @JsonKey()
  List<String> get selectedKeywords {
    if (_selectedKeywords is EqualUnmodifiableListView)
      return _selectedKeywords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedKeywords);
  }

  @override
  @JsonKey()
  final String selectedDistanceFilter;
// 'local', 'regional', 'national', 'global'
  @override
  final int? appliedMinAge;
// Filters applied via FilterPage
  @override
  final int? appliedMaxAge;
  @override
  final String? appliedGender;
// Interest Search Results State
  final List<User> _interestSearchResults;
// Interest Search Results State
  @override
  @JsonKey()
  List<User> get interestSearchResults {
    if (_interestSearchResults is EqualUnmodifiableListView)
      return _interestSearchResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_interestSearchResults);
  }

  @override
  @JsonKey()
  final bool isLoadingInterestSearch;
  @override
  final String? interestSearchError;
// Data needed for filtering based on current user
  @override
  final GeoPoint? currentUserLocation;
  @override
  final String? currentUserCountry;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchStateCopyWith<_SearchState> get copyWith =>
      __$SearchStateCopyWithImpl<_SearchState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchState &&
            (identical(other.userQuery, userQuery) ||
                other.userQuery == userQuery) &&
            const DeepCollectionEquality()
                .equals(other._userSuggestions, _userSuggestions) &&
            (identical(other.isLoadingNameSearch, isLoadingNameSearch) ||
                other.isLoadingNameSearch == isLoadingNameSearch) &&
            (identical(other.nameSearchError, nameSearchError) ||
                other.nameSearchError == nameSearchError) &&
            const DeepCollectionEquality()
                .equals(other._selectedActivities, _selectedActivities) &&
            const DeepCollectionEquality()
                .equals(other._selectedKeywords, _selectedKeywords) &&
            (identical(other.selectedDistanceFilter, selectedDistanceFilter) ||
                other.selectedDistanceFilter == selectedDistanceFilter) &&
            (identical(other.appliedMinAge, appliedMinAge) ||
                other.appliedMinAge == appliedMinAge) &&
            (identical(other.appliedMaxAge, appliedMaxAge) ||
                other.appliedMaxAge == appliedMaxAge) &&
            (identical(other.appliedGender, appliedGender) ||
                other.appliedGender == appliedGender) &&
            const DeepCollectionEquality()
                .equals(other._interestSearchResults, _interestSearchResults) &&
            (identical(
                    other.isLoadingInterestSearch, isLoadingInterestSearch) ||
                other.isLoadingInterestSearch == isLoadingInterestSearch) &&
            (identical(other.interestSearchError, interestSearchError) ||
                other.interestSearchError == interestSearchError) &&
            (identical(other.currentUserLocation, currentUserLocation) ||
                other.currentUserLocation == currentUserLocation) &&
            (identical(other.currentUserCountry, currentUserCountry) ||
                other.currentUserCountry == currentUserCountry));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      userQuery,
      const DeepCollectionEquality().hash(_userSuggestions),
      isLoadingNameSearch,
      nameSearchError,
      const DeepCollectionEquality().hash(_selectedActivities),
      const DeepCollectionEquality().hash(_selectedKeywords),
      selectedDistanceFilter,
      appliedMinAge,
      appliedMaxAge,
      appliedGender,
      const DeepCollectionEquality().hash(_interestSearchResults),
      isLoadingInterestSearch,
      interestSearchError,
      currentUserLocation,
      currentUserCountry);

  @override
  String toString() {
    return 'SearchState(userQuery: $userQuery, userSuggestions: $userSuggestions, isLoadingNameSearch: $isLoadingNameSearch, nameSearchError: $nameSearchError, selectedActivities: $selectedActivities, selectedKeywords: $selectedKeywords, selectedDistanceFilter: $selectedDistanceFilter, appliedMinAge: $appliedMinAge, appliedMaxAge: $appliedMaxAge, appliedGender: $appliedGender, interestSearchResults: $interestSearchResults, isLoadingInterestSearch: $isLoadingInterestSearch, interestSearchError: $interestSearchError, currentUserLocation: $currentUserLocation, currentUserCountry: $currentUserCountry)';
  }
}

/// @nodoc
abstract mixin class _$SearchStateCopyWith<$Res>
    implements $SearchStateCopyWith<$Res> {
  factory _$SearchStateCopyWith(
          _SearchState value, $Res Function(_SearchState) _then) =
      __$SearchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String userQuery,
      List<User> userSuggestions,
      bool isLoadingNameSearch,
      String? nameSearchError,
      List<String> selectedActivities,
      List<String> selectedKeywords,
      String selectedDistanceFilter,
      int? appliedMinAge,
      int? appliedMaxAge,
      String? appliedGender,
      List<User> interestSearchResults,
      bool isLoadingInterestSearch,
      String? interestSearchError,
      GeoPoint? currentUserLocation,
      String? currentUserCountry});
}

/// @nodoc
class __$SearchStateCopyWithImpl<$Res> implements _$SearchStateCopyWith<$Res> {
  __$SearchStateCopyWithImpl(this._self, this._then);

  final _SearchState _self;
  final $Res Function(_SearchState) _then;

  /// Create a copy of SearchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userQuery = null,
    Object? userSuggestions = null,
    Object? isLoadingNameSearch = null,
    Object? nameSearchError = freezed,
    Object? selectedActivities = null,
    Object? selectedKeywords = null,
    Object? selectedDistanceFilter = null,
    Object? appliedMinAge = freezed,
    Object? appliedMaxAge = freezed,
    Object? appliedGender = freezed,
    Object? interestSearchResults = null,
    Object? isLoadingInterestSearch = null,
    Object? interestSearchError = freezed,
    Object? currentUserLocation = freezed,
    Object? currentUserCountry = freezed,
  }) {
    return _then(_SearchState(
      userQuery: null == userQuery
          ? _self.userQuery
          : userQuery // ignore: cast_nullable_to_non_nullable
              as String,
      userSuggestions: null == userSuggestions
          ? _self._userSuggestions
          : userSuggestions // ignore: cast_nullable_to_non_nullable
              as List<User>,
      isLoadingNameSearch: null == isLoadingNameSearch
          ? _self.isLoadingNameSearch
          : isLoadingNameSearch // ignore: cast_nullable_to_non_nullable
              as bool,
      nameSearchError: freezed == nameSearchError
          ? _self.nameSearchError
          : nameSearchError // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedActivities: null == selectedActivities
          ? _self._selectedActivities
          : selectedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedKeywords: null == selectedKeywords
          ? _self._selectedKeywords
          : selectedKeywords // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedDistanceFilter: null == selectedDistanceFilter
          ? _self.selectedDistanceFilter
          : selectedDistanceFilter // ignore: cast_nullable_to_non_nullable
              as String,
      appliedMinAge: freezed == appliedMinAge
          ? _self.appliedMinAge
          : appliedMinAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedMaxAge: freezed == appliedMaxAge
          ? _self.appliedMaxAge
          : appliedMaxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      appliedGender: freezed == appliedGender
          ? _self.appliedGender
          : appliedGender // ignore: cast_nullable_to_non_nullable
              as String?,
      interestSearchResults: null == interestSearchResults
          ? _self._interestSearchResults
          : interestSearchResults // ignore: cast_nullable_to_non_nullable
              as List<User>,
      isLoadingInterestSearch: null == isLoadingInterestSearch
          ? _self.isLoadingInterestSearch
          : isLoadingInterestSearch // ignore: cast_nullable_to_non_nullable
              as bool,
      interestSearchError: freezed == interestSearchError
          ? _self.interestSearchError
          : interestSearchError // ignore: cast_nullable_to_non_nullable
              as String?,
      currentUserLocation: freezed == currentUserLocation
          ? _self.currentUserLocation
          : currentUserLocation // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      currentUserCountry: freezed == currentUserCountry
          ? _self.currentUserCountry
          : currentUserCountry // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
