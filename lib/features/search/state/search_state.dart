// lib/features/search/state/search_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/core/models/user.dart';

part 'search_state.freezed.dart';

// Default distance filter value
const String defaultDistanceFilter = 'local';

@freezed
abstract class SearchState with _$SearchState {
  const factory SearchState({
    // Name/Username Search State
    @Default('') String userQuery,
    @Default([]) List<User> userSuggestions,
    @Default(false) bool isLoadingNameSearch,
    String? nameSearchError,

    // Interest Search Criteria
    @Default([]) List<String> selectedActivities, // List of activity IDs
    @Default([]) List<String> selectedKeywords,
    @Default(defaultDistanceFilter)
    String selectedDistanceFilter, // 'local', 'regional', 'national', 'global'
    int? appliedMinAge, // Filters applied via FilterPage
    int? appliedMaxAge,
    String? appliedGender,

    // Interest Search Results State
    @Default([]) List<User> interestSearchResults,
    @Default(false) bool isLoadingInterestSearch,
    String? interestSearchError,

    // Data needed for filtering based on current user
    GeoPoint? currentUserLocation,
    String? currentUserCountry,
  }) = _SearchState;
}
