// lib/features/search/providers/search_providers.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/features/search/viewmodels/search_viewmodel.dart';

// Provider generated by @riverpod on SearchViewModel class
// AutoDisposeAsyncNotifierProvider<SearchViewModel, SearchState> searchViewModelProvider
// Add other search-specific providers here if needed later.
