// lib/features/onboarding/helpers/navigation_helper.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';

/// A mixin that provides navigation helpers for the onboarding flow.
mixin OnboardingNavigationHelper {
  /// The page controller for the onboarding flow
  PageController get pageController;
  
  /// The state notifier for the onboarding state
  OnboardingViewModel get viewModel;
  
  /// The ref object for accessing providers
  WidgetRef get ref;
  
  /// The current edit context
  EditContext get editContext;
  
  /// Update the edit context
  void updateEditContext(EditContext newContext);
  
  /// Check if the widget is still mounted
  bool get mounted;

  /// Navigate to a specific page with animation
  Future<void> animateToPage(int pageIndex) async {
    // Update the ViewModel's current page
    viewModel.setCurrentPage(pageIndex);
    
    // Add a small delay to allow state updates to propagate
    if (editContext.isEditing && pageIndex == editContext.sourcePageIndex) {
      // Only add delay when returning to source page from edit mode
      await Future.delayed(const Duration(milliseconds: 50));
    }

    if (mounted) {
      pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      
      // If we are navigating TO the source page, reset the editing context
      if (pageIndex == editContext.sourcePageIndex && editContext.isEditing) {
        updateEditContext(editContext.reset());
      }
    }
  }

  /// Start editing a field from the summary page
  Future<void> startEditing({
    required int targetPageIndex,
    required int sourcePageIndex,
    required Map<String, dynamic> originalValues,
  }) async {
    // Create a new edit context
    final newContext = EditContext.forField(
      targetPageIndex: targetPageIndex,
      sourcePageIndex: sourcePageIndex,
      originalValues: originalValues,
    );
    
    // Update the edit context
    updateEditContext(newContext);
    
    // Navigate to the target page
    await animateToPage(targetPageIndex);
  }

  /// Apply edits and return to the source page
  Future<void> applyEdits({
    required Map<String, dynamic> Function() getUpdatedValues,
  }) async {
    // Get the updated values
    final updatedValues = getUpdatedValues();
    
    // Apply the updates to the ViewModel
    _applyUpdatesToViewModel(updatedValues);
    
    // Wait for state to update
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Navigate back to the source page
    if (mounted) {
      await animateToPage(editContext.sourcePageIndex);
    }
  }

  /// Cancel edits and return to the source page
  Future<void> cancelEdits() async {
    // Revert changes by applying original values
    _applyUpdatesToViewModel(editContext.originalValues);
    
    // Navigate back to the source page
    await animateToPage(editContext.sourcePageIndex);
  }

  /// Apply updates to the ViewModel based on the field keys
  void _applyUpdatesToViewModel(Map<String, dynamic> updates) {
    updates.forEach((key, value) {
      switch (key) {
        case 'fullName':
          viewModel.updateFullName(value);
          break;
        case 'username':
          viewModel.updateUsername(value);
          break;
        case 'birthdate':
          viewModel.updateBirthdate(value);
          break;
        case 'gender':
          viewModel.updateGender(value);
          break;
        case 'city':
          viewModel.updateCity(value);
          break;
        case 'profileImage':
          viewModel.updateProfileImage(value);
          break;
        case 'defaultAvatar':
          viewModel.updateDefaultAvatar(value);
          break;
        case 'generatedAvatarColor':
          viewModel.updateGeneratedAvatarColor(value);
          break;
        case 'avatarType':
          // Handle avatar type based on the value
          if (value == 'generated' && updates.containsKey('generatedAvatarColor')) {
            viewModel.selectGeneratedAvatar(updates['generatedAvatarColor']);
          }
          break;
        case 'sharedActivities':
          viewModel.updateSharedActivities(value);
          break;
        case 'myInterests':
          viewModel.updateMyInterests(value);
          break;
        // Add more cases as needed
      }
    });
  }
}
