// lib/features/onboarding/widgets/onboarding_page_base.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';

/// A base class for onboarding pages.
/// This provides common functionality for all onboarding pages.
abstract class OnboardingPageBase extends ConsumerWidget {
  /// The current onboarding state
  final OnboardingState state;
  
  /// The onboarding view model
  final OnboardingViewModel viewModel;
  
  /// The edit context
  final EditContext editContext;
  
  /// Callback for navigating to the next page
  final Future<void> Function(int pageIndex) onNavigate;
  
  /// Callback for applying edits
  final Future<void> Function({required Map<String, dynamic> Function() getUpdatedValues}) onApplyEdits;
  
  /// Callback for canceling edits
  final Future<void> Function() onCancelEdits;

  const OnboardingPageBase({
    super.key,
    required this.state,
    required this.viewModel,
    required this.editContext,
    required this.onNavigate,
    required this.onApplyEdits,
    required this.onCancelEdits,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return buildPageContent(context, ref);
  }

  /// Build the content of the page.
  /// This method should be implemented by subclasses.
  Widget buildPageContent(BuildContext context, WidgetRef ref);

  /// Helper method for building standard navigation buttons
  Widget buildNavigationButtons({
    required BuildContext context,
    required int currentPage,
    required int previousPage,
    required int nextPage,
    required bool canProceedToNext,
    String backButtonText = 'Back',
    String nextButtonText = 'Next',
    VoidCallback? onBackPressed,
    VoidCallback? onNextPressed,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Back button
        TextButton(
          onPressed: onBackPressed ?? () {
            if (editContext.isEditing) {
              onCancelEdits();
            } else {
              viewModel.previousPage();
              onNavigate(previousPage);
            }
          },
          child: Text(editContext.isEditing ? 'Cancel' : backButtonText),
        ),
        
        // Next button
        ElevatedButton(
          onPressed: canProceedToNext
              ? (onNextPressed ?? () {
                  if (editContext.isEditing) {
                    // Apply edits and return to summary
                    onApplyEdits(getUpdatedValues: () => collectFieldValues(context));
                  } else {
                    // Standard next page navigation
                    viewModel.nextPage();
                    onNavigate(nextPage);
                  }
                })
              : null, // Disable if cannot proceed
          child: Text(editContext.isEditing ? 'Update' : nextButtonText),
        ),
      ],
    );
  }

  /// Collect field values for applying edits.
  /// This method should be implemented by subclasses.
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    // Default implementation returns an empty map
    return {};
  }
}
