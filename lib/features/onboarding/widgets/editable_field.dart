// lib/features/onboarding/widgets/editable_field.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:collection/collection.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/models/shared_activity.dart';

/// A reusable widget for displaying an editable field in the summary page.
class EditableField extends StatelessWidget {
  /// The title of the field
  final String title;

  /// The current value to display
  final String value;

  /// Optional tooltip for the edit button
  final String? tooltip;

  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  const EditableField({
    super.key,
    required this.title,
    required this.value,
    this.tooltip,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: Text(value),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        tooltip: tooltip ?? 'Edit $title',
        onPressed: onEdit,
      ),
    );
  }
}

/// A widget for displaying an editable list field in the summary page.
class EditableList<PERSON>ield extends StatelessWidget {
  /// The title of the field
  final String title;

  /// The list of values to display
  final List<String>? values;

  /// Optional tooltip for the edit button
  final String? tooltip;

  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  /// Text to display when the list is empty
  final String emptyText;

  const EditableListField({
    super.key,
    required this.title,
    required this.values,
    this.tooltip,
    required this.onEdit,
    this.emptyText = 'Not yet added',
  });

  @override
  Widget build(BuildContext context) {
    final displayText =
        values != null && values!.isNotEmpty ? values!.join(", ") : emptyText;

    return ListTile(
      title: Text(title),
      subtitle: Text(displayText),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        tooltip: tooltip ?? 'Edit $title',
        onPressed: onEdit,
      ),
    );
  }
}

/// A widget for displaying an editable profile picture in the summary page.
class EditableProfilePicture extends StatelessWidget {
  /// The widget to display as the profile picture
  final Widget profilePictureWidget;

  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  const EditableProfilePicture({
    super.key,
    required this.profilePictureWidget,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: InkWell(
        onTap: onEdit,
        child: Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Profile picture widget
              profilePictureWidget,

              // Edit icon overlay
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.edit, color: Colors.white, size: 18),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

/// A widget for displaying shared activities as chips with ID-to-name resolution
class EditableSharedActivitiesField extends ConsumerWidget {
  /// The title of the field
  final String title;

  /// The list of activity IDs to display
  final List<String>? activityIds;

  /// Optional tooltip for the edit button
  final String? tooltip;

  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  /// Text to display when the list is empty
  final String emptyText;

  const EditableSharedActivitiesField({
    super.key,
    required this.title,
    required this.activityIds,
    this.tooltip,
    required this.onEdit,
    this.emptyText = 'Not yet added',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allActivitiesAsync = ref.watch(sharedActivitiesProvider);

    return allActivitiesAsync.when(
      data: (allActivities) => _buildContent(context, allActivities),
      loading: () => _buildLoadingContent(context),
      error: (error, stack) => _buildErrorContent(context, error),
    );
  }

  Widget _buildContent(
      BuildContext context, List<SharedActivity> allActivities) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Edit Button Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Chips Display
          _buildChips(context, allActivities),
        ],
      ),
    );
  }

  Widget _buildChips(BuildContext context, List<SharedActivity> allActivities) {
    if (activityIds == null || activityIds!.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Text(
          emptyText,
          style: TextStyle(color: Colors.grey[600]),
        ),
      );
    }

    // Convert IDs to names and create chips
    final activityNames = activityIds!.map((id) {
      final activity = allActivities.firstWhereOrNull(
        (act) => act.activityId == id,
      );
      return activity?.activityName ?? 'Unknown Activity';
    }).toList();

    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: activityNames.map((name) {
        return Chip(
          label: Text(name),
          labelStyle: const TextStyle(fontSize: 12),
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
          // No delete or tap functionality - just display
        );
      }).toList(),
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
              ),
            ],
          ),
          const SizedBox(height: 8),
          const CircularProgressIndicator(),
        ],
      ),
    );
  }

  Widget _buildErrorContent(BuildContext context, Object error) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                tooltip: tooltip ?? 'Edit $title',
                onPressed: onEdit,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Error loading activities: $error',
            style: TextStyle(color: Colors.red[600]),
          ),
        ],
      ),
    );
  }
}
