// lib/features/onboarding/widgets/editable_field.dart
import 'package:flutter/material.dart';

/// A reusable widget for displaying an editable field in the summary page.
class EditableField extends StatelessWidget {
  /// The title of the field
  final String title;
  
  /// The current value to display
  final String value;
  
  /// Optional tooltip for the edit button
  final String? tooltip;
  
  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  const EditableField({
    Key? key,
    required this.title,
    required this.value,
    this.tooltip,
    required this.onEdit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: Text(value),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        tooltip: tooltip ?? 'Edit $title',
        onPressed: onEdit,
      ),
    );
  }
}

/// A widget for displaying an editable list field in the summary page.
class EditableListField extends StatelessWidget {
  /// The title of the field
  final String title;
  
  /// The list of values to display
  final List<String>? values;
  
  /// Optional tooltip for the edit button
  final String? tooltip;
  
  /// Callback when the edit button is pressed
  final VoidCallback onEdit;
  
  /// Text to display when the list is empty
  final String emptyText;

  const EditableListField({
    Key? key,
    required this.title,
    required this.values,
    this.tooltip,
    required this.onEdit,
    this.emptyText = 'Not yet added',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final displayText = values != null && values!.isNotEmpty 
        ? values!.join(", ") 
        : emptyText;
        
    return ListTile(
      title: Text(title),
      subtitle: Text(displayText),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        tooltip: tooltip ?? 'Edit $title',
        onPressed: onEdit,
      ),
    );
  }
}

/// A widget for displaying an editable profile picture in the summary page.
class EditableProfilePicture extends StatelessWidget {
  /// The widget to display as the profile picture
  final Widget profilePictureWidget;
  
  /// Callback when the edit button is pressed
  final VoidCallback onEdit;

  const EditableProfilePicture({
    Key? key,
    required this.profilePictureWidget,
    required this.onEdit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: InkWell(
        onTap: onEdit,
        child: Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Profile picture widget
              profilePictureWidget,
              
              // Edit icon overlay
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.edit, color: Colors.white, size: 18),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
