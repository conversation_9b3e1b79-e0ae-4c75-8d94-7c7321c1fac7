# Onboarding Feature Refactoring

This document explains the refactoring approach for the Onboarding feature in the Watermelon app.

## Problem

The original `onboarding_screen.dart` file had grown to over 1200 lines of code, making it difficult to maintain and extend. The code had several issues:

1. **Monolithic Structure**: All UI and logic was in a single file
2. **Complex State Management**: Managing edit mode and original values was becoming unwieldy
3. **Repetitive Code**: Similar patterns were repeated for each page
4. **Timing Issues**: State updates weren't always reflected in the UI due to async timing issues

## Solution

The refactoring approach breaks down the code into smaller, more focused components:

### 1. Directory Structure

```
lib/features/onboarding/
├── constants/
│   └── page_indices.dart       # Constants for page indices
├── helpers/
│   └── navigation_helper.dart  # Navigation helper mixin
├── models/
│   └── edit_context.dart       # Model for edit context
├── pages/
│   ├── name_username_page.dart # Individual page implementations
│   ├── summary_page.dart
│   └── ...
├── screens/
│   └── onboarding_screen.dart  # Main screen (refactored)
├── viewmodels/
│   └── onboarding_viewmodel.dart # ViewModel (unchanged)
└── widgets/
    ├── editable_field.dart     # Reusable UI components
    └── onboarding_page_base.dart # Base class for pages
```

### 2. Key Components

#### EditContext

A dedicated class to manage edit state, replacing multiple boolean flags and original value variables:

```dart
class EditContext {
  final bool isEditing;
  final Map<String, dynamic> originalValues;
  final int sourcePageIndex;
  final int targetPageIndex;
  // ...
}
```

#### OnboardingNavigationHelper

A mixin that provides navigation helpers for the onboarding flow:

```dart
mixin OnboardingNavigationHelper {
  Future<void> animateToPage(int pageIndex) async { /* ... */ }
  Future<void> startEditing({ /* ... */ }) async { /* ... */ }
  Future<void> applyEdits({ /* ... */ }) async { /* ... */ }
  Future<void> cancelEdits() async { /* ... */ }
}
```

#### OnboardingPageBase

A base class for all onboarding pages:

```dart
abstract class OnboardingPageBase extends ConsumerWidget {
  // Common properties and methods
  Widget buildNavigationButtons({ /* ... */ }) { /* ... */ }
  Map<String, dynamic> collectFieldValues(BuildContext context) { /* ... */ }
}
```

#### Reusable UI Components

Widgets like `EditableField` and `EditableListField` to reduce repetitive UI code:

```dart
class EditableField extends StatelessWidget {
  final String title;
  final String value;
  final VoidCallback onEdit;
  // ...
}
```

### 3. Implementation Strategy

1. **Incremental Refactoring**: Start with the most complex pages (NameUsername and Summary)
2. **Maintain Compatibility**: Keep the original file working while refactoring
3. **Test Each Component**: Ensure each refactored component works correctly before moving on

## Benefits

1. **Improved Maintainability**: Smaller, focused files are easier to understand and modify
2. **Better Organization**: Clear separation of concerns between UI, state, and navigation
3. **Reduced Duplication**: Reusable components eliminate repetitive code
4. **Easier Extension**: Adding new pages or features is simpler with the modular approach
5. **More Robust**: Structured approach to state management reduces timing issues

## Migration Plan

1. Implement the refactored components
2. Create a new `OnboardingScreenRefactored` class
3. Test the refactored implementation
4. Replace the original implementation with the refactored one

## Usage Example

```dart
// Creating a page
NameUsernamePage(
  state: value,
  viewModel: viewModel,
  editContext: editContext,
  onNavigate: animateToPage,
  onApplyEdits: applyEdits,
  onCancelEdits: cancelEdits,
  formKey: _nameUsernameFormKey,
  fullNameController: _fullNameController,
  usernameController: _usernameController,
)

// Starting edit mode
onStartEditing(
  targetPageIndex: nameUsernamePageIndex,
  sourcePageIndex: state.currentPage,
  originalValues: {
    'fullName': state.fullName,
    'username': state.username,
  },
)

// Applying edits
await onApplyEdits(
  getUpdatedValues: () => {
    'fullName': fullNameController.text,
    'username': usernameController.text,
  }
)
```
