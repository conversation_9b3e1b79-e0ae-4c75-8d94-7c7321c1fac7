// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'onboarding_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$onboardingViewModelHash() =>
    r'63984e7f64f0576cce8bc536a5fdded31d8d2fd0';

/// See also [OnboardingViewModel].
@ProviderFor(OnboardingViewModel)
final onboardingViewModelProvider = AutoDisposeAsyncNotifierProvider<
    OnboardingViewModel, OnboardingState>.internal(
  OnboardingViewModel.new,
  name: r'onboardingViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onboardingViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnboardingViewModel = AutoDisposeAsyncNotifier<OnboardingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
