// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'onboarding_viewmodel.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$onboardingViewModelHash() =>
    r'3269aae73ce209baa81827d0de2fa9bf8a2f59c4';

/// See also [OnboardingViewModel].
@ProviderFor(OnboardingViewModel)
final onboardingViewModelProvider = AutoDisposeAsyncNotifierProvider<
    OnboardingViewModel, OnboardingState>.internal(
  OnboardingViewModel.new,
  name: r'onboardingViewModelProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onboardingViewModelHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnboardingViewModel = AutoDisposeAsyncNotifier<OnboardingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
