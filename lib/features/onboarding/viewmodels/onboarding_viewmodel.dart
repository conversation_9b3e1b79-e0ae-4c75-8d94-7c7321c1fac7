// lib/features/onboarding/viewmodels/onboarding_viewmodel.dart
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/services/auth_repository.dart';
import 'package:watermelon_draft/core/services/user_repository.dart';
import 'package:watermelon_draft/core/services/location_service.dart';
import 'package:watermelon_draft/core/models/user.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:fpdart/fpdart.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:watermelon_draft/features/profile/providers/profile_providers.dart';

part 'onboarding_viewmodel.freezed.dart';
part 'onboarding_viewmodel.g.dart';

@riverpod
class OnboardingViewModel extends _$OnboardingViewModel {
  late final AuthRepository _authRepository;
  late final UserRepository _userRepository;
  late final LocationService _locationService;
  late final SharedPreferences _prefs;
  Timer? _usernameDebounce; // Timer for debouncing username checks

  @override
  FutureOr<OnboardingState> build() async {
    _authRepository = ref.watch(authRepositoryProvider);
    _userRepository = ref.watch(userRepositoryProvider);
    _locationService =
        ref.watch(locationServiceProvider); // Get LocationService instance
    _prefs = await ref.watch(sharedPreferencesProvider.future);

    ref.onDispose(() {
      // Dispose timer
      _usernameDebounce?.cancel();
    });

    return OnboardingState(); // Initial state
  }

  // Helper method to safely update the state when it's AsyncData
  void _updateData(
      OnboardingState Function(OnboardingState currentState) updateFn) {
    // --- option 1 ---
    // Check if the state is AsyncData and holds a value
    // This is a more manual way to check the state type
    // if (state is AsyncData<OnboardingState>) {
    //   // Check if the state holds data
    //   state = AsyncData(updateFn(state.value!)); // Apply the update function
    // }

    // --- option 2 ---
    // Use whenData to safely access the current value
    state.whenData((currentValue) {
      state = AsyncData(updateFn(currentValue));
    });
    // If state is AsyncLoading or AsyncError, we generally don't want to
    // apply synchronous updates. You could potentially log an error here
    // or handle it differently if needed, but often ignoring it is fine.
  }

  // -------- Methods to update the state --------
  void updateFullName(String fullName) {
    // --- Check without using helper method ---
    // if (state is AsyncData<OnboardingState>) {
    //   state = AsyncValue.data(state.value!.copyWith(fullName: fullName));
    // }
    _updateData((currentState) => currentState.copyWith(fullName: fullName));
  }

  void updateUsername(String username) {
    print("VM: updateUsername called with: $username");
    // Update the state immediately with the new username text
    _updateData((currentState) => currentState.copyWith(
        username: username,
        usernameError: null, // Clear previous error on new input
        isUsernameChecking: username.length >= 3 &&
            !username.contains(
                ' ') // Start checking immediately if basic format is valid
        ));

    // Debounce the availability check
    _usernameDebounce?.cancel(); // Cancel previous timer if any
    if (username.length >= 3 && !username.contains(' ')) {
      // Only check if basic validation passes
      _usernameDebounce = Timer(const Duration(milliseconds: 500), () {
        print("VM: Debounce timer fired for: $username");
        checkUsernameAvailability(username);
      });
    } else {
      // If basic validation fails, update state immediately
      _updateData(
          (currentState) => currentState.copyWith(isUsernameChecking: false));
    }
  }

  Future<void> checkUsernameAvailability(String username) async {
    print("VM: checkUsernameAvailability called for: $username");
    // Ensure we are still checking the *current* username in the state
    final currentUsernameInState = state.value?.username;
    if (username != currentUsernameInState) {
      // The username changed again before the debounce timer fired, do nothing.
      return;
    }

    // No need to set loading state manually here, update is fine
    _updateData(
        (currentState) => currentState.copyWith(isUsernameChecking: true));
    print("VM: Calling repository isUsernameTaken for: $username");
    final result = await _userRepository.isUsernameTaken(username);
    print("VM: Repository result for $username: $result");
    // Check if state is still mounted / relevant before updating state
    // (Although less critical inside AsyncNotifier compared to StatefulWidget)
    if (username == state.value?.username) {
      // Check again if username hasn't changed
      result.fold(
        (failure) {
          // Handle error fetching availability (e.g., network error)
          _updateData((currentState) => currentState.copyWith(
                usernameError: 'Could not check username. Try again.',
                isUsernameChecking: false,
              ));
        },
        (isTaken) {
          // Update state based on availability
          _updateData((currentState) => currentState.copyWith(
                usernameError: isTaken ? 'Username already taken' : null,
                isUsernameChecking: false,
              ));
        },
      );
    }
  }

  void updateBirthdate(DateTime birthdate) {
    _updateData((currentState) => currentState.copyWith(birthdate: birthdate));
  }

  void updateGender(String gender) {
    _updateData((currentState) => currentState.copyWith(gender: gender));
  }

  void updateCity(String city) {
    _updateData((currentState) => currentState.copyWith(city: city));
  }

  Future<void> updateLocationFromPoint(GeoPoint point) async {
    // No need to set loading state here, it's part of the overall state
    final countryResult = await _locationService.getCountryFromLocation(point);

    countryResult.fold((failure) {
      // Log the error, maybe set an error state if needed for UI feedback?
      // For now, just update state without the country if it fails.
      print("Failed to get country from location point: $failure");
      _updateData((currentState) => currentState.copyWith(
          location: point,
          city: null, // Clear city if we only have GPS point
          country: null // Indicate country fetch failed
          ));
    }, (country) {
      // Also try to get city name from these coordinates
      _locationService.getCityFromLocation(point).then((cityResult) {
        cityResult.fold((cityFailure) {
          print("Failed to get city from location point: $cityFailure");
          _updateData((currentState) => currentState.copyWith(
              location: point,
              city: "Unknown Location", // Or null
              country: country));
        }, (city) {
          _updateData((currentState) => currentState.copyWith(
              location: point,
              city: city, // Update city
              country: country // Update country
              ));
        });
      });
    });
  }

  Future<void> updateLocationFromAddress(String address) async {
    // Clear location point if user is typing address
    _updateData((currentState) => currentState.copyWith(location: null));

    // Get Location and Country from the city name
    final locationCountryResult =
        await _locationService.getLocationAndCountryFromCity(address);

    locationCountryResult.fold((failure) {
      print("Failed to get location/country from address: $failure");
      _updateData((currentState) => currentState.copyWith(
          city: address, // Keep the typed city
          location: null,
          country: null // Indicate country fetch failed
          ));
    }, (resultTuple) {
      final location = resultTuple.$1; // GeoPoint?
      final country = resultTuple.$2; // String?
      _updateData((currentState) => currentState.copyWith(
          city: address, // Keep the typed city
          location: location, // Update location if found
          country: country // Update country if found
          ));
    });
  }

  void updateProfileImage(XFile? image) {
    _updateData((currentState) => currentState.copyWith(
        profileImage: image,
        defaultAvatar: null, // Clear default avatar if image is selected
        avatarType: image != null ? 'uploaded' : null,
        generatedAvatarColor: null // Clear generated color
        ));
  }

  void updateDefaultAvatar(String? avatarPath) {
    _updateData((currentState) => currentState.copyWith(
        defaultAvatar: avatarPath,
        profileImage: null, // Clear selected image if default is chosen
        avatarType: avatarPath != null ? 'default' : null,
        generatedAvatarColor: null // Clear generated color
        ));
  }

  void selectGeneratedAvatar(Color color) {
    _updateData((currentState) => currentState.copyWith(
        profileImage: null,
        defaultAvatar: null,
        avatarType: 'generated', // Explicitly set type
        generatedAvatarColor: color // Store the selected color
        ));
  }

  // --- Optional: Method to update just the color if regenerated ---
  // This might be called from _regenerateColor in the picker
  void updateGeneratedAvatarColor(Color color) {
    _updateData((currentState) {
      // Only update color if generated is the *current* type
      if (currentState.avatarType == 'generated') {
        return currentState.copyWith(generatedAvatarColor: color);
      }
      return currentState; // Otherwise, no change
    });
  }

  void updateSharedActivities(List<String> activities) {
    // Renamed parameter
    _updateData(
        (currentState) => currentState.copyWith(sharedActivities: activities));
  }

  void updateMyInterests(List<String> keywords) {
    _updateData((currentState) => currentState.copyWith(myInterests: keywords));
  }

  void nextPage() {
    _updateData((currentState) =>
        currentState.copyWith(currentPage: currentState.currentPage + 1));
  }

  void previousPage() {
    _updateData((currentState) =>
        currentState.copyWith(currentPage: currentState.currentPage - 1));
  }

  void setCurrentPage(int pageIndex) {
    // You might want to add bounds checking here if necessary,
    // e.g., pageIndex >= 0 && pageIndex < totalOnboardingPages
    // For now, assuming pageIndex will be valid from the caller.
    _updateData(
        (currentState) => currentState.copyWith(currentPage: pageIndex));
    print("ViewModel: Current page set to $pageIndex");
  }

  Future<Either<Failure, Unit>> completeOnboarding() async {
    // --- 1. Get current data state and set isSaving = true ---
    // Need to access the value directly to check isSaving before updating.
    final initialData = state.valueOrNull;
    if (initialData == null) {
      // Should not happen if called from UI where state is already data
      return left(AppLogicFailure("Onboarding state is not ready."));
    }
    if (initialData.isSaving) {
      return left(AppLogicFailure("Save already in progress."));
    }
    // Set isSaving to true and clear any previous saveError
    _updateData((currentState) =>
        currentState.copyWith(isSaving: true, saveError: null));

    // --- 2. Perform the save operation ---
    try {
      // --- VALIDATION (Ensure all required fields are filled) ---
      final validationResult = validate();
      if (validationResult.isLeft()) {
        // If validation fails, update state with error and isSaving: false
        final failure =
            validationResult.getLeft().toNullable()!; // Get the Failure object
        _updateData((currentState) =>
            currentState.copyWith(isSaving: false, saveError: failure));
        return left(failure); // Return the error if validation fails
      }

      // --- Get Current User ---
      final currentUserResult = await _authRepository.currentUser();

      return await currentUserResult.fold(
        (failure) async {
          // Made this async to allow state update before return
          // Handle the case where fetching the current user failed
          _updateData((currentState) =>
              currentState.copyWith(isSaving: false, saveError: failure));
          return left(failure);
        },
        (currentUser) async {
          // User is extracted from the 'Right' side
          if (currentUser == null) {
            // Correctly check if the extracted user is null
            final authFailure =
                AuthFailure("User is not logged in or session expired.");
            _updateData((currentState) =>
                currentState.copyWith(isSaving: false, saveError: authFailure));
            return left(authFailure);
          }

          // --- Upload image if needed, and set avatar type ---
          String? profilePictureUrl;
          String? avatarType;
          // Use state.value! because we know state is AsyncData here
          final currentStateForUpload =
              state.value!; // Get current state for upload logic

          // --- IMAGE UPLOAD ---
          if (currentStateForUpload.profileImage != null) {
            // --- Attempt to Upload Image ---
            final uploadResult = await _userRepository.uploadProfilePicture(
                currentUser.id, currentStateForUpload.profileImage!);

            // --- Use fold, throwing failure on Left ---
            final failureOrUrl = uploadResult.fold(
              // Use await here if the fold returns a Future
              (failure) {
                // Image upload failed, update state and return failure
                _updateData(
                    (cs) => cs.copyWith(isSaving: false, saveError: failure));
                throw failure; // Propagate to outer try-catch to return Left(failure)
              },
              (url) {
                // Upload succeeded, set the variables and continue
                avatarType = "uploaded";
                return url; // Return the URL for profilePictureUrl
              },
            );
            profilePictureUrl = failureOrUrl; // Assign URL if successful
          } else if (currentStateForUpload.defaultAvatar != null) {
            profilePictureUrl = currentStateForUpload.defaultAvatar;
            avatarType = "default";
          } else {
            // Generate avatar logic (or set to null if generation happens elsewhere)
            // If you generate here, you'd call your generateAvatar logic
            // For now, let's assume profilePictureUrl remains null for generated
            profilePictureUrl = null;
            avatarType = "generated";
          }
          // --- END IMAGE UPLOAD ---

          String? finalGeneratedColorHex; // Variable to hold hex string
          if (avatarType == 'generated') {
            // Get the color from state (ensure it's not null)
            final colorValue =
                currentStateForUpload.generatedAvatarColor?.toARGB32();
            if (colorValue != null) {
              finalGeneratedColorHex = colorValue
                  .toRadixString(16)
                  .padLeft(8, '0')
                  .toUpperCase(); // Convert Color to hex string (e.g., FF112233)
            }
          }

          // --- Calculate age ---
          int? age;
          if (currentStateForUpload.birthdate != null) {
            final now = DateTime.now();
            age = now.year - currentStateForUpload.birthdate!.year;
            if (now.month < currentStateForUpload.birthdate!.month ||
                (now.month == currentStateForUpload.birthdate!.month &&
                    now.day < currentStateForUpload.birthdate!.day)) {
              age--;
            }
          }

          // --- Create/Update User object ---
          // Debug location data before saving
          if (currentStateForUpload.location != null) {
            print(
                "OnboardingViewModel: Saving location - lat: ${currentStateForUpload.location!.latitude}, lng: ${currentStateForUpload.location!.longitude}");
          } else {
            print("OnboardingViewModel: No location data to save");
          }

          final updatedUser = User(
            userId: currentUser.id,
            username: currentStateForUpload.username!,
            email: currentUser.email!,
            fullName: currentStateForUpload.fullName!,
            age: age,
            gender: currentStateForUpload.gender!,
            city: currentStateForUpload.city,
            sharedActivities: currentStateForUpload.sharedActivities!,
            myInterests: currentStateForUpload.myInterests,
            profilePictureUrl: profilePictureUrl,
            onboardingComplete: true,
            discoverable: true,
            location: currentStateForUpload.location,
            avatarType: avatarType,
            country: currentStateForUpload.country,
            generatedAvatarColor: finalGeneratedColorHex,
          );

          // --- Save to Supabase ---
          final result = await _userRepository.updateUser(updatedUser);

          return result.fold(
            // Handle final result
            (failure) {
              // Update user failed
              _updateData(
                  (cs) => cs.copyWith(isSaving: false, saveError: failure));
              return left(failure);
            },
            (_) async {
              // Unit success from repository
              // Save onboarding complete flag
              try {
                // A: Local flag indicating onboarding is done for this device/session
                await _prefs.setBool(
                    'onboarding_complete_${currentUser.id}', true);
                // B: Update the current OnboardingViewModel's state (isSaving=false)
                _updateData((cs) =>
                    cs.copyWith(isSaving: false, saveError: null)); // Success
                // C: Invalidate currentUserProfileProvider
                ref.invalidate(
                    currentUserProfileProvider); // Invalidate for next screen
                // D: Return success for the completeOnboarding operation
                return right(unit);
              } catch (e) {
                final prefFailure = AppLogicFailure(
                    "Error saving completion status: $e"); // Use GenericFailure if defined
                _updateData((cs) =>
                    cs.copyWith(isSaving: false, saveError: prefFailure));
                return left(prefFailure);
              }
            },
          );
        },
      );
    } catch (e) {
      // Outer catch for unexpected errors like image upload throw
      final failure = e is Failure
          ? e
          : AppLogicFailure(
              "Unexpected error during onboarding: ${e.toString()}");
      _updateData((currentState) =>
          currentState.copyWith(isSaving: false, saveError: failure));
      return left(failure);
    }
  }

  Either<Failure, Unit> validate() {
    final currentState = state.value!;
    if (currentState.fullName == null || currentState.fullName!.isEmpty) {
      return left(ValidationError('Please enter your full name.'));
    }
    if (currentState.username == null || currentState.username!.isEmpty) {
      return left(ValidationError('Please enter a username.'));
    }
    // --- Add Username Availability Validation ---
    if (currentState.isUsernameChecking) {
      return left(ValidationError('Checking username availability...'));
    }
    if (currentState.usernameError != null) {
      return left(ValidationError(
          currentState.usernameError!)); // Use the error from state
    }
    // --- Basic format validation still useful ---
    if (currentState.username!.length < 3) {
      return left(ValidationError('Username must be at least 3 characters.'));
    }
    if (currentState.username!.contains(' ')) {
      return left(ValidationError('Username cannot contain spaces.'));
    }

    if (currentState.birthdate == null) {
      return left(ValidationError('Please enter your birthdate.'));
    }
    if (currentState.gender == null || currentState.gender!.isEmpty) {
      return left(ValidationError('Please select your gender.'));
    }
    if (currentState.city == null || currentState.city!.isEmpty) {
      if (currentState.location == null) {
        // Require city only if no GPS location
        return left(
            ValidationError("Please enter your city or use current location."));
      }
    }
    if (currentState.sharedActivities == null ||
        currentState.sharedActivities!.isEmpty) {
      return left(
          ValidationError('Please select at least one "Shared Activity".'));
    }
    // Check avatar selection
    if (currentState.avatarType == null) {
      return left(ValidationError('Please select a profile picture option.'));
    }

    return right(unit); // All fields are valid
  }
}

@freezed
abstract class OnboardingState with _$OnboardingState {
  factory OnboardingState({
    @Default(0) int currentPage,
    String? fullName,
    String? username,
    DateTime? birthdate,
    String? gender,
    String? city,
    GeoPoint? location,
    XFile? profileImage, // For temporary storage of picked image
    String? defaultAvatar,
    @Default([]) List<String>? sharedActivities,
    @Default([]) List<String>? myInterests,
    String? country,
    String? avatarType,
    Color? generatedAvatarColor,
    @Default(false) bool isUsernameChecking, // Track if check is in progress
    String? usernameError, // Store error message (null if available/valid)
    @Default(false) bool isSaving, // To show loading indicator on save
    Failure? saveError, // To show error message if save fails
  }) = _OnboardingState;
}
