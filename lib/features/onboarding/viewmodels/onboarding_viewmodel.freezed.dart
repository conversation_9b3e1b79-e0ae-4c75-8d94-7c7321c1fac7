// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_viewmodel.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OnboardingState {
  int get currentPage;
  String? get fullName;
  String? get username;
  DateTime? get birthdate;
  String? get gender;
  String? get city;
  GeoPoint? get location;
  XFile? get profileImage; // For temporary storage of picked image
  String? get defaultAvatar;
  List<String>? get sharedActivities;
  List<String>? get myInterests;
  String? get country;
  String? get avatarType;
  Color? get generatedAvatarColor;
  bool get isUsernameChecking; // Track if check is in progress
  String? get usernameError; // Store error message (null if available/valid)
  bool get isSaving; // To show loading indicator on save
  Failure? get saveError;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OnboardingStateCopyWith<OnboardingState> get copyWith =>
      _$OnboardingStateCopyWithImpl<OnboardingState>(
          this as OnboardingState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OnboardingState &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.birthdate, birthdate) ||
                other.birthdate == birthdate) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.defaultAvatar, defaultAvatar) ||
                other.defaultAvatar == defaultAvatar) &&
            const DeepCollectionEquality()
                .equals(other.sharedActivities, sharedActivities) &&
            const DeepCollectionEquality()
                .equals(other.myInterests, myInterests) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.avatarType, avatarType) ||
                other.avatarType == avatarType) &&
            (identical(other.generatedAvatarColor, generatedAvatarColor) ||
                other.generatedAvatarColor == generatedAvatarColor) &&
            (identical(other.isUsernameChecking, isUsernameChecking) ||
                other.isUsernameChecking == isUsernameChecking) &&
            (identical(other.usernameError, usernameError) ||
                other.usernameError == usernameError) &&
            (identical(other.isSaving, isSaving) ||
                other.isSaving == isSaving) &&
            (identical(other.saveError, saveError) ||
                other.saveError == saveError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentPage,
      fullName,
      username,
      birthdate,
      gender,
      city,
      location,
      profileImage,
      defaultAvatar,
      const DeepCollectionEquality().hash(sharedActivities),
      const DeepCollectionEquality().hash(myInterests),
      country,
      avatarType,
      generatedAvatarColor,
      isUsernameChecking,
      usernameError,
      isSaving,
      saveError);

  @override
  String toString() {
    return 'OnboardingState(currentPage: $currentPage, fullName: $fullName, username: $username, birthdate: $birthdate, gender: $gender, city: $city, location: $location, profileImage: $profileImage, defaultAvatar: $defaultAvatar, sharedActivities: $sharedActivities, myInterests: $myInterests, country: $country, avatarType: $avatarType, generatedAvatarColor: $generatedAvatarColor, isUsernameChecking: $isUsernameChecking, usernameError: $usernameError, isSaving: $isSaving, saveError: $saveError)';
  }
}

/// @nodoc
abstract mixin class $OnboardingStateCopyWith<$Res> {
  factory $OnboardingStateCopyWith(
          OnboardingState value, $Res Function(OnboardingState) _then) =
      _$OnboardingStateCopyWithImpl;
  @useResult
  $Res call(
      {int currentPage,
      String? fullName,
      String? username,
      DateTime? birthdate,
      String? gender,
      String? city,
      GeoPoint? location,
      XFile? profileImage,
      String? defaultAvatar,
      List<String>? sharedActivities,
      List<String>? myInterests,
      String? country,
      String? avatarType,
      Color? generatedAvatarColor,
      bool isUsernameChecking,
      String? usernameError,
      bool isSaving,
      Failure? saveError});
}

/// @nodoc
class _$OnboardingStateCopyWithImpl<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  _$OnboardingStateCopyWithImpl(this._self, this._then);

  final OnboardingState _self;
  final $Res Function(OnboardingState) _then;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? fullName = freezed,
    Object? username = freezed,
    Object? birthdate = freezed,
    Object? gender = freezed,
    Object? city = freezed,
    Object? location = freezed,
    Object? profileImage = freezed,
    Object? defaultAvatar = freezed,
    Object? sharedActivities = freezed,
    Object? myInterests = freezed,
    Object? country = freezed,
    Object? avatarType = freezed,
    Object? generatedAvatarColor = freezed,
    Object? isUsernameChecking = null,
    Object? usernameError = freezed,
    Object? isSaving = null,
    Object? saveError = freezed,
  }) {
    return _then(_self.copyWith(
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      birthdate: freezed == birthdate
          ? _self.birthdate
          : birthdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as XFile?,
      defaultAvatar: freezed == defaultAvatar
          ? _self.defaultAvatar
          : defaultAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedActivities: freezed == sharedActivities
          ? _self.sharedActivities
          : sharedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      myInterests: freezed == myInterests
          ? _self.myInterests
          : myInterests // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarType: freezed == avatarType
          ? _self.avatarType
          : avatarType // ignore: cast_nullable_to_non_nullable
              as String?,
      generatedAvatarColor: freezed == generatedAvatarColor
          ? _self.generatedAvatarColor
          : generatedAvatarColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      isUsernameChecking: null == isUsernameChecking
          ? _self.isUsernameChecking
          : isUsernameChecking // ignore: cast_nullable_to_non_nullable
              as bool,
      usernameError: freezed == usernameError
          ? _self.usernameError
          : usernameError // ignore: cast_nullable_to_non_nullable
              as String?,
      isSaving: null == isSaving
          ? _self.isSaving
          : isSaving // ignore: cast_nullable_to_non_nullable
              as bool,
      saveError: freezed == saveError
          ? _self.saveError
          : saveError // ignore: cast_nullable_to_non_nullable
              as Failure?,
    ));
  }
}

/// @nodoc

class _OnboardingState implements OnboardingState {
  _OnboardingState(
      {this.currentPage = 0,
      this.fullName,
      this.username,
      this.birthdate,
      this.gender,
      this.city,
      this.location,
      this.profileImage,
      this.defaultAvatar,
      final List<String>? sharedActivities = const [],
      final List<String>? myInterests = const [],
      this.country,
      this.avatarType,
      this.generatedAvatarColor,
      this.isUsernameChecking = false,
      this.usernameError,
      this.isSaving = false,
      this.saveError})
      : _sharedActivities = sharedActivities,
        _myInterests = myInterests;

  @override
  @JsonKey()
  final int currentPage;
  @override
  final String? fullName;
  @override
  final String? username;
  @override
  final DateTime? birthdate;
  @override
  final String? gender;
  @override
  final String? city;
  @override
  final GeoPoint? location;
  @override
  final XFile? profileImage;
// For temporary storage of picked image
  @override
  final String? defaultAvatar;
  final List<String>? _sharedActivities;
  @override
  @JsonKey()
  List<String>? get sharedActivities {
    final value = _sharedActivities;
    if (value == null) return null;
    if (_sharedActivities is EqualUnmodifiableListView)
      return _sharedActivities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _myInterests;
  @override
  @JsonKey()
  List<String>? get myInterests {
    final value = _myInterests;
    if (value == null) return null;
    if (_myInterests is EqualUnmodifiableListView) return _myInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? country;
  @override
  final String? avatarType;
  @override
  final Color? generatedAvatarColor;
  @override
  @JsonKey()
  final bool isUsernameChecking;
// Track if check is in progress
  @override
  final String? usernameError;
// Store error message (null if available/valid)
  @override
  @JsonKey()
  final bool isSaving;
// To show loading indicator on save
  @override
  final Failure? saveError;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OnboardingStateCopyWith<_OnboardingState> get copyWith =>
      __$OnboardingStateCopyWithImpl<_OnboardingState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OnboardingState &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.birthdate, birthdate) ||
                other.birthdate == birthdate) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.defaultAvatar, defaultAvatar) ||
                other.defaultAvatar == defaultAvatar) &&
            const DeepCollectionEquality()
                .equals(other._sharedActivities, _sharedActivities) &&
            const DeepCollectionEquality()
                .equals(other._myInterests, _myInterests) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.avatarType, avatarType) ||
                other.avatarType == avatarType) &&
            (identical(other.generatedAvatarColor, generatedAvatarColor) ||
                other.generatedAvatarColor == generatedAvatarColor) &&
            (identical(other.isUsernameChecking, isUsernameChecking) ||
                other.isUsernameChecking == isUsernameChecking) &&
            (identical(other.usernameError, usernameError) ||
                other.usernameError == usernameError) &&
            (identical(other.isSaving, isSaving) ||
                other.isSaving == isSaving) &&
            (identical(other.saveError, saveError) ||
                other.saveError == saveError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentPage,
      fullName,
      username,
      birthdate,
      gender,
      city,
      location,
      profileImage,
      defaultAvatar,
      const DeepCollectionEquality().hash(_sharedActivities),
      const DeepCollectionEquality().hash(_myInterests),
      country,
      avatarType,
      generatedAvatarColor,
      isUsernameChecking,
      usernameError,
      isSaving,
      saveError);

  @override
  String toString() {
    return 'OnboardingState(currentPage: $currentPage, fullName: $fullName, username: $username, birthdate: $birthdate, gender: $gender, city: $city, location: $location, profileImage: $profileImage, defaultAvatar: $defaultAvatar, sharedActivities: $sharedActivities, myInterests: $myInterests, country: $country, avatarType: $avatarType, generatedAvatarColor: $generatedAvatarColor, isUsernameChecking: $isUsernameChecking, usernameError: $usernameError, isSaving: $isSaving, saveError: $saveError)';
  }
}

/// @nodoc
abstract mixin class _$OnboardingStateCopyWith<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  factory _$OnboardingStateCopyWith(
          _OnboardingState value, $Res Function(_OnboardingState) _then) =
      __$OnboardingStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int currentPage,
      String? fullName,
      String? username,
      DateTime? birthdate,
      String? gender,
      String? city,
      GeoPoint? location,
      XFile? profileImage,
      String? defaultAvatar,
      List<String>? sharedActivities,
      List<String>? myInterests,
      String? country,
      String? avatarType,
      Color? generatedAvatarColor,
      bool isUsernameChecking,
      String? usernameError,
      bool isSaving,
      Failure? saveError});
}

/// @nodoc
class __$OnboardingStateCopyWithImpl<$Res>
    implements _$OnboardingStateCopyWith<$Res> {
  __$OnboardingStateCopyWithImpl(this._self, this._then);

  final _OnboardingState _self;
  final $Res Function(_OnboardingState) _then;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentPage = null,
    Object? fullName = freezed,
    Object? username = freezed,
    Object? birthdate = freezed,
    Object? gender = freezed,
    Object? city = freezed,
    Object? location = freezed,
    Object? profileImage = freezed,
    Object? defaultAvatar = freezed,
    Object? sharedActivities = freezed,
    Object? myInterests = freezed,
    Object? country = freezed,
    Object? avatarType = freezed,
    Object? generatedAvatarColor = freezed,
    Object? isUsernameChecking = null,
    Object? usernameError = freezed,
    Object? isSaving = null,
    Object? saveError = freezed,
  }) {
    return _then(_OnboardingState(
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _self.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      birthdate: freezed == birthdate
          ? _self.birthdate
          : birthdate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gender: freezed == gender
          ? _self.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as GeoPoint?,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as XFile?,
      defaultAvatar: freezed == defaultAvatar
          ? _self.defaultAvatar
          : defaultAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      sharedActivities: freezed == sharedActivities
          ? _self._sharedActivities
          : sharedActivities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      myInterests: freezed == myInterests
          ? _self._myInterests
          : myInterests // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      country: freezed == country
          ? _self.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarType: freezed == avatarType
          ? _self.avatarType
          : avatarType // ignore: cast_nullable_to_non_nullable
              as String?,
      generatedAvatarColor: freezed == generatedAvatarColor
          ? _self.generatedAvatarColor
          : generatedAvatarColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      isUsernameChecking: null == isUsernameChecking
          ? _self.isUsernameChecking
          : isUsernameChecking // ignore: cast_nullable_to_non_nullable
              as bool,
      usernameError: freezed == usernameError
          ? _self.usernameError
          : usernameError // ignore: cast_nullable_to_non_nullable
              as String?,
      isSaving: null == isSaving
          ? _self.isSaving
          : isSaving // ignore: cast_nullable_to_non_nullable
              as bool,
      saveError: freezed == saveError
          ? _self.saveError
          : saveError // ignore: cast_nullable_to_non_nullable
              as Failure?,
    ));
  }
}

// dart format on
