# Onboarding Architecture Deep Dive

## Overview

The refactored onboarding system uses a **component-based architecture** where each piece has a specific responsibility. Instead of one giant file handling everything, we have specialized components that work together through well-defined interfaces.

## Component Breakdown

### 1. **Main Screen (Coordinator Pattern)**

```dart
class OnboardingScreenRefactored extends ConsumerStatefulWidget 
    with OnboardingNavigation<PERSON>el<PERSON> {
  // Acts as the central coordinator
}
```

**Responsibilities:**
- Manages the `PageController` for navigation
- Holds form controllers (`_fullNameController`, `_usernameController`)
- Implements the navigation mixin to get shared behavior
- Orchestrates communication between pages via callbacks

**Think of it as:** The conductor of an orchestra - doesn't play instruments but coordinates all the musicians.

### 2. **Individual Pages (Component Pattern)**

```dart
class NameUsernamePage extends OnboardingPageBase {
  // Handles only name/username logic
}
```

**Responsibilities:**
- Renders its specific UI
- Handles form validation for its fields
- Collects user input
- Communicates with parent via callbacks

**Think of it as:** Individual musicians in the orchestra - each plays their part and follows the conductor's cues.

### 3. **EditContext (State Object Pattern)**

```dart
class EditContext {
  final bool isEditing;
  final Map<String, dynamic> originalValues;
  final int sourcePageIndex;
  final int targetPageIndex;
}
```

**Responsibilities:**
- Tracks whether we're in edit mode
- Stores original values for cancellation
- Knows where we came from and where we're going

**Think of it as:** A clipboard that remembers what you were doing and where you were.

## Data Flow Architecture

### Normal Flow (New User)
```
User Input → Page → ViewModel → State Update → UI Rebuild
```

### Edit Flow (From Summary)
```
1. Summary Page → "Edit" clicked
2. EditContext created with original values
3. Navigate to target page
4. User makes changes
5. Changes applied to ViewModel
6. Navigate back to Summary
7. Summary shows updated values
```

## Detailed Flow Example

Let's trace editing a name:

### Step 1: User Clicks Edit
```dart
// In SummaryPage
EditableField(
  title: 'Full Name',
  value: state.fullName ?? 'Not set',
  onEdit: () => _startEditingField(
    context: context,
    targetPageIndex: nameUsernamePageIndex,
    originalValues: {
      'fullName': state.fullName,
      'username': state.username,
    },
  ),
)
```

### Step 2: Start Editing Process
```dart
// In SummaryPage._startEditingField()
onStartEditing(
  targetPageIndex: targetPageIndex,
  sourcePageIndex: state.currentPage,
  originalValues: originalValues,
);
```

### Step 3: Navigation Helper Takes Over
```dart
// In OnboardingNavigationHelper.startEditing()
final newContext = EditContext.forField(
  targetPageIndex: targetPageIndex,
  sourcePageIndex: sourcePageIndex,
  originalValues: originalValues,
);

updateEditContext(newContext);
await animateToPage(targetPageIndex);
```

### Step 4: Page Renders in Edit Mode
```dart
// In NameUsernamePage
buildNavigationButtons(
  // ...
  child: Text(editContext.isEditing ? 'Update' : 'Next'),
)
```

### Step 5: User Clicks Update
```dart
// In NameUsernamePage
onNextPressed: () async {
  if (editContext.isEditing) {
    await onApplyEdits(
      getUpdatedValues: () => collectFieldValues(context)
    );
  }
}
```

### Step 6: Apply Changes
```dart
// In OnboardingNavigationHelper.applyEdits()
final updatedValues = getUpdatedValues();
_applyUpdatesToViewModel(updatedValues);
await Future.delayed(const Duration(milliseconds: 100));
await animateToPage(editContext.sourcePageIndex);
```

## Communication Patterns

### 1. **Callback Pattern (Child → Parent)**
```dart
// Child page communicates to parent via callbacks
NameUsernamePage(
  onNavigate: animateToPage,        // "Please navigate to page X"
  onApplyEdits: applyEdits,         // "Please save these changes"
  onCancelEdits: cancelEdits,       // "Please cancel and revert"
)
```

### 2. **Dependency Injection Pattern**
```dart
// Parent provides dependencies to child
NameUsernamePage(
  state: value,                     // Current state
  viewModel: viewModel,             // Business logic
  editContext: editContext,         // Edit state
  formKey: _nameUsernameFormKey,    // Form controller
  fullNameController: _fullNameController, // Text controller
)
```

### 3. **Observer Pattern (Riverpod)**
```dart
// Pages watch state changes
final stateAsync = ref.watch(onboardingViewModelProvider);
```

## Benefits of This Architecture

### 1. **Separation of Concerns**
- Each file has one clear responsibility
- UI logic separated from business logic
- Navigation logic centralized in mixin

### 2. **Reusability**
- `EditableField` can be used anywhere
- `OnboardingPageBase` provides common functionality
- Navigation patterns can be reused in other features

### 3. **Testability**
- Each component can be tested in isolation
- Mock dependencies easily
- Clear interfaces make testing straightforward

### 4. **Maintainability**
- Changes to one page don't affect others
- Easy to add new pages
- Clear data flow makes debugging easier

## Comparison: Old vs New

### Old Approach (Monolithic)
```dart
class OnboardingScreen {
  // 1200+ lines
  bool _isEditingFromSummary = false;
  String? _originalFullName;
  String? _originalUsername;
  // ... 20+ state variables
  
  Widget _buildNameUsernamePage() { /* 200 lines */ }
  Widget _buildSummaryPage() { /* 300 lines */ }
  // ... 8 page builders
}
```

**Problems:**
- Hard to find specific logic
- Changes affect multiple areas
- Difficult to test individual parts
- State management becomes complex

### New Approach (Modular)
```dart
// Main coordinator (150 lines)
class OnboardingScreenRefactored with OnboardingNavigationHelper {
  EditContext _editContext = const EditContext();
}

// Individual pages (50-100 lines each)
class NameUsernamePage extends OnboardingPageBase {
  // Only name/username logic
}

// Reusable components (20-50 lines each)
class EditableField extends StatelessWidget {
  // Only field display logic
}
```

**Benefits:**
- Easy to find specific logic
- Changes are isolated
- Each component is testable
- State management is structured
