# Onboarding Feature Migration Guide

This guide provides step-by-step instructions for migrating from the original monolithic `onboarding_screen.dart` to the refactored, modular implementation.

## Step 1: Review the New Structure

Familiarize yourself with the new directory structure and components:

```
lib/features/onboarding/
├── constants/
│   └── page_indices.dart
├── helpers/
│   └── navigation_helper.dart
├── models/
│   └── edit_context.dart
├── pages/
│   ├── name_username_page.dart
│   ├── summary_page.dart
│   └── ...
├── screens/
│   └── onboarding_screen_refactored.dart
├── viewmodels/
│   └── onboarding_viewmodel.dart
└── widgets/
│   ├── editable_field.dart
│   └── onboarding_page_base.dart
├── README.md
└── MIGRATION_GUIDE.md
```

## Step 2: Implement the Remaining Pages

Currently, only the `NameUsernamePage` and `SummaryPage` have been fully implemented. You need to implement the remaining pages:

1. Create `dob_gender_page.dart` in the `pages` directory
2. Create `profile_picture_page.dart` in the `pages` directory
3. Create `location_page.dart` in the `pages` directory
4. Create `shared_activities_page.dart` in the `pages` directory
5. Create `my_interests_page.dart` in the `pages` directory
6. Create `welcome_page.dart` in the `pages` directory

Use the existing pages as templates. Each page should:
- Extend `OnboardingPageBase`
- Implement `buildPageContent` and `collectFieldValues` methods
- Use the provided callbacks for navigation and state management

## Step 3: Update the Refactored Screen

Once all pages are implemented, update `onboarding_screen_refactored.dart` to use the new page components:

```dart
// Replace placeholder methods with imports
import 'package:watermelon_draft/features/onboarding/pages/welcome_page.dart';
import 'package:watermelon_draft/features/onboarding/pages/dob_gender_page.dart';
// ... other imports

// Update the PageView to use the imported components
body: switch (stateAsync) {
  AsyncData(:final value) => PageView(
      controller: pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: <Widget>[
        WelcomePage(
          state: value,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: animateToPage,
          onApplyEdits: applyEdits,
          onCancelEdits: cancelEdits,
        ),
        NameUsernamePage(
          // ... existing code
        ),
        DobGenderPage(
          state: value,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: animateToPage,
          onApplyEdits: applyEdits,
          onCancelEdits: cancelEdits,
          selectedDate: _selectedDate,
          onDateSelected: (date) {
            setState(() {
              _selectedDate = date;
            });
          },
        ),
        // ... other pages
      ],
    ),
  // ... rest of the switch
}
```

## Step 4: Test the Refactored Implementation

Before replacing the original implementation:

1. Update `main.dart` to use `OnboardingScreenRefactored` instead of `OnboardingScreen`
2. Test all pages and flows:
   - New user flow
   - Edit flow from summary page
   - Validation
   - Error handling
   - Navigation between pages

## Step 5: Replace the Original Implementation

Once testing is complete:

1. Rename `onboarding_screen_refactored.dart` to `onboarding_screen.dart`
2. Update the class name from `OnboardingScreenRefactored` to `OnboardingScreen`
3. Update any imports in other files that reference the onboarding screen

## Step 6: Clean Up

Remove any unused files or code:

1. Delete the original `onboarding_screen.dart` if you kept it during migration
2. Remove any temporary placeholder methods from the main screen
3. Update any documentation or comments to reflect the new structure

## Common Issues and Solutions

### State Not Updating

If you encounter issues with state not updating properly:

```dart
// Add a small delay to ensure state updates are processed
await Future.delayed(const Duration(milliseconds: 100));

// Always check if the widget is still mounted
if (mounted) {
  // Perform UI updates
}
```

### Form Validation

Ensure form validation is properly handled:

```dart
// Dismiss keyboard before validation
FocusScope.of(context).unfocus();

// Validate the form
final bool isFormValid = formKey.currentState?.validate() ?? false;

// Check other conditions
final bool canProceed = isFormValid && otherConditions;

if (canProceed) {
  // Proceed with navigation or edits
}
```

### Edit Context Management

Make sure to update the edit context correctly:

```dart
// Starting edit mode
updateEditContext(EditContext.forField(
  targetPageIndex: targetPageIndex,
  sourcePageIndex: sourcePageIndex,
  originalValues: originalValues,
));

// Resetting edit mode
updateEditContext(editContext.reset());
```

## Next Steps

After completing the migration:

1. Consider adding unit tests for the new components
2. Document any complex logic or edge cases
3. Look for opportunities to further improve the code organization
4. Apply similar refactoring patterns to other complex screens in the app
