// lib/features/onboarding/pages/profile_picture_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/widgets/profile_image_picker.dart';

/// The page for selecting a profile picture in the onboarding flow.
class ProfilePicturePage extends OnboardingPageBase {
  const ProfilePicturePage({
    super.key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
  }) : super(
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Header
          Text(
            'Profile Picture *',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Choose how your profile picture will appear. A picture is required.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Profile Image Picker
          Expanded(
            child: Center(
              child: ProfileImagePicker(
                // Pass initial values from ViewModel state
                initialImageFile: editContext.isEditing
                    ? _getOriginalProfileImage(context)
                    : state.profileImage,
                initialDefaultAvatarPath: editContext.isEditing
                    ? _getOriginalDefaultAvatarPath(context)
                    : state.defaultAvatar,
                nameForGeneratedAvatar: state.fullName ?? 'Unknown',
                initialAvatarType: editContext.isEditing
                    ? _getOriginalAvatarType(context)
                    : state.avatarType,
                initialGeneratedColor: editContext.isEditing
                    ? _getOriginalGeneratedAvatarColor(context)
                    : state.generatedAvatarColor,

                // Connect callbacks to ViewModel methods
                onImageSelected: (image) {
                  viewModel.updateProfileImage(image);
                },
                onDefaultAvatarSelected: (path) {
                  viewModel.updateDefaultAvatar(path);
                },
                onGeneratedAvatarSelected: (color) {
                  viewModel.selectGeneratedAvatar(color);
                },
                onGeneratedAvatarColorRegenerated: (newColor) {
                  viewModel.updateGeneratedAvatarColor(newColor);
                },

                updateButtonText: editContext.isEditing ? 'Update' : 'Next',
                cancelButtonText: editContext.isEditing ? 'Cancel' : 'Back',
                onUpdate: () => _handleNext(context),
                onBack: () => _handleBack(context),
              ),
            ),
          ),

          // Footer text
          Text(
            'You can change your profile picture later in your profile settings.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'profileImage': state.profileImage,
      'defaultAvatar': state.defaultAvatar,
      'avatarType': state.avatarType,
      'generatedAvatarColor': state.generatedAvatarColor,
    };
  }

  /// Handle the Next/Update button press
  Future<void> _handleNext(BuildContext context) async {
    if (editContext.isEditing) {
      // Apply edits and return to summary
      await onApplyEdits(getUpdatedValues: () => collectFieldValues(context));
    } else {
      // Standard next page navigation - no validation blocking
      viewModel.nextPage();
      await onNavigate(state.currentPage + 1);
    }
  }

  /// Handle the Back/Cancel button press
  Future<void> _handleBack(BuildContext context) async {
    if (editContext.isEditing) {
      // Revert changes and cancel edits
      await _revertProfileImageChanges(context);
      await onCancelEdits();
    } else {
      // Standard back page navigation
      viewModel.previousPage();
      await onNavigate(state.currentPage - 1);
    }
  }

  /// Revert profile image changes when canceling edit mode
  Future<void> _revertProfileImageChanges(BuildContext context) async {
    final originalValues = editContext.originalValues;
    final originalAvatarType = originalValues['avatarType'] as String?;
    final originalProfileImage = originalValues['profileImage'] as XFile?;
    final originalDefaultAvatarPath =
        originalValues['defaultAvatar'] as String?;
    final originalGeneratedAvatarColor =
        originalValues['generatedAvatarColor'] as Color?;

    // Revert to original values
    if (originalAvatarType == 'uploaded' && originalProfileImage != null) {
      viewModel.updateProfileImage(originalProfileImage);
    } else if (originalAvatarType == 'default' &&
        originalDefaultAvatarPath != null) {
      viewModel.updateDefaultAvatar(originalDefaultAvatarPath);
    } else if (originalAvatarType == 'generated' &&
        originalGeneratedAvatarColor != null) {
      viewModel.selectGeneratedAvatar(originalGeneratedAvatarColor);
    } else {
      // Fallback: clear avatar state
      viewModel.updateProfileImage(null);
    }
  }

  /// Get original profile image for edit mode
  XFile? _getOriginalProfileImage(BuildContext context) {
    return editContext.originalValues['profileImage'] as XFile?;
  }

  /// Get original default avatar path for edit mode
  String? _getOriginalDefaultAvatarPath(BuildContext context) {
    return editContext.originalValues['defaultAvatar'] as String?;
  }

  /// Get original avatar type for edit mode
  String? _getOriginalAvatarType(BuildContext context) {
    return editContext.originalValues['avatarType'] as String?;
  }

  /// Get original generated avatar color for edit mode
  Color? _getOriginalGeneratedAvatarColor(BuildContext context) {
    return editContext.originalValues['generatedAvatarColor'] as Color?;
  }
}
