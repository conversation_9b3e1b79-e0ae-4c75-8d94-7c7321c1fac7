// lib/features/onboarding/pages/summary_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/editable_field.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';

/// The summary page for the onboarding flow.
class SummaryPage extends OnboardingPageBase {
  /// Callback for starting the edit process
  final Future<void> Function({
    required int targetPageIndex,
    required int sourcePageIndex,
    required Map<String, dynamic> originalValues,
  }) onStartEditing;

  /// Page indices for navigation
  final int nameUsernamePageIndex;
  final int dobGenderPageIndex;
  final int profilePicPageIndex;
  final int locationPageIndex;
  final int sharedActivitiesPageIndex;
  final int myInterestsPageIndex;

  const SummaryPage({
    super.key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
    required this.onStartEditing,
    required this.nameUsernamePageIndex,
    required this.dobGenderPageIndex,
    required this.profilePicPageIndex,
    required this.locationPageIndex,
    required this.sharedActivitiesPageIndex,
    required this.myInterestsPageIndex,
  }) : super(
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Summary', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 16),

          // Profile Picture
          EditableProfilePicture(
            profilePictureWidget: _buildProfilePictureWidget(),
            onEdit: () => _startEditingField(
              context: context,
              targetPageIndex: profilePicPageIndex,
              originalValues: {
                'avatarType': state.avatarType,
                'profileImage': state.profileImage,
                'defaultAvatar': state.defaultAvatar,
                'generatedAvatarColor': state.generatedAvatarColor,
              },
            ),
          ),
          const SizedBox(height: 16),

          // Scrollable list of editable fields
          Expanded(
            child: ListView(
              children: [
                // Full Name
                EditableField(
                  title: 'Full Name',
                  value: state.fullName ?? 'Not set',
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: nameUsernamePageIndex,
                    originalValues: {
                      'fullName': state.fullName,
                      'username': state.username,
                    },
                  ),
                ),

                // Username
                EditableField(
                  title: 'Username',
                  value: '@${state.username ?? 'Not set'}',
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: nameUsernamePageIndex,
                    originalValues: {
                      'fullName': state.fullName,
                      'username': state.username,
                    },
                  ),
                ),

                // Date of Birth
                EditableField(
                  title: 'Date of Birth',
                  value: state.birthdate != null
                      ? '${state.birthdate!.toLocal()}'.split(' ')[0]
                      : 'Not set',
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: dobGenderPageIndex,
                    originalValues: {
                      'birthdate': state.birthdate,
                      'gender': state.gender,
                    },
                  ),
                ),

                // Gender
                EditableField(
                  title: 'Gender',
                  value: state.gender ?? 'Not set',
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: dobGenderPageIndex,
                    originalValues: {
                      'birthdate': state.birthdate,
                      'gender': state.gender,
                    },
                  ),
                ),

                // Location
                EditableField(
                  title: 'Current City',
                  value: state.city ?? 'Not yet selected',
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: locationPageIndex,
                    originalValues: {
                      'city': state.city,
                      'location': state.location,
                      'country': state.country,
                    },
                  ),
                ),

                // Shared Activities
                EditableListField(
                  title: 'Shared Activities',
                  values: state.sharedActivities,
                  resolveIds: true, // Resolve activity IDs to names
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: sharedActivitiesPageIndex,
                    originalValues: {
                      'sharedActivities': state.sharedActivities,
                    },
                  ),
                ),

                // My Interests
                EditableListField(
                  title: 'My Interests',
                  values: state.myInterests,
                  onEdit: () => _startEditingField(
                    context: context,
                    targetPageIndex: myInterestsPageIndex,
                    originalValues: {
                      'myInterests': state.myInterests,
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Navigation Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage();
                  onNavigate(state.currentPage - 1);
                },
                child: const Text('Back'),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Complete onboarding
                  final result = await viewModel.completeOnboarding();
                  result.fold(
                    (failure) {
                      // Error handling is done by OnboardingScreen listener
                      // No need to show SnackBar here to avoid duplicates
                      print(
                          "Summary: Onboarding completion failed: ${failure.message}");
                    },
                    (_) {
                      print("SummaryPage: $state");
                      // Navigate to home on success
                      if (context.mounted) {
                        context.beamToReplacementNamed('/home');
                      }
                    },
                  );
                },
                child: state.isSaving
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text('Complete Onboarding'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Start editing a field
  void _startEditingField({
    required BuildContext context,
    required int targetPageIndex,
    required Map<String, dynamic> originalValues,
  }) {
    onStartEditing(
      targetPageIndex: targetPageIndex,
      sourcePageIndex: state.currentPage,
      originalValues: originalValues,
    );
  }

  /// Build the profile picture widget based on the avatar type
  Widget _buildProfilePictureWidget() {
    if (state.avatarType == 'uploaded' && state.profileImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.file(
          File(state.profileImage!.path),
          fit: BoxFit.cover,
          width: double.infinity,
          height: 150,
        ),
      );
    } else if (state.avatarType == 'default' && state.defaultAvatar != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.asset(
          state.defaultAvatar!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: 150,
        ),
      );
    } else if (state.avatarType == 'generated') {
      return generateAvatar(
        state.fullName ?? '?',
        radius: 60,
        color: state.generatedAvatarColor,
      );
    } else {
      return const Icon(Icons.person, size: 80, color: Colors.grey);
    }
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    // Not used for summary page
    return {};
  }
}
