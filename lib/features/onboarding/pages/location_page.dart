// lib/features/onboarding/pages/location_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/widgets/city_selector_widget.dart';

/// The page for setting location in the onboarding flow.
/// Uses the enhanced CitySelectorWidget with MapBox Search integration.
class LocationPage extends OnboardingPageBase {
  const LocationPage({
    super.key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
  }) : super(
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Set Your Location *',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Watermelon uses your location to help you find connections and activities nearby.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 24),

          // Enhanced City Selector Widget with MapBox Search
          Expanded(
            child: CitySelectorWidget(
              initialCity: editContext.isEditing
                  ? _getOriginalCity(context)
                  : state.city,
              initialLocation: editContext.isEditing
                  ? _getOriginalLocation(context)
                  : state.location,
              initialCountry: editContext.isEditing
                  ? _getOriginalCountry(context)
                  : state.country,
              onLocationSet: (location, city, country) {
                // This callback updates the ViewModel's state
                // The CitySelectorWidget handles getting location/city/country with MapBox reliability
                print(
                    "LocationPage: Location set - City: $city, Country: $country");

                if (location != null) {
                  // Prefer GPS location if available, update all fields
                  viewModel.updateLocationFromPoint(location);
                } else if (city != null) {
                  // Fallback to city name if GPS failed or wasn't used
                  // This method in the VM should also update the country
                  viewModel.updateLocationFromAddress(city);
                }

                // Update country if available (from MapBox or reverse geocoding)
                if (country != null) {
                  // Note: You might need to add this method to the ViewModel
                  // viewModel.updateCountry(country);
                }

                // If both location and city are null, the ViewModel state remains unchanged
              },
            ),
          ),

          const SizedBox(height: 16),

          // Navigation Buttons
          buildNavigationButtons(
            context: context,
            currentPage: state.currentPage,
            previousPage: state.currentPage - 1,
            nextPage: state.currentPage + 1,
            canProceedToNext:
                true, // Always allow navigation - validation happens at summary
            onNextPressed: () async {
              if (editContext.isEditing) {
                // Apply edits and return to summary
                await onApplyEdits(
                    getUpdatedValues: () => collectFieldValues(context));
              } else {
                // Standard next page navigation - no validation blocking
                viewModel.nextPage();
                await onNavigate(state.currentPage + 1);
              }
            },
            onBackPressed: () async {
              if (editContext.isEditing) {
                // Revert changes and cancel edits
                await _revertLocationChanges(context);
                await onCancelEdits();
              } else {
                // Standard back page navigation
                viewModel.previousPage();
                await onNavigate(state.currentPage - 1);
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'city': state.city,
      'location': state.location,
      'country': state.country,
    };
  }

  /// Revert location changes when canceling edit mode
  Future<void> _revertLocationChanges(BuildContext context) async {
    final originalValues = editContext.originalValues;
    final originalCity = originalValues['city'] as String?;
    final originalLocation = originalValues['location'] as GeoPoint?;

    // Revert to original values
    if (originalLocation != null) {
      // If we had a GPS location, restore it
      viewModel.updateLocationFromPoint(originalLocation);
    } else if (originalCity != null) {
      // If we had a city name, restore it
      viewModel.updateLocationFromAddress(originalCity);
    }
    // Note: If both were null originally, the ViewModel state will remain unchanged
    // which effectively clears the location data
  }

  /// Get original city for edit mode
  String? _getOriginalCity(BuildContext context) {
    return editContext.originalValues['city'] as String?;
  }

  /// Get original location for edit mode
  GeoPoint? _getOriginalLocation(BuildContext context) {
    return editContext.originalValues['location'] as GeoPoint?;
  }

  /// Get original country for edit mode
  String? _getOriginalCountry(BuildContext context) {
    return editContext.originalValues['country'] as String?;
  }
}
