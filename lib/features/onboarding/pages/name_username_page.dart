// lib/features/onboarding/pages/name_username_page.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/widgets/full_name_input_widget.dart';

/// The page for entering name and username in the onboarding flow.
class NameUsernamePage extends OnboardingPageBase {
  /// The form key for validating the form
  final GlobalKey<FormState> formKey;

  /// The controller for the full name field
  final TextEditingController fullNameController;

  /// The controller for the username field
  final TextEditingController usernameController;

  const NameUsernamePage({
    super.key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
    required this.formKey,
    required this.fullNameController,
    required this.usernameController,
  }) : super(
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Your Name & Username',
                style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            const Text(
                'Let\'s start with the basics. Your username must be unique.'),
            const SizedBox(height: 24),

            // Full Name Input
            FullNameInputWidget(
              controller: fullNameController,
              onChanged: (value) => viewModel.updateFullName(value),
            ),
            const SizedBox(height: 16),

            // Username Input
            buildUsernameField(context),

            const Spacer(), // Push buttons to bottom

            // Navigation Buttons
            buildNavigationButtons(
              context: context,
              currentPage: state.currentPage,
              previousPage: state.currentPage - 1,
              nextPage: state.currentPage + 1,
              canProceedToNext: _canProceedToNext(),
              onNextPressed: () async {
                // Dismiss keyboard before validation/navigation
                FocusScope.of(context).unfocus();

                final bool isFormValid =
                    formKey.currentState?.validate() ?? false;
                final bool isUsernameAvailable =
                    (state.usernameError == null && !state.isUsernameChecking);
                final bool canProceed = isFormValid && isUsernameAvailable;

                if (canProceed) {
                  if (editContext.isEditing) {
                    // Apply edits and return to summary
                    await onApplyEdits(
                        getUpdatedValues: () => collectFieldValues(context));
                  } else {
                    // Standard next page navigation
                    viewModel.nextPage();
                    await onNavigate(state.currentPage + 1);
                  }
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build the username field with validation and availability checking
  Widget buildUsernameField(BuildContext context) {
    return TextFormField(
      controller: usernameController,
      decoration: InputDecoration(
        labelText: 'Username',
        hintText: 'Choose a unique username',
        prefixIcon: const Icon(Icons.alternate_email),
        border: const OutlineInputBorder(),
        counterText: "",
        // Suffix Icon for Loading/Status
        suffixIcon: state.isUsernameChecking
            ? Container(
                padding: const EdgeInsets.all(12.0),
                child: const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : (state.username != null &&
                    state.username!.length >= 3 &&
                    !state.username!.contains(' ') &&
                    RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(state.username!) &&
                    state.usernameError == null)
                ? const Icon(Icons.check_circle, color: Colors.green)
                : (state.usernameError != null && !state.isUsernameChecking)
                    ? const Icon(Icons.error, color: Colors.red)
                    : null,
        // Error Text from ViewModel
        errorText: state.usernameError,
      ),
      maxLength: 20,
      keyboardType: TextInputType.text,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_]')),
      ],
      autocorrect: false,
      validator: (value) {
        // Basic format validation
        if (value == null || value.isEmpty) {
          return 'Please enter a username';
        }
        if (value.length < 3) {
          return 'Username must be at least 3 characters';
        }
        if (value.length > 20) {
          return 'Username cannot exceed 20 characters';
        }
        if (value.contains(' ')) {
          return 'Username cannot contain spaces';
        }
        if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
          return 'Only letters, numbers, and underscores allowed';
        }
        // ViewModel handles uniqueness check and sets state.usernameError
        return state.usernameError;
      },
      textInputAction: TextInputAction.next,
      onChanged: (value) {
        // This triggers validation in VM
        viewModel.updateUsername(value);
      },
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'fullName': fullNameController.text,
      'username': usernameController.text,
    };
  }

  /// Check if we can proceed to the next page
  bool _canProceedToNext() {
    return !state.isUsernameChecking &&
        state.usernameError == null &&
        state.username != null &&
        state.username!.isNotEmpty &&
        state.username!.length >= 3 &&
        !state.username!.contains(' ') &&
        state.fullName != null &&
        state.fullName!.isNotEmpty;
  }
}
