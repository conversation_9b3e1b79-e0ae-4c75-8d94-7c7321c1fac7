// lib/features/onboarding/pages/my_interests_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/features/profile/widgets/interests_selector_widget.dart';

/// The page for selecting personal interests/keywords in the onboarding flow.
/// Uses the existing InterestsSelectorWidget for consistent UX.
class MyInterestsPage extends OnboardingPageBase {
  const MyInterestsPage({
    Key? key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function({required Map<String, dynamic> Function() getUpdatedValues}) onApplyEdits,
    required Future<void> Function() onCancelEdits,
  }) : super(
          key: key,
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Your Interests',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Add keywords that describe your interests, hobbies, and passions. This helps others discover you based on shared interests!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Interests Selector Widget
          Expanded(
            child: SingleChildScrollView(
              child: InterestsSelectorWidget(
                initialKeywords: editContext.isEditing
                    ? _getOriginalMyInterests(context)
                    : (state.myInterests ?? []),
                onKeywordsChanged: (updatedKeywords) {
                  // Update the ViewModel's state
                  print("MyInterestsPage: Interests updated - ${updatedKeywords.length} selected");
                  viewModel.updateMyInterests(updatedKeywords);
                },
                maxTotalKeywords: 5, // Standard limit for onboarding
                maxCustomKeywords: 2, // Allow some custom interests
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Helpful information text
          Text(
            "💡 Tip: Mix predefined interests with your own custom ones for the best matching experience!",
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Reminder text
          Text(
            "You can update your interests anytime in your profile settings.",
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Navigation Buttons
          buildNavigationButtons(
            context: context,
            currentPage: state.currentPage,
            previousPage: state.currentPage - 1,
            nextPage: state.currentPage + 1,
            canProceedToNext: true, // Always allow navigation - interests are optional
            onNextPressed: () async {
              if (editContext.isEditing) {
                // Apply edits and return to summary
                await onApplyEdits(getUpdatedValues: () => collectFieldValues(context));
              } else {
                // Standard next page navigation - go to summary
                viewModel.nextPage();
                await onNavigate(state.currentPage + 1);
              }
            },
            onBackPressed: () async {
              if (editContext.isEditing) {
                // Revert changes and cancel edits
                await _revertMyInterestsChanges(context);
                await onCancelEdits();
              } else {
                // Standard back page navigation
                viewModel.previousPage();
                await onNavigate(state.currentPage - 1);
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'myInterests': state.myInterests,
    };
  }

  /// Revert interests changes when canceling edit mode
  Future<void> _revertMyInterestsChanges(BuildContext context) async {
    final originalValues = editContext.originalValues;
    final originalInterests = originalValues['myInterests'] as List<String>?;

    // Revert to original values
    if (originalInterests != null) {
      viewModel.updateMyInterests(originalInterests);
    } else {
      // Clear interests if nothing was originally set
      viewModel.updateMyInterests([]);
    }
  }

  /// Get original interests for edit mode
  List<String> _getOriginalMyInterests(BuildContext context) {
    final originalInterests = editContext.originalValues['myInterests'] as List<String>?;
    return originalInterests ?? [];
  }
}
