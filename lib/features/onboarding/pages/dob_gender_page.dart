// lib/features/onboarding/pages/dob_gender_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/widgets/date_picker_widget.dart';
import 'package:watermelon_draft/widgets/gender_selector_widget.dart';

/// The page for entering date of birth and gender in the onboarding flow.
class DobGenderPage extends OnboardingPageBase {
  /// The form key for validating the form
  final GlobalKey<FormState> formKey;

  /// The currently selected date of birth
  final DateTime? selectedDate;

  /// Callback when a date is selected
  final Function(DateTime?) onDateSelected;

  const DobGenderPage({
    Key? key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
    required this.formKey,
    required this.selectedDate,
    required this.onDateSelected,
  }) : super(
          key: key,
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    // Store validation function for DOB picker
    String? Function()? dobValidationFunction;

    return Form(
      key: formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'About You',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'We only use your birthdate to calculate your age and will not display your date of birth publicly.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 32),

            // Date of Birth Picker
            DateOfBirthPicker(
              selectedDate: selectedDate ?? state.birthdate,
              onDateSelected: (date) {
                onDateSelected(date);
                viewModel.updateBirthdate(date);
              },
              onValidationReady: (validationFn) {
                dobValidationFunction = validationFn;
              },
            ),
            const SizedBox(height: 32),

            // Gender Selector
            SimpleGenderSelector(
              selectedGender: state.gender,
              onGenderSelected: (gender) {
                viewModel.updateGender(gender);
              },
              validator: (gender) {
                if (gender == null || gender.isEmpty) {
                  return 'Please select your gender';
                }
                return null;
              },
            ),

            const Spacer(), // Push buttons to bottom

            // Navigation Buttons
            buildNavigationButtons(
              context: context,
              currentPage: state.currentPage,
              previousPage: state.currentPage - 1,
              nextPage: state.currentPage + 1,
              canProceedToNext: _canProceedToNext(),
              onNextPressed: () async {
                // Dismiss keyboard before validation/navigation
                FocusScope.of(context).unfocus();

                // Validate DOB picker (this will mark it as touched and show errors)
                final dobError = dobValidationFunction?.call();

                // Validate form (mainly for gender selection)
                final bool isFormValid =
                    formKey.currentState?.validate() ?? false;

                // Check if we have valid data (no errors)
                final bool hasValidData =
                    dobError == null && _canProceedToNext();

                if (isFormValid && hasValidData) {
                  if (editContext.isEditing) {
                    // Apply edits and return to summary
                    await onApplyEdits(
                        getUpdatedValues: () => collectFieldValues(context));
                  } else {
                    // Standard next page navigation
                    viewModel.nextPage();
                    await onNavigate(state.currentPage + 1);
                  }
                } else {
                  // Show error message if validation fails
                  if (!hasValidData) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Please complete all required fields with valid information'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'birthdate': selectedDate ?? state.birthdate,
      'gender': state.gender,
    };
  }

  /// Check if we can proceed to the next page
  bool _canProceedToNext() {
    final dateToCheck = selectedDate ?? state.birthdate;
    return dateToCheck != null &&
        state.gender != null &&
        state.gender!.isNotEmpty &&
        _isValidAge(dateToCheck);
  }

  /// Check if the selected date represents a valid age
  bool _isValidAge(DateTime birthdate) {
    final now = DateTime.now();
    final age = now.year -
        birthdate.year -
        ((now.month < birthdate.month ||
                (now.month == birthdate.month && now.day < birthdate.day))
            ? 1
            : 0);
    return age >= 13 && !birthdate.isAfter(now);
  }
}
