// lib/features/onboarding/pages/dob_gender_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/features/onboarding/widgets/onboarding_page_base.dart';
import 'package:watermelon_draft/widgets/date_picker_widget.dart';
import 'package:watermelon_draft/widgets/gender_selector_widget.dart';

/// The page for entering date of birth and gender in the onboarding flow.
class DobGenderPage extends OnboardingPageBase {
  /// The form key for validating the form
  final GlobalKey<FormState> formKey;

  /// The currently selected date of birth
  final DateTime? selectedDate;

  /// Callback when a date is selected
  final Function(DateTime?) onDateSelected;

  const DobGenderPage({
    Key? key,
    required OnboardingState state,
    required OnboardingViewModel viewModel,
    required EditContext editContext,
    required Future<void> Function(int pageIndex) onNavigate,
    required Future<void> Function(
            {required Map<String, dynamic> Function() getUpdatedValues})
        onApplyEdits,
    required Future<void> Function() onCancelEdits,
    required this.formKey,
    required this.selectedDate,
    required this.onDateSelected,
  }) : super(
          key: key,
          state: state,
          viewModel: viewModel,
          editContext: editContext,
          onNavigate: onNavigate,
          onApplyEdits: onApplyEdits,
          onCancelEdits: onCancelEdits,
        );

  @override
  Widget buildPageContent(BuildContext context, WidgetRef ref) {
    return Form(
      key: formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'About You',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'We only use your birthdate to calculate your age and will not display your date of birth publicly.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 32),

            // Date of Birth Picker
            DateOfBirthPicker(
              selectedDate: selectedDate ?? state.birthdate,
              onDateSelected: (date) {
                onDateSelected(date);
                viewModel.updateBirthdate(date);
              },
            ),
            const SizedBox(height: 32),

            // Gender Selector
            SimpleGenderSelector(
              selectedGender: state.gender,
              onGenderSelected: (gender) {
                viewModel.updateGender(gender);
              },
              validator: (gender) {
                if (gender == null || gender.isEmpty) {
                  return 'Please select your gender';
                }
                return null;
              },
            ),

            const Spacer(), // Push buttons to bottom

            // Navigation Buttons
            buildNavigationButtons(
              context: context,
              currentPage: state.currentPage,
              previousPage: state.currentPage - 1,
              nextPage: state.currentPage + 1,
              canProceedToNext:
                  true, // Always allow navigation - validation happens at summary
              onNextPressed: () async {
                // Dismiss keyboard before navigation
                FocusScope.of(context).unfocus();

                if (editContext.isEditing) {
                  // Apply edits and return to summary
                  await onApplyEdits(
                      getUpdatedValues: () => collectFieldValues(context));
                } else {
                  // Standard next page navigation - no validation blocking
                  viewModel.nextPage();
                  await onNavigate(state.currentPage + 1);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Map<String, dynamic> collectFieldValues(BuildContext context) {
    return {
      'birthdate': selectedDate ?? state.birthdate,
      'gender': state.gender,
    };
  }
}
