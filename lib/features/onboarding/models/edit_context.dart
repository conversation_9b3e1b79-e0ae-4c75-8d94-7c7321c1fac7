// lib/features/onboarding/models/edit_context.dart
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';

/// A class to manage the edit context for the onboarding flow.
/// This consolidates all edit-related state into a single object.
class EditContext {
  /// Whether the user is currently editing from the summary page
  final bool isEditing;
  
  /// The original values before editing, used for cancellation
  final Map<String, dynamic> originalValues;
  
  /// The page index where editing started (usually the summary page)
  final int sourcePageIndex;
  
  /// The target page index for editing
  final int targetPageIndex;

  const EditContext({
    this.isEditing = false,
    Map<String, dynamic>? originalValues,
    this.sourcePageIndex = 0,
    this.targetPageIndex = 0,
  }) : originalValues = originalValues ?? const {};

  /// Create a copy of this EditContext with some fields replaced
  EditContext copyWith({
    bool? isEditing,
    Map<String, dynamic>? originalValues,
    int? sourcePageIndex,
    int? targetPageIndex,
  }) {
    return EditContext(
      isEditing: isEditing ?? this.isEditing,
      originalValues: originalValues ?? Map.from(this.originalValues),
      sourcePageIndex: sourcePageIndex ?? this.sourcePageIndex,
      targetPageIndex: targetPageIndex ?? this.targetPageIndex,
    );
  }

  /// Create an EditContext for editing a specific field
  static EditContext forField({
    required int targetPageIndex, 
    required int sourcePageIndex,
    required Map<String, dynamic> originalValues,
  }) {
    return EditContext(
      isEditing: true,
      originalValues: originalValues,
      sourcePageIndex: sourcePageIndex,
      targetPageIndex: targetPageIndex,
    );
  }

  /// Reset the edit context when returning to the source page
  EditContext reset() {
    return EditContext(
      isEditing: false,
      originalValues: {},
      sourcePageIndex: 0,
      targetPageIndex: 0,
    );
  }

  /// Helper method to get a typed value from originalValues
  T? getValue<T>(String key) {
    final value = originalValues[key];
    if (value is T) {
      return value;
    }
    return null;
  }

  /// Get string value with fallback
  String getStringValue(String key, {String defaultValue = ''}) {
    return getValue<String>(key) ?? defaultValue;
  }

  /// Get DateTime value
  DateTime? getDateTimeValue(String key) {
    return getValue<DateTime>(key);
  }

  /// Get XFile value
  XFile? getXFileValue(String key) {
    return getValue<XFile>(key);
  }

  /// Get Color value
  Color? getColorValue(String key) {
    return getValue<Color>(key);
  }

  /// Get GeoPoint value
  GeoPoint? getGeoPointValue(String key) {
    return getValue<GeoPoint>(key);
  }

  /// Get List of String value
  List<String>? getStringListValue(String key) {
    return getValue<List<String>>(key);
  }
}
