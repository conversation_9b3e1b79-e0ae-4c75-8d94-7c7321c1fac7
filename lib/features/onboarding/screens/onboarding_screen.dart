// lib/features/onboarding/screens/onboarding_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';
import 'package:watermelon_draft/widgets/profile_image_picker.dart';
import 'package:beamer/beamer.dart';
import 'package:watermelon_draft/widgets/city_selector_widget.dart';
import 'package:watermelon_draft/features/profile/widgets/interests_selector_widget.dart';
import 'package:watermelon_draft/features/profile/widgets/shared_activities_selector_widget.dart';
import 'package:watermelon_draft/core/utils/avatar_utils.dart';
import 'package:watermelon_draft/widgets/full_name_input_widget.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  final PageController _pageController = PageController();
  // --- Form keys ---
  final _nameUsernameFormKey = GlobalKey<FormState>();
  // --- Controllers ---
  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _cityController = TextEditingController();
  // --- Other Local State ---
  DateTime? _selectedDate;

  // --- For Edit from Summary logic ---
  bool _isEditingFromSummary = false;
  final int _summaryPageIndex = 7;
  // Assuming: 0:Welcome, 1:Name/User, 2:DOB/Gender, 3:Pic, 4:Location, 5:SharedActs, 6:Interests, 7:Summary
  final int _welcomePageIndex = 0;
  final int _nameUsernamePageIndex = 1;
  final int _dobGenderPageIndex = 2;
  final int _profilePicPageIndex = 3;
  final int _locationPageIndex = 4;
  final int _sharedActivitiesPageIndex = 5;
  final int _myInterestsPageIndex = 6;

// --- Variables to store original state before editing ---
  String? _originalAvatarType;
  XFile? _originalProfileImage;
  String? _originalDefaultAvatarPath;
  Color? _originalGeneratedAvatarColor;

  String? _originalFullName;
  String? _originalUsername;
  DateTime? _originalBirthdate;
  String? _originalGender;
  String? _originalCity;
  List<String>? _originalSharedActivities;
  List<String>? _originalMyInterests;
  // String? _originalCountry;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing values from ViewModel's *initial synchronous state*
    // The provider.future is for ASYNC build. If build is sync after prefs, value is available.
    final initialState = ref.read(onboardingViewModelProvider).valueOrNull;
    if (initialState != null) {
      _fullNameController.text = initialState.fullName ?? '';
      _usernameController.text = initialState.username ?? '';
      _selectedDate = initialState.birthdate;
      _cityController.text = initialState.city ?? '';
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fullNameController.dispose();
    _usernameController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  // --- Modified navigation helpers to potentially reset edit flag ---
  Future<void> _animateToPage(int pageIndex) async {
    // Update the ViewModel's current page
    ref.read(onboardingViewModelProvider.notifier).setCurrentPage(pageIndex);

    // Add a small delay to allow state updates to propagate
    if (_isEditingFromSummary && pageIndex == _summaryPageIndex) {
      // Only add delay when returning to summary from edit mode
      await Future.delayed(Duration(milliseconds: 50));
    }

    if (mounted) {
      _pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      // If we are navigating TO the summary page, reset the editing flag
      if (pageIndex == _summaryPageIndex && _isEditingFromSummary) {
        setState(() {
          _isEditingFromSummary = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<OnboardingState>>(onboardingViewModelProvider,
        (previous, next) {
      final currentOnboardingState = next.valueOrNull;
      // Check if saveError exists and has changed (to avoid showing multiple SnackBars for same error)
      if (currentOnboardingState?.saveError != null &&
          (previous?.valueOrNull?.saveError !=
                  currentOnboardingState!.saveError ||
              previous?.valueOrNull?.isSaving == true &&
                  currentOnboardingState.isSaving ==
                      false) && // Or if saving just finished with an error
          mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(currentOnboardingState
                  .saveError!.message), // saveError is Failure?
              backgroundColor: Colors.red),
        );
      }
    });

    // Watch the state to rebuild the UI
    final stateAsync = ref.watch(onboardingViewModelProvider);
    final OnboardingViewModel viewModel =
        ref.read(onboardingViewModelProvider.notifier);

    // Determine currentPage for AppBar logic safely
    final int currentPage = stateAsync.valueOrNull?.currentPage ?? 0;
    final int totalPages = 8; // WELCOME + 6 STEPS + SUMMARY = 8 pages (0-7)

    return Scaffold(
      appBar: AppBar(
        title: Text(_getStepTitle(currentPage)),
        automaticallyImplyLeading: false,

        // Show progress indicator in AppBar
        bottom: PreferredSize(
          // Use PreferredSize for LinearProgressIndicator in AppBar bottom
          preferredSize: Size(
              double.infinity, 4.0), // Standard height for progress indicator
          child: switch (stateAsync) {
            // Use switch for AppBar's progress too
            AsyncLoading() =>
              const LinearProgressIndicator(value: null), // Indeterminate
            AsyncData(:final value) => LinearProgressIndicator(
                value: (value.currentPage + 1) / totalPages,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor),
              ),
            _ => const SizedBox(
                height: 4.0), // Empty space for error or other states
          },
        ),
      ),

      // Use pattern matching (switch) on the AsyncValue state
      body: switch (stateAsync) {
        AsyncData(:final value) => // value is OnboardingState
          PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: <Widget>[
              _buildWelcomePage(value, viewModel, _isEditingFromSummary),
              _buildNameUsernamePage(value, viewModel, _isEditingFromSummary),
              _buildDobGenderPage(value, viewModel, _isEditingFromSummary),
              _buildProfilePicturePage(value, viewModel, _isEditingFromSummary),
              _buildLocationPage(value, viewModel, _isEditingFromSummary),
              _buildSharedActivitiesPage(
                  value, viewModel, _isEditingFromSummary),
              _buildMyInterestsPage(value, viewModel, _isEditingFromSummary),
              _buildSummaryPage(value, viewModel),
            ],
          ),
        AsyncError(:final error) => Center(
              // Handle provider-level error
              child: Padding(
            padding: const EdgeInsets.all(16.0),
            child:
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text(
                  'Error loading onboarding: ${error is Failure ? error.message : error.toString()}'),
              const SizedBox(height: 10),
              ElevatedButton(
                  onPressed: () => ref.invalidate(onboardingViewModelProvider),
                  child: const Text("Retry"))
            ]),
          )),
        _ => const Center(
            child:
                CircularProgressIndicator()), // Handles AsyncLoading for the whole body
      },
    );
  }

  // --- Page 1: Welcome Page ---
  Widget _buildWelcomePage(OnboardingState state, OnboardingViewModel viewModel,
      bool isEditingFromSummary) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Welcome to Watermelon!', style: TextStyle(fontSize: 24)),
          SizedBox(height: 20),
          Text('Connect with others through shared activities.',
              textAlign: TextAlign.center),
          SizedBox(height: 40),
          ElevatedButton(
            onPressed: () {
              // viewModel.nextPage(); // Call this in ViewModel
              _pageController.nextPage(
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut);
            },
            child: Text('Get Started'),
          ),
        ],
      ),
    );
  }

  // --- Page 2: Full name and Username Page ---
  Widget _buildNameUsernamePage(OnboardingState onboardingState,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Form(
      key:
          _nameUsernameFormKey, // Use the key defined in _OnboardingScreenState
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Your Name & Username',
                style: Theme.of(context).textTheme.headlineSmall),
            SizedBox(height: 8),
            const Text(
                'Let\'s start with the basics. Your username must be unique.'),
            const SizedBox(height: 24),

            // --- Full Name Widget ---
            FullNameInputWidget(
              controller: _fullNameController,
              onChanged: (value) => viewModel.updateFullName(value),
            ),
            SizedBox(height: 16),

            // --- Username Input ---
            TextFormField(
              controller: _usernameController,
              decoration: InputDecoration(
                labelText: 'Username',
                hintText: 'Choose a unique username',
                prefixIcon: Icon(Icons.alternate_email),
                border: OutlineInputBorder(),
                counterText: "",
                // --- Suffix Icon for Loading/Status ---
                suffixIcon: onboardingState.isUsernameChecking
                    ? Container(
                        padding: EdgeInsets.all(12.0),
                        child: SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : (onboardingState.username != null &&
                            onboardingState.username!.length >= 3 &&
                            !onboardingState.username!.contains(' ') &&
                            RegExp(r'^[a-zA-Z0-9_]+$')
                                .hasMatch(onboardingState.username!) &&
                            onboardingState.usernameError == null)
                        // Show check if valid and available
                        ? Icon(Icons.check_circle, color: Colors.green)
                        : (onboardingState.usernameError != null &&
                                !onboardingState
                                    .isUsernameChecking) // Check !isUsernameChecking here too
                            ? Icon(Icons.error,
                                color: Colors
                                    .red) // Show error if taken/failed check
                            : null,
                // --- Error Text from ViewModel ---
                errorText: onboardingState
                    .usernameError, // Display errors from onboardingState here
              ),
              maxLength: 20,
              keyboardType: TextInputType.text,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_]')),
              ],
              autocorrect: false,
              validator: (value) {
                // Basic format validation ONLY
                if (value == null || value.isEmpty) {
                  return 'Please enter a username';
                }
                if (value.length < 3) {
                  return 'Username must be at least 3 characters';
                }
                if (value.length > 20) {
                  // Consistent with maxLength
                  return 'Username cannot exceed 20 characters';
                }
                if (value.contains(' ')) {
                  return 'Username cannot contain spaces';
                }
                if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
                  return 'Only letters, numbers, and underscores allowed';
                }
                // ViewModel handles uniqueness check and sets onboardingState.usernameError
                return onboardingState
                    .usernameError; // Also display server-side error here
              },
              textInputAction: TextInputAction.next,
              onChanged: (value) {
                // This triggers validation in VM
                viewModel.updateUsername(value);
              },
            ),

            // --- Display Loading/Status Text Indicator ---
            // Padding(
            //   // Add some padding
            //   padding: const EdgeInsets.only(top: 8.0),
            //   child: SizedBox(
            //     // Use SizedBox to reserve space even when empty
            //     height: 20, // Adjust height as needed
            //     child: onboardingState.isUsernameChecking
            //         ? Row(
            //             // Show loading indicator and text
            //             children: [
            //               SizedBox(
            //                   width: 16, // Smaller indicator
            //                   height: 16,
            //                   child: CircularProgressIndicator(strokeWidth: 2)),
            //               SizedBox(width: 8),
            //               Text("Checking availability...",
            //                   style:
            //                       TextStyle(fontSize: 12, color: Colors.grey)),
            //             ],
            //           )
            //         : (onboardingState.username !=
            //                     null && // Check basic format validity again for showing success
            //                 onboardingState.username!.length >= 3 &&
            //                 !onboardingState.username!.contains(' ') &&
            //                 RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(onboardingState
            //                     .username!) && // Check valid characters
            //                 onboardingState.usernameError ==
            //                     null) // AND no error from VM check
            //             ? Row(
            //                 // Show success message
            //                 children: [
            //                   Icon(Icons.check_circle,
            //                       color: Colors.green, size: 16),
            //                   SizedBox(width: 8),
            //                   Text("Username available",
            //                       style: TextStyle(
            //                           color: Colors.green, fontSize: 12)),
            //                 ],
            //               )
            //             : SizedBox
            //                 .shrink(), // Show nothing if initial/invalid format/not checking/no error
            //   ),
            // ),

            Spacer(), // Push buttons to bottom

            // --- Action Buttons ---
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // --- Back / Cancel Button ---
                TextButton(
                  onPressed: () {
                    if (isEditingFromSummary) {
                      // --- Revert ViewModel state to original values ---
                      viewModel.updateFullName(_originalFullName ?? '');
                      viewModel.updateUsername(_originalUsername ??
                          ''); // This will clear usernameError in VM

                      // --- ALSO Reset local TextEditingControllers ---
                      _fullNameController.text = _originalFullName ?? '';
                      _usernameController.text = _originalUsername ?? '';
                      // ---

                      _animateToPage(
                          _summaryPageIndex); // Navigates to Summary & resets _isEditingFromSummary flag
                    } else {
                      // Standard "Back" logic
                      viewModel.previousPage();
                      _animateToPage(
                          _welcomePageIndex); // Or onboardingState.currentPage - 1
                    }
                  },
                  child: Text(isEditingFromSummary ? 'Cancel' : 'Back'),
                ),

                // --- Next / Done Button ---
                // ElevatedButton(
                //   // Perform validation for this step
                //   // Disable Next if username is empty, too short, contains space, checking, or taken
                //   onPressed: onboardingState
                //               .isUsernameChecking || // Disable while checking
                //           onboardingState.usernameError !=
                //               null || // Disable if error (taken/check failed)
                //           onboardingState.username == null ||
                //           onboardingState
                //               .username!.isEmpty || // Disable if empty
                //           onboardingState.username!.length <
                //               3 || // Disable if too short
                //           onboardingState.username!
                //               .contains(' ') || // Disable if contains space
                //           onboardingState.fullName == null ||
                //           onboardingState.fullName!
                //               .isEmpty // Disable if full name is empty
                //       ? null
                //       : () {
                //           // Only need to validate the format locally, availability is in state
                //           if (_nameUsernameFormKey.currentState!.validate()) {
                //             // Final validation just in case
                //             viewModel.nextPage();
                //             _animateToPage(onboardingState.currentPage + 1);
                //           }
                //         },
                //   child: Text(isEditingFromSummary ? 'Update' : 'Next'),
                // ),

                ElevatedButton(
                  onPressed: () async {
                    // Perform validation for this step
                    FocusScope.of(context)
                        .unfocus(); // Dismiss keyboard before validation/navigation

                    final bool isFormValid =
                        _nameUsernameFormKey.currentState?.validate() ?? false;
                    // Read the LATEST state from viewmodel AFTER validation might have triggered updates from TextFormFields
                    final currentOnboardingState =
                        ref.read(onboardingViewModelProvider).valueOrNull;
                    final bool isUsernameAvailable =
                        (currentOnboardingState?.usernameError == null &&
                            !(currentOnboardingState?.isUsernameChecking ??
                                true)); // Ensure not checking

                    final bool canProceed = isFormValid && isUsernameAvailable;

                    if (canProceed) {
                      if (isEditingFromSummary) {
                        // Ensure the ViewModel has the absolute latest from controllers before navigating
                        viewModel.updateFullName(_fullNameController.text);
                        viewModel.updateUsername(_usernameController.text);

                        // Yield to the event loop to allow Riverpod to process state updates
                        // Use a slightly longer delay to ensure state updates are processed
                        await Future.delayed(Duration(milliseconds: 100));

                        if (mounted) {
                          // Check mounted AFTER the delay
                          await _animateToPage(_summaryPageIndex);
                        }
                      } else {
                        viewModel.nextPage();
                        await _animateToPage(
                            currentOnboardingState?.currentPage != null
                                ? currentOnboardingState!.currentPage + 1
                                : onboardingState.currentPage +
                                    1); // Use latest currentPage
                      }
                    } else {
                      print(
                          "Onboarding: Name/Username validation failed or username not available for 'Update'/'Next'.");
                      // Optionally show a general SnackBar if needed, though field errors should be visible
                    }
                  },
                  child: Text(isEditingFromSummary ? 'Update' : 'Next'),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  // --- Page 3: Age and Gender Page ---
  Widget _buildDobGenderPage(OnboardingState state,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center, // Center content
              children: [
                Text(
                  'Date of birth. We only use your birthdate to calculate your age and will not display your date of birth.',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),

                // --- Date Picker ---
                GestureDetector(
                  onTap: () async {
                    final DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate ?? DateTime.now(),
                        firstDate: DateTime(1900),
                        lastDate: DateTime.now());
                    if (pickedDate != null && pickedDate != _selectedDate) {
                      setState(() {
                        _selectedDate = pickedDate;
                      }); // Update local display
                      viewModel.updateBirthdate(pickedDate); // Update ViewModel
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'Date of Birth',
                      border: OutlineInputBorder(),
                    ),
                    child: Text(
                      _selectedDate == null
                          ? 'Select Date'
                          : '${_selectedDate!.toLocal()}'.split(' ')[0],
                    ),
                  ),
                ),
                SizedBox(height: 24),

                // --- Gender Selection ---
                Text('Gender', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(height: 8),
                Column(
                  // Wrap RadioListTiles in a Column
                  children: <Widget>[
                    RadioListTile<String>(
                      title: const Text('Male'),
                      value: 'Male',
                      groupValue: state
                          .gender, // The currently selected value from ViewModel
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel
                              .updateGender(value); // Update ViewModel state
                        }
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Female'),
                      value: 'Female',
                      groupValue: state.gender,
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel.updateGender(value);
                        }
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Other'),
                      value: 'Other',
                      groupValue: state.gender,
                      onChanged: (String? value) {
                        if (value != null) {
                          viewModel.updateGender(value);
                        }
                      },
                    ),
                    // RadioListTile<String>(
                    //   title: const Text('Prefer not to say'),
                    //   value: 'Prefer not to say',
                    //   groupValue: state.gender,
                    //   onChanged: (String? value) {
                    //     if (value != null) {
                    //       viewModel.updateGender(value);
                    //     }
                    //   },
                    // ),
                  ],
                ),
                // Spacer(), // Push buttons to bottom
              ],
            ),
          ),
        ),
        // --- Navigation Buttons ---
        // Spacer(), // Push buttons to bottom
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    // Navigate back to Name/Username (Index 1)
                    viewModel.previousPage();
                    _pageController.previousPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut);
                  },
                  child: Text('Back'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Validate *both* fields on this page
                    bool dateSelected = _selectedDate != null;
                    bool genderSelected =
                        state.gender != null && state.gender!.isNotEmpty;

                    if (!dateSelected) {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text("Please select your birthdate.")));
                      return; // Stop if date is missing
                    }
                    if (!genderSelected) {
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text("Please select your gender.")));
                      return; // Stop if gender is missing
                    }

                    // If both are selected, proceed
                    viewModel.nextPage();
                    _pageController.nextPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut);
                  },
                  child: Text('Next'),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  // --- Page 4: Profile Picture Page ---
  Widget _buildProfilePicturePage(OnboardingState state,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Profile Picture',
              style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          const Text(
              'Choose how your profile picture will appear. A picture is required.'),
          const SizedBox(height: 24),
          Expanded(
            child: Center(
              child: ProfileImagePicker(
                // Pass initial values from ViewModel state
                initialImageFile: _isEditingFromSummary
                    ? _originalProfileImage
                    : state.profileImage,
                initialDefaultAvatarPath: _isEditingFromSummary
                    ? _originalDefaultAvatarPath
                    : state.defaultAvatar,
                nameForGeneratedAvatar: state.fullName ?? 'Unknown',
                initialAvatarType: _isEditingFromSummary
                    ? _originalAvatarType
                    : state.avatarType,
                initialGeneratedColor: _isEditingFromSummary
                    ? _originalGeneratedAvatarColor
                    : state.generatedAvatarColor,

                // Connect callbacks to ViewModel methods
                onImageSelected: (image) {
                  viewModel.updateProfileImage(image);
                },
                onDefaultAvatarSelected: (path) {
                  viewModel.updateDefaultAvatar(path);
                },
                onGeneratedAvatarSelected: (color) {
                  viewModel.selectGeneratedAvatar(color);
                },
                onGeneratedAvatarColorRegenerated: (newColor) {
                  viewModel.updateGeneratedAvatarColor(newColor);
                },

                updateButtonText: isEditingFromSummary ? 'Update' : 'Next',
                cancelButtonText: isEditingFromSummary ? 'Cancel' : 'Back',
                onUpdate: () {
                  // This is the "Next" or "Update" button
                  if (state.avatarType != null) {
                    if (isEditingFromSummary) {
                      // Navigate back to summary and reset edit mode.
                      _animateToPage(_summaryPageIndex);
                    } else {
                      viewModel.nextPage(); // Go to next onboarding step
                      _animateToPage(state.currentPage + 1);
                    }
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content:
                            Text('Please select a profile picture option.'),
                      ),
                    );
                  }
                },
                onBack: () {
                  // This is the "Back" or "Cancel" button
                  if (isEditingFromSummary) {
                    // --- Revert changes in ViewModel state ---
                    print("Cancelling edit, reverting avatar state.");
                    if (_originalAvatarType == 'uploaded' &&
                        _originalProfileImage != null) {
                      viewModel.updateProfileImage(_originalProfileImage);
                    } else if (_originalAvatarType == 'default' &&
                        _originalDefaultAvatarPath != null) {
                      viewModel.updateDefaultAvatar(_originalDefaultAvatarPath);
                    } else if (_originalAvatarType == 'generated' &&
                        _originalGeneratedAvatarColor != null) {
                      viewModel.selectGeneratedAvatar(
                          _originalGeneratedAvatarColor!);
                    } else {
                      // Fallback if original was null or inconsistent (e.g., clear selection)
                      viewModel.updateProfileImage(
                          null); // or a method to clear avatar state
                    }
                    // ---
                    _animateToPage(_summaryPageIndex);
                    // setState(() {
                    //   _isEditingFromSummary = false; // Reset flag
                    // });
                  } else {
                    viewModel.previousPage();
                    _animateToPage(state.currentPage - 1);
                  }
                },
              ),
            ),
          ),
          Text(
            // Add reminder text
            "You can change your profile picture later in your profile settings.",
            style: TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center, // Center the text
          ),
        ],
      ),
    );
  }

  // --- Page 5: Location Page ---
  Widget _buildLocationPage(OnboardingState state,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Set Your Location',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text(
            'Watermelon uses your location to help you find connections and activities nearby.',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.left,
          ),
          SizedBox(height: 24),

          // --- Use the Reusable CitySelectorWidget ---
          CitySelectorWidget(
            initialCity: state.city,
            initialLocation: state.location,
            initialCountry: state.country,
            onLocationSet: (location, city, country) {
              // This callback updates the ViewModel's state
              // The CitySelectorWidget handles getting location/city/country
              if (location != null) {
                // Prefer GPS location if available, update all fields
                viewModel.updateLocationFromPoint(location);
              } else if (city != null) {
                // Fallback to city name if GPS failed or wasn't used
                // This method in the VM should also update the country
                viewModel.updateLocationFromAddress(city);
              }
              // If both are null, the ViewModel state remains unchanged for location/city/country
            },
          ),

          Spacer(), // Push buttons to bottom

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage();
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                // --- Validation for Next Button ---
                onPressed: (state.location != null ||
                        (state.city != null && state.city!.isNotEmpty))
                    ? () {
                        // Enabled if location OR city is set in the ViewModel state
                        viewModel.nextPage();
                        _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut);
                      }
                    : null, // Disable button if neither location nor city is set
                child: Text('Next'),
              ),
            ],
          )
        ],
      ),
    );
  }

// --- Page 6: Shared Activities Page ---
  Widget _buildSharedActivitiesPage(OnboardingState state,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // Align content to start
        children: [
          Text('Select Activities to Share',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text(
            "Select at least one activity you'd like to share with others. You can add up to 5. Drag to reorder.",
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          SizedBox(height: 16),

          // --- Use the Reusable Widget ---
          Expanded(
            // Allow the selector to take available space
            child: SingleChildScrollView(
              // Make the content scrollable if needed
              child: SharedActivitiesSelectorWidget(
                initialActivities: state.sharedActivities ??
                    [], // Pass initial selection from ViewModel
                onActivitiesChanged: (updatedActivities) {
                  // Callback updates the ViewModel
                  viewModel.updateSharedActivities(updatedActivities);
                },
                // maxActivities: 5, // Default is 5, explicitly pass if needed
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            // Add reminder text
            "You can add, remove, or change these activities to share later in your profile settings.",
            style: TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center, // Center the text
          ),

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                // Changed to TextButton for Back
                onPressed: () {
                  viewModel.previousPage(); // Goes back to Location (Index 4)
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                // Disable "Next" if no activities are selected
                onPressed: (state.sharedActivities?.isNotEmpty ?? false)
                    ? () {
                        viewModel
                            .nextPage(); // Goes forward to My Interests (Index 6)
                        _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut);
                      }
                    : null, // Disable if empty
                child: Text('Next'),
              ),
            ],
          )
        ],
      ),
    );
  }

  // --- Page 7: My Interests Page ---
  Widget _buildMyInterestsPage(OnboardingState state,
      OnboardingViewModel viewModel, bool isEditingFromSummary) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('My Interests',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            'Select some interests to help others connect with you. You can always change these later in your profile settings.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          Expanded(
            // Make the selector scrollable if needed
            child: SingleChildScrollView(
              child: InterestsSelectorWidget(
                initialKeywords: state.myInterests ?? [],
                onKeywordsChanged: (updatedKeywords) {
                  viewModel.updateMyInterests(updatedKeywords);
                },
              ),
            ),
          ),
          const SizedBox(height: 24),

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage(); // Go back to Shared Activities
                  _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: const Text('Back'),
              ),
              TextButton(
                onPressed: () {
                  viewModel.nextPage(); // Go next to SummaryPage
                  _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text("Skip for now"),
              ),
              ElevatedButton(
                onPressed: () {
                  viewModel.nextPage(); // Go next to SummaryPage
                  _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut);
                },
                child: Text('Next'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // --- Page 8: Summary Page ---
  Widget _buildSummaryPage(
      OnboardingState state, OnboardingViewModel viewModel) {
    return Padding(
      // Added Padding
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // Align content left
        children: [
          Text('Summary', style: Theme.of(context).textTheme.headlineSmall),
          SizedBox(height: 16),

          // --- Profile Picture Display ---
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: InkWell(
              onTap: () {
                // Get current state from ViewModel
                final currentOnboardingState =
                    ref.read(onboardingViewModelProvider).valueOrNull;
                if (currentOnboardingState == null) return;

                setState(() {
                  _isEditingFromSummary = true;
                  // Stash original values ---
                  _originalAvatarType = currentOnboardingState.avatarType;
                  _originalProfileImage = currentOnboardingState.profileImage;
                  _originalDefaultAvatarPath =
                      currentOnboardingState.defaultAvatar;
                  _originalGeneratedAvatarColor =
                      currentOnboardingState.generatedAvatarColor;
                });

                // Navigate back to Profile Picture Page (Index 3)
                _animateToPage(_profilePicPageIndex);
              },
              child: Container(
                height: 150, // Adjust height as needed
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200], // Background color
                  borderRadius: BorderRadius.circular(12), // Rounded corners
                ),
                child: Stack(
                  // Use Stack to overlay edit icon
                  alignment: Alignment.center,
                  children: [
                    // --- Display Correct Avatar ---
                    if (state.avatarType == 'uploaded' &&
                        state.profileImage != null)
                      ClipRRect(
                        // Clip the image to match container border radius
                        borderRadius: BorderRadius.circular(12),
                        child: Image.file(
                          File(state.profileImage!.path),
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: 150,
                        ),
                      )
                    else if (state.avatarType == 'default' &&
                        state.defaultAvatar != null)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          state.defaultAvatar!,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: 150,
                        ),
                      )
                    else if (state.avatarType == 'generated') // Check type
                      generateAvatar(
                        state.fullName ?? '?', // Use name from state
                        radius: 60,
                        color:
                            state.generatedAvatarColor, // Use the STORED color
                      )
                    else // Fallback if somehow state is inconsistent
                      Icon(Icons.person, size: 80, color: Colors.grey),

                    // --- Edit Icon Overlay ---
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: .5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.edit, color: Colors.white, size: 18),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 16),

          // --- Rest of the Summary Items (Scrollable) ---
          Expanded(
            // Make the list scrollable
            child: ListView(
              // Use ListView instead of just Column
              children: [
                ListTile(
                  title: Text('Full Name'),
                  subtitle: Text(state.fullName ?? 'Not set'),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Name',
                    onPressed: () {
                      final currentOnboardingState =
                          ref.read(onboardingViewModelProvider).valueOrNull;
                      if (currentOnboardingState == null) return;
                      setState(() {
                        _isEditingFromSummary = true;
                        // --- Stash original values ---
                        _originalFullName = currentOnboardingState.fullName;
                        _originalUsername = currentOnboardingState.username;
                        // ---
                      });
                      _animateToPage(_nameUsernamePageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text('Username'),
                  subtitle: Text('@${state.username ?? 'Not set'}'),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Username',
                    onPressed: () {
                      final currentOnboardingState =
                          ref.read(onboardingViewModelProvider).valueOrNull;
                      if (currentOnboardingState == null) return;
                      setState(() {
                        _isEditingFromSummary = true;
                        // --- Stash original values ---
                        _originalFullName = currentOnboardingState.fullName;
                        _originalUsername = currentOnboardingState.username;
                        // ---
                      });
                      _animateToPage(_nameUsernamePageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Date of Birth"),
                  subtitle: Text(state.birthdate != null
                      ? '${state.birthdate!.toLocal()}'.split(' ')[0]
                      : 'Not set'), // Use null check
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Birthday',
                    onPressed: () {
                      setState(() {
                        _isEditingFromSummary = true;
                      });
                      _animateToPage(_dobGenderPageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Gender"),
                  subtitle: Text(state.gender ?? "Not set"),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Gender',
                    onPressed: () {
                      setState(() {
                        _isEditingFromSummary = true;
                      });
                      _animateToPage(_dobGenderPageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Current City"),
                  subtitle: Text(state.city ?? "Not yet selected"),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Location',
                    onPressed: () {
                      setState(() {
                        _isEditingFromSummary = true;
                      });
                      _animateToPage(_locationPageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text("Shared Activities"),
                  subtitle: Text(
                      state.sharedActivities?.join(", ") ?? 'Not yet added'),
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Shared Activities',
                    onPressed: () {
                      setState(() {
                        _isEditingFromSummary = true;
                      });
                      _animateToPage(_sharedActivitiesPageIndex);
                    },
                  ),
                ),
                ListTile(
                  title: Text("My Interests"),
                  subtitle: Text(state.myInterests?.join(", ") ??
                      'Not yet added'), // Updated placeholder
                  trailing: IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit My Interests',
                    onPressed: () {
                      setState(() {
                        _isEditingFromSummary = true;
                      });
                      _animateToPage(_myInterestsPageIndex);
                    },
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 20),

          // --- Navigation Buttons ---
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  viewModel.previousPage(); // Go back to My Interests (Index 6)
                  _animateToPage(state.currentPage - 1);
                },
                child: Text('Back'),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Refresh state one more time before completing onboarding
                  // This ensures we have the latest values
                  ref.invalidate(onboardingViewModelProvider);

                  // Complete onboarding
                  final result = await viewModel.completeOnboarding();
                  result.fold((l) {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(l.message),
                      backgroundColor: Colors.red,
                    ));
                  }, (r) {
                    // Navigate to home
                    if (mounted) {
                      context.beamToReplacementNamed(
                          '/home'); // Navigate using Beamer
                    }
                  });
                },
                child: state.isSaving
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ))
                    : const Text('Complete Onboarding'),
              ),
            ],
          )
        ],
      ),
    );
  }

  // Define _getStepTitle method if not already present
  String _getStepTitle(int currentPage) {
    // Adjust titles and indices based on your actual page setup
    // Example: 0:Welcome, 1:Name/User, 2:DOB/Gender, 3:Pic, 4:Location, 5:SharedActs, 6:Interests, 7:Summary
    switch (currentPage) {
      case 0:
        return 'Welcome to Watermelon!';
      case 1:
        return 'Your Name & Username (Step 2 of 8)';
      case 2:
        return 'About You (Step 3 of 8)';
      case 3:
        return 'Profile Picture (Step 4 of 8)';
      case 4:
        return 'Your Location (Step 5 of 8)';
      case 5:
        return 'Activities to Share (Step 6 of 8)';
      case 6:
        return 'Your Interests (Step 7 of 8)';
      case 7:
        return 'Review Your Profile (Step 8 of 8)';
      default:
        return 'Onboarding';
    }
  }
}
