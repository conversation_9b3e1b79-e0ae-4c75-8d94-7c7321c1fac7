// lib/features/onboarding/screens/onboarding_screen_refactored.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/errors.dart';
import 'package:watermelon_draft/features/onboarding/constants/page_indices.dart';
import 'package:watermelon_draft/features/onboarding/helpers/navigation_helper.dart';
import 'package:watermelon_draft/features/onboarding/models/edit_context.dart';
import 'package:watermelon_draft/features/onboarding/pages/name_username_page.dart';
import 'package:watermelon_draft/features/onboarding/pages/summary_page.dart';
import 'package:watermelon_draft/features/onboarding/viewmodels/onboarding_viewmodel.dart';

/// The main onboarding screen that manages the flow between different pages.
class OnboardingScreenRefactored extends ConsumerStatefulWidget {
  const OnboardingScreenRefactored({super.key});

  @override
  ConsumerState<OnboardingScreenRefactored> createState() => _OnboardingScreenRefactoredState();
}

class _OnboardingScreenRefactoredState extends ConsumerState<OnboardingScreenRefactored>
    with OnboardingNavigationHelper {
  // --- Controllers ---
  @override
  final PageController pageController = PageController();
  final _nameUsernameFormKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _cityController = TextEditingController();
  
  // --- Local State ---
  DateTime? _selectedDate;
  EditContext _editContext = const EditContext();

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing values from ViewModel
    final initialState = ref.read(onboardingViewModelProvider).valueOrNull;
    if (initialState != null) {
      _fullNameController.text = initialState.fullName ?? '';
      _usernameController.text = initialState.username ?? '';
      _selectedDate = initialState.birthdate;
      _cityController.text = initialState.city ?? '';
    }
  }

  @override
  void dispose() {
    pageController.dispose();
    _fullNameController.dispose();
    _usernameController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  // --- Implementation of OnboardingNavigationHelper ---
  @override
  OnboardingViewModel get viewModel => ref.read(onboardingViewModelProvider.notifier);

  @override
  EditContext get editContext => _editContext;

  @override
  void updateEditContext(EditContext newContext) {
    setState(() {
      _editContext = newContext;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen for errors
    ref.listen<AsyncValue<OnboardingState>>(onboardingViewModelProvider,
        (previous, next) {
      final currentOnboardingState = next.valueOrNull;
      // Check if saveError exists and has changed
      if (currentOnboardingState?.saveError != null &&
          (previous?.valueOrNull?.saveError != currentOnboardingState!.saveError ||
              previous?.valueOrNull?.isSaving == true &&
                  currentOnboardingState.isSaving == false) &&
          mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(currentOnboardingState.saveError!.message),
              backgroundColor: Colors.red),
        );
      }
    });

    // Watch the state to rebuild the UI
    final stateAsync = ref.watch(onboardingViewModelProvider);

    // Determine currentPage for AppBar logic safely
    final int currentPage = stateAsync.valueOrNull?.currentPage ?? 0;

    return Scaffold(
      appBar: AppBar(
        title: Text(_getStepTitle(currentPage)),
        automaticallyImplyLeading: false,

        // Show progress indicator in AppBar
        bottom: PreferredSize(
          preferredSize: const Size(double.infinity, 4.0),
          child: switch (stateAsync) {
            AsyncLoading() => const LinearProgressIndicator(value: null),
            AsyncData(:final value) => LinearProgressIndicator(
                value: (value.currentPage + 1) / OnboardingPageIndices.totalPages,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor),
              ),
            _ => const SizedBox(height: 4.0),
          },
        ),
      ),

      // Use pattern matching on the AsyncValue state
      body: switch (stateAsync) {
        AsyncData(:final value) => PageView(
            controller: pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: <Widget>[
              _buildWelcomePage(value),
              NameUsernamePage(
                state: value,
                viewModel: viewModel,
                editContext: editContext,
                onNavigate: animateToPage,
                onApplyEdits: applyEdits,
                onCancelEdits: cancelEdits,
                formKey: _nameUsernameFormKey,
                fullNameController: _fullNameController,
                usernameController: _usernameController,
              ),
              _buildDobGenderPage(value),
              _buildProfilePicturePage(value),
              _buildLocationPage(value),
              _buildSharedActivitiesPage(value),
              _buildMyInterestsPage(value),
              SummaryPage(
                state: value,
                viewModel: viewModel,
                editContext: editContext,
                onNavigate: animateToPage,
                onApplyEdits: applyEdits,
                onCancelEdits: cancelEdits,
                onStartEditing: startEditing,
                nameUsernamePageIndex: OnboardingPageIndices.nameUsername,
                dobGenderPageIndex: OnboardingPageIndices.dobGender,
                profilePicPageIndex: OnboardingPageIndices.profilePicture,
                locationPageIndex: OnboardingPageIndices.location,
                sharedActivitiesPageIndex: OnboardingPageIndices.sharedActivities,
                myInterestsPageIndex: OnboardingPageIndices.myInterests,
              ),
            ],
          ),
        AsyncError(:final error) => Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error loading onboarding: ${error is Failure ? error.message : error.toString()}'),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () => ref.invalidate(onboardingViewModelProvider),
                      child: const Text("Retry"),
                    ),
                  ],
                ),
              ),
            ),
        _ => const Center(child: CircularProgressIndicator()),
      },
    );
  }

  // --- Temporary placeholder methods for pages that haven't been refactored yet ---
  // These will be replaced with proper implementations in separate files

  Widget _buildWelcomePage(OnboardingState state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text('Welcome to Watermelon!', style: TextStyle(fontSize: 24)),
          const SizedBox(height: 20),
          const Text('Connect with others through shared activities.',
              textAlign: TextAlign.center),
          const SizedBox(height: 40),
          ElevatedButton(
            onPressed: () async {
              await animateToPage(OnboardingPageIndices.nameUsername);
            },
            child: const Text('Get Started'),
          ),
        ],
      ),
    );
  }

  Widget _buildDobGenderPage(OnboardingState state) {
    // Placeholder - will be replaced with proper implementation
    return Center(child: Text('Date of Birth & Gender Page'));
  }

  Widget _buildProfilePicturePage(OnboardingState state) {
    // Placeholder - will be replaced with proper implementation
    return Center(child: Text('Profile Picture Page'));
  }

  Widget _buildLocationPage(OnboardingState state) {
    // Placeholder - will be replaced with proper implementation
    return Center(child: Text('Location Page'));
  }

  Widget _buildSharedActivitiesPage(OnboardingState state) {
    // Placeholder - will be replaced with proper implementation
    return Center(child: Text('Shared Activities Page'));
  }

  Widget _buildMyInterestsPage(OnboardingState state) {
    // Placeholder - will be replaced with proper implementation
    return Center(child: Text('My Interests Page'));
  }

  // --- Helper methods ---

  String _getStepTitle(int currentPage) {
    switch (currentPage) {
      case OnboardingPageIndices.welcome:
        return 'Welcome to Watermelon!';
      case OnboardingPageIndices.nameUsername:
        return 'Your Name & Username (Step 2 of 8)';
      case OnboardingPageIndices.dobGender:
        return 'About You (Step 3 of 8)';
      case OnboardingPageIndices.profilePicture:
        return 'Profile Picture (Step 4 of 8)';
      case OnboardingPageIndices.location:
        return 'Your Location (Step 5 of 8)';
      case OnboardingPageIndices.sharedActivities:
        return 'Activities to Share (Step 6 of 8)';
      case OnboardingPageIndices.myInterests:
        return 'Your Interests (Step 7 of 8)';
      case OnboardingPageIndices.summary:
        return 'Review Your Profile (Step 8 of 8)';
      default:
        return 'Onboarding';
    }
  }
}
