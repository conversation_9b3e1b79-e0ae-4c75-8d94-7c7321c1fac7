# Data Flow in Refactored Onboarding

## Visual Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    OnboardingScreen                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  PageController │  │  EditContext    │  │ Form         │ │
│  │                 │  │                 │  │ Controllers  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                                                             │
│  with OnboardingNavigationHelper                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • animateToPage()    • startEditing()                   │ │
│  │ • applyEdits()       • cancelEdits()                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      PageView                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌────────┐ │
│  │ WelcomePage │ │ NameUsername│ │ SummaryPage │ │  ...   │ │
│  │             │ │    Page     │ │             │ │        │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 OnboardingViewModel                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • updateFullName()    • updateUsername()                │ │
│  │ • updateBirthdate()   • updateGender()                  │ │
│  │ • updateCity()        • updateSharedActivities()        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   OnboardingState                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ fullName, username, birthdate, gender, city,            │ │
│  │ sharedActivities, myInterests, profileImage, etc.       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Detailed Data Flow Scenarios

### Scenario 1: Normal Navigation (New User)

```
User Input → Page Widget → ViewModel Method → State Update → UI Rebuild

Example: User types name
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ User types      │    │ NameUsernamePage│    │ OnboardingVM    │
│ "John Doe"      │───▶│ onChanged:      │───▶│ updateFullName()│
│ in text field   │    │ viewModel.      │    │                 │
└─────────────────┘    │ updateFullName  │    └─────────────────┘
                       └─────────────────┘             │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ UI rebuilds     │◀───│ Riverpod        │◀───│ State updated   │
│ with new name   │    │ notifies        │    │ fullName =      │
│                 │    │ listeners       │    │ "John Doe"      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Scenario 2: Edit Flow (From Summary)

```
Step 1: Start Editing
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ User clicks     │    │ SummaryPage     │    │ OnboardingScreen│
│ "Edit" on       │───▶│ calls           │───▶│ startEditing()  │
│ Full Name       │    │ onStartEditing  │    │ (from mixin)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Navigate to     │◀───│ EditContext     │◀───│ Store original  │
│ NameUsername    │    │ updated         │    │ values:         │
│ Page            │    │                 │    │ {fullName: ".."}│
└─────────────────┘    └─────────────────┘    └─────────────────┘

Step 2: User Makes Changes
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ User changes    │    │ NameUsernamePage│    │ OnboardingVM    │
│ name to         │───▶│ Text controller │───▶│ updateFullName()│
│ "Jane Doe"      │    │ updates         │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

Step 3: Apply Changes
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ User clicks     │    │ NameUsernamePage│    │ OnboardingScreen│
│ "Update"        │───▶│ calls           │───▶│ applyEdits()    │
│                 │    │ onApplyEdits    │    │ (from mixin)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Navigate back   │◀───│ State updated   │◀───│ Apply new values│
│ to Summary      │    │ in ViewModel    │    │ to ViewModel    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

Step 4: Summary Updates
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ SummaryPage     │    │ Riverpod        │    │ EditContext     │
│ rebuilds with   │◀───│ triggers        │    │ reset to        │
│ "Jane Doe"      │    │ rebuild         │    │ not editing     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Component Communication Patterns

### 1. Parent → Child (Dependency Injection)
```dart
// OnboardingScreen passes data DOWN to pages
NameUsernamePage(
  state: currentState,           // Current data
  viewModel: viewModel,          // Business logic
  editContext: editContext,      // Edit state
  formKey: _formKey,            // Form controller
  onNavigate: animateToPage,     // Navigation callback
)
```

### 2. Child → Parent (Callbacks)
```dart
// Pages communicate UP to parent via callbacks
class NameUsernamePage {
  final Future<void> Function(int) onNavigate;
  final Future<void> Function({required Map<String, dynamic> Function()}) onApplyEdits;
  
  void handleUpdate() {
    onApplyEdits(getUpdatedValues: () => {
      'fullName': fullNameController.text,
      'username': usernameController.text,
    });
  }
}
```

### 3. State Management (Riverpod)
```dart
// All components watch the same state
final stateAsync = ref.watch(onboardingViewModelProvider);

// ViewModel updates trigger rebuilds everywhere
viewModel.updateFullName('New Name');
// ↓
// All widgets watching onboardingViewModelProvider rebuild
```

## Key Benefits of This Flow

### 1. **Unidirectional Data Flow**
```
User Input → ViewModel → State → UI Update
```
Data always flows in one direction, making it predictable.

### 2. **Centralized State**
```
All pages read from the same OnboardingState
All updates go through the same OnboardingViewModel
```

### 3. **Isolated Components**
```
Each page only knows about:
- Its own UI logic
- The callbacks it can call
- The state it receives

Each page doesn't know about:
- Other pages
- Navigation logic
- How state is stored
```

### 4. **Testable Architecture**
```dart
// Easy to test individual components
test('NameUsernamePage validates correctly', () {
  final page = NameUsernamePage(
    state: mockState,
    viewModel: mockViewModel,
    editContext: mockEditContext,
    onNavigate: mockNavigate,
    // ...
  );
  // Test just this page's logic
});
```

## Comparison with Old Architecture

### Old (Monolithic)
```
┌─────────────────────────────────────────┐
│           OnboardingScreen              │
│  ┌─────────────────────────────────────┐ │
│  │ All state variables                 │ │
│  │ All navigation logic                │ │
│  │ All page builders                   │ │
│  │ All form controllers                │ │
│  │ All validation logic                │ │
│  │ All edit mode handling              │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```
**Problem:** Everything is coupled together.

### New (Modular)
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│ OnboardingScreen│  │ NameUsernamePage│  │ SummaryPage     │
│ (Coordinator)   │  │ (Component)     │  │ (Component)     │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │ OnboardingVM    │
                    │ (State Manager) │
                    └─────────────────┘
```
**Benefit:** Each component has a single responsibility.
