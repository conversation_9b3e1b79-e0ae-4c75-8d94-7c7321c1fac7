import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:watermelon_draft/core/providers.dart';
import 'package:watermelon_draft/core/routes.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:watermelon_draft/features/onboarding/screens/onboarding_screen_refactored.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  final container = ProviderContainer(); // Create a ProviderContainer

  // 1. Initialize SharedPreferences EAGERLY:
  await container.read(sharedPreferencesProvider.future);

  // 2. Initialize Supabase:
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL'] ?? '',
    anonKey: dotenv.env['SUPABASE_ANON_KEY'] ?? '',
  );

  runApp(ProviderScope(
    // Wrap with ProviderScope
    child: MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
        routeInformationParser: BeamerParser(), // Add BeamerParser
        routerDelegate: routerDelegate, // Use your BeamerDelegate
        backButtonDispatcher: BeamerBackButtonDispatcher(
            delegate: routerDelegate) // Fix back button issue.
        );
    //   return Scaffold(
    //     body: OnboardingScreenRefactored(),
    //   );
  }
}
