# Freezed Package - Complete Guide

## 🎯 **What is Freezed?**

Freezed is a **code generation package** that automatically creates immutable data classes with tons of useful features. Think of it as a "super-powered class generator" that writes boilerplate code for you!

### **What Freezed Generates For You**

When you write a simple Freezed class, it automatically generates:

- ✅ **Immutable data class** with all properties final
- ✅ **Constructor** with named parameters
- ✅ **copyWith() method** for creating modified copies
- ✅ **toString() method** with readable output
- ✅ **operator ==** and **hashCode** for value equality
- ✅ **JSON serialization** (toJson/fromJson) when combined with json_annotation
- ✅ **Union types** (sealed classes) for state management
- ✅ **Pattern matching** capabilities

### **Why Use Freezed?**

```dart
// ❌ Without Freezed - you'd write ALL this manually:
class User {
  final String id;
  final String name;
  final String? email;
  final AppLocation? location;
  
  const User({
    required this.id,
    required this.name,
    this.email,
    this.location,
  });
  
  // Manual copyWith method
  User copyWith({
    String? id,
    String? name,
    String? email,
    AppLocation? location,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      location: location ?? this.location,
    );
  }
  
  // Manual toString
  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, location: $location)';
  }
  
  // Manual equality
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.location == location;
  }
  
  // Manual hashCode
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        location.hashCode;
  }
  
  // Manual JSON methods
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'location': location?.toJson(),
    };
  }
  
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      location: json['location'] != null 
          ? AppLocation.fromJson(json['location']) 
          : null,
    );
  }
}

// ✅ With Freezed - you write THIS and get everything above for FREE:
@freezed
abstract class User with _$User {
  const factory User({
    required String id,
    required String name,
    String? email,
    AppLocation? location,
  }) = _User;
  
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
```

## 🔧 **Basic Freezed Syntax & Structure**

### **1. Basic Freezed Class**

```dart
// Step 1: Import required packages
import 'package:freezed_annotation/freezed_annotation.dart';

// Step 2: Declare the generated files (these will be created by build_runner)
part 'user.freezed.dart';           // Contains the generated class implementation
part 'user.g.dart';                 // Contains JSON serialization (if using json_annotation)

// Step 3: Define your Freezed class
@freezed                             // This annotation tells Freezed to generate code
abstract class User with _$User {    // 'with _$User' gives access to generated methods
  const factory User({               // Factory constructor defines the structure
    required String id,              // Required field
    required String name,            // Required field
    String? email,                   // Optional field (nullable)
    @Default('active') String status, // Optional field with default value
    AppLocation? location,           // Optional custom object
  }) = _User;                        // '= _User' is the implementation class name
  
  // JSON serialization factory (optional)
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
```

### **2. Understanding the Generated Files**

When you run `dart run build_runner build`, Freezed creates:

#### **user.freezed.dart** - The Main Implementation
```dart
// This file contains the actual class implementation
class _$_User implements _User {
  const _$_User({
    required this.id,
    required this.name,
    this.email,
    this.status = 'active',
    this.location,
  });

  @override
  final String id;
  @override
  final String name;
  @override
  final String? email;
  @override
  final String status;
  @override
  final AppLocation? location;

  // Generated toString, operator ==, hashCode, copyWith methods...
}
```

#### **user.g.dart** - JSON Serialization
```dart
// This file contains JSON serialization methods
User _$UserFromJson(Map<String, dynamic> json) {
  return _User(
    id: json['id'] as String,
    name: json['name'] as String,
    email: json['email'] as String?,
    status: json['status'] as String? ?? 'active',
    location: json['location'] == null
        ? null
        : AppLocation.fromJson(json['location'] as Map<String, dynamic>),
  );
}

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'status': instance.status,
      'location': instance.location,
    };
```

## 🎯 **Freezed Features Explained**

### **1. Immutability & copyWith()**

```dart
// Create a user
final user = User(
  id: '123',
  name: 'John Doe',
  email: '<EMAIL>',
);

// ❌ This won't work - properties are final (immutable)
// user.name = 'Jane Doe'; // Compilation error!

// ✅ Use copyWith to create a modified copy
final updatedUser = user.copyWith(
  name: 'Jane Doe',           // Change the name
  email: '<EMAIL>',  // Change the email
  // id stays the same        // Other properties remain unchanged
);

print(user.name);        // 'John Doe' (original unchanged)
print(updatedUser.name); // 'Jane Doe' (new copy with changes)
```

### **2. Default Values**

```dart
@freezed
abstract class AppLocation with _$AppLocation {
  const factory AppLocation({
    required double latitude,
    required double longitude,
    double? accuracy,
    DateTime? timestamp,
    @Default(AppLocationSource.unknown) AppLocationSource source, // Default value
  }) = _AppLocation;
  
  factory AppLocation.fromJson(Map<String, dynamic> json) => _$AppLocationFromJson(json);
}

// Usage
final location1 = AppLocation(latitude: 37.7749, longitude: -122.4194);
print(location1.source); // AppLocationSource.unknown (default value used)

final location2 = AppLocation(
  latitude: 37.7749, 
  longitude: -122.4194,
  source: AppLocationSource.gps, // Explicit value
);
print(location2.source); // AppLocationSource.gps
```

### **3. Custom JSON Converters**

```dart
@freezed
abstract class User with _$User {
  const factory User({
    required String id,
    required String name,
    String? email,
    // Custom JSON converter for complex objects
    @JsonKey(
      name: 'location',                                    // JSON field name
      fromJson: appLocationFromJson,                       // Custom deserializer
      toJson: appLocationToJson,                          // Custom serializer
    )
    AppLocation? location,
    
    // Custom converter for DateTime
    @JsonKey(
      name: 'created_at',
      fromJson: _dateTimeFromJson,
      toJson: _dateTimeToJson,
    )
    DateTime? createdAt,
  }) = _User;
  
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

// Custom converter functions
DateTime? _dateTimeFromJson(String? json) {
  return json != null ? DateTime.parse(json) : null;
}

String? _dateTimeToJson(DateTime? dateTime) {
  return dateTime?.toIso8601String();
}
```

### **4. Multiple Constructors (Union Types)**

```dart
// This is SUPER powerful for state management!
@freezed
abstract class LoadingState with _$LoadingState {
  // Different "states" of loading
  const factory LoadingState.initial() = _Initial;
  const factory LoadingState.loading() = _Loading;
  const factory LoadingState.success(String data) = _Success;
  const factory LoadingState.error(String message) = _Error;
  
  factory LoadingState.fromJson(Map<String, dynamic> json) => _$LoadingStateFromJson(json);
}

// Usage with pattern matching
LoadingState state = LoadingState.loading();

// Pattern matching (like switch statements but better)
String message = state.when(
  initial: () => 'Ready to start',
  loading: () => 'Loading...',
  success: (data) => 'Success: $data',
  error: (message) => 'Error: $message',
);

// Or use maybeWhen for partial matching
state.maybeWhen(
  success: (data) => print('Got data: $data'),
  error: (message) => print('Error occurred: $message'),
  orElse: () => print('Still loading or initial'),
);

// Or use map for more complex logic
Widget buildWidget() {
  return state.map(
    initial: (_) => Text('Tap to start'),
    loading: (_) => CircularProgressIndicator(),
    success: (successState) => Text('Data: ${successState.data}'),
    error: (errorState) => Text('Error: ${errorState.message}'),
  );
}
```

## 🚀 **Real-World AppLocation Example**

Let's break down your AppLocation class:

```dart
// 1. Imports and file declarations
import 'dart:math' as math;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:mapbox_search/mapbox_search.dart' as mapbox;

part 'app_location.freezed.dart';  // Generated implementation
part 'app_location.g.dart';        // Generated JSON methods

// 2. Main Freezed class
@freezed
abstract class AppLocation with _$AppLocation {
  const factory AppLocation({
    /// Latitude in decimal degrees (-90 to 90)
    required double latitude,        // Required field - must be provided
    
    /// Longitude in decimal degrees (-180 to 180)  
    required double longitude,       // Required field - must be provided
    
    /// Optional accuracy in meters (for GPS-derived locations)
    double? accuracy,                // Optional field - can be null
    
    /// Optional timestamp when location was captured
    DateTime? timestamp,             // Optional field - can be null
    
    /// Optional source of the location data
    @Default(AppLocationSource.unknown) AppLocationSource source, // Has default value
  }) = _AppLocation;                 // Implementation class name

  // 3. JSON serialization factory
  factory AppLocation.fromJson(Map<String, dynamic> json) => _$AppLocationFromJson(json);
  
  // 4. Named constructors for convenience
  factory AppLocation.fromGps({
    required double latitude,
    required double longitude,
    double? accuracy,
    DateTime? timestamp,
  }) {
    return AppLocation(
      latitude: latitude,
      longitude: longitude,
      accuracy: accuracy,
      timestamp: timestamp ?? DateTime.now(),
      source: AppLocationSource.gps,  // Specific source for GPS
    );
  }
  
  factory AppLocation.fromGeocoding({
    required double latitude,
    required double longitude,
  }) {
    return AppLocation(
      latitude: latitude,
      longitude: longitude,
      source: AppLocationSource.geocoding,  // Specific source for geocoding
    );
  }
}

// 5. Enum for location source
enum AppLocationSource {
  unknown,
  gps,
  geocoding,
  userInput,
  mapbox,
  osm,
}
```

### **What Freezed Generates for AppLocation**

```dart
// Usage examples of generated methods:

// 1. Constructor
final location = AppLocation(
  latitude: 37.7749,
  longitude: -122.4194,
  // source automatically gets AppLocationSource.unknown (default)
);

// 2. copyWith method
final updatedLocation = location.copyWith(
  accuracy: 10.0,                    // Add accuracy
  timestamp: DateTime.now(),         // Add timestamp
  source: AppLocationSource.gps,     // Change source
  // latitude and longitude stay the same
);

// 3. toString method
print(location.toString());
// Output: AppLocation(latitude: 37.7749, longitude: -122.4194, accuracy: null, timestamp: null, source: AppLocationSource.unknown)

// 4. Equality comparison
final location1 = AppLocation(latitude: 37.7749, longitude: -122.4194);
final location2 = AppLocation(latitude: 37.7749, longitude: -122.4194);
print(location1 == location2); // true (same values)

// 5. JSON serialization
Map<String, dynamic> json = location.toJson();
AppLocation fromJson = AppLocation.fromJson(json);

// 6. Named constructors
final gpsLocation = AppLocation.fromGps(
  latitude: 37.7749,
  longitude: -122.4194,
  accuracy: 5.0,
);
print(gpsLocation.source); // AppLocationSource.gps
```

## 🔧 **Build Runner Commands**

To generate the Freezed files, you need to run:

```bash
# Generate files once
dart run build_runner build

# Generate files and delete conflicting outputs
dart run build_runner build --delete-conflicting-outputs

# Watch for changes and regenerate automatically
dart run build_runner watch

# Clean generated files
dart run build_runner clean
```

## 🎯 **Best Practices**

### **1. File Organization**

```dart
// ✅ Good: Keep related classes in the same file
// user.dart
@freezed
abstract class User with _$User { }

@freezed  
abstract class UserPreferences with _$UserPreferences { }

enum UserRole { admin, user, guest }
```

### **2. Documentation**

```dart
@freezed
abstract class AppLocation with _$AppLocation {
  /// Represents a geographic location with latitude and longitude coordinates.
  /// 
  /// This class is immutable and provides methods for distance calculations,
  /// validation, and serialization to various formats (GeoJSON, PostGIS).
  /// 
  /// Example:
  /// ```dart
  /// final location = AppLocation(latitude: 37.7749, longitude: -122.4194);
  /// if (location.isValid) {
  ///   print('Valid location: ${location.displayString}');
  /// }
  /// ```
  const factory AppLocation({
    /// Latitude in decimal degrees (-90 to 90)
    required double latitude,
    
    /// Longitude in decimal degrees (-180 to 180)
    required double longitude,
    
    // ... other fields
  }) = _AppLocation;
}
```

### **3. Validation**

```dart
// Add validation in extension methods, not in the Freezed class
extension AppLocationValidation on AppLocation {
  bool get isValid {
    return latitude >= -90 && latitude <= 90 &&
           longitude >= -180 && longitude <= 180;
  }
  
  List<String> get validationErrors {
    final errors = <String>[];
    if (latitude < -90 || latitude > 90) {
      errors.add('Latitude must be between -90 and 90');
    }
    if (longitude < -180 || longitude > 180) {
      errors.add('Longitude must be between -180 and 180');
    }
    return errors;
  }
}
```

## 🎉 **Summary**

Freezed is a **productivity superpower** that:

- ✅ **Eliminates boilerplate** - writes hundreds of lines of code for you
- ✅ **Ensures immutability** - prevents accidental state mutations
- ✅ **Provides copyWith** - makes updating immutable objects easy
- ✅ **Handles JSON automatically** - serialization just works
- ✅ **Supports union types** - perfect for state management
- ✅ **Generates perfect equality** - value-based comparison out of the box

Once you understand Freezed, you'll never want to write manual data classes again! 🌟
