# Extension Methods in Dart - Complete Guide

## 🎯 **What Are Extension Methods?**

Extension methods allow you to add new functionality to existing classes **without modifying their source code**. Think of them as a way to "bolt on" additional methods and properties to any class, including:

- ✅ **Your own classes** (like Freezed models)
- ✅ **Third-party classes** (like String, List, DateTime)
- ✅ **Flutter widgets** (like Widget, BuildContext)
- ✅ **Package classes** (like GeoPoint from flutter_osm_plugin)

### **Why Extension Methods Are Awesome**

```dart
// ❌ Without extensions - ugly utility classes
class StringUtils {
  static bool isValidEmail(String email) => email.contains('@');
  static String capitalize(String text) => text[0].toUpperCase() + text.substring(1);
}

// Usage feels disconnected from the data
bool valid = StringUtils.isValidEmail(userEmail);
String title = StringUtils.capitalize(userName);

// ✅ With extensions - clean and intuitive
extension StringExtensions on String {
  bool get isValidEmail => contains('@');
  String get capitalized => this[0].toUpperCase() + substring(1);
}

// Usage feels natural and discoverable
bool valid = userEmail.isValidEmail;  // Reads like English!
String title = userName.capitalized;   // IDE autocomplete works!
```

## 🔧 **Basic Syntax & Structure**

### **1. Basic Extension Declaration**

```dart
// Basic structure
extension ExtensionName on TargetClass {
  // Your new methods and properties go here
}

// Real example from AppLocation
extension AppLocationExtensions on AppLocation {
  // Add validation method to AppLocation
  bool get isValid {
    // 'this' refers to the AppLocation instance
    return this.latitude >= -90 && 
           this.latitude <= 90 && 
           this.longitude >= -180 && 
           this.longitude <= 180;
  }
}
```

### **2. Using Extensions**

```dart
// Create an AppLocation instance
final location = AppLocation(latitude: 37.7749, longitude: -122.4194);

// Use the extension method - feels like a native property!
if (location.isValid) {
  print('Location is valid!');
}
// The IDE will show 'isValid' in autocomplete as if it's part of AppLocation!
```

## 🎯 **Types of Extension Members**

### **1. Getter Properties (Computed Values)**

```dart
extension AppLocationExtensions on AppLocation {
  /// Check if coordinates are within valid Earth ranges
  bool get isValid {
    // 'this' is optional - can access properties directly
    return latitude >= -90 && 
           latitude <= 90 && 
           longitude >= -180 && 
           longitude <= 180;
  }
  
  /// Create a human-readable display string
  String get displayString {
    return 'Lat: ${latitude.toStringAsFixed(6)}, '
           'Lng: ${longitude.toStringAsFixed(6)}';
  }
  
  /// Get the hemisphere information
  String get hemisphere {
    final ns = latitude >= 0 ? 'North' : 'South';
    final ew = longitude >= 0 ? 'East' : 'West';
    return '$ns $ew';
  }
}

// Usage - these feel like properties of AppLocation
print(location.displayString);  // "Lat: 37.774900, Lng: -122.419400"
print(location.hemisphere);     // "North West"
```

### **2. Methods (Functions with Parameters)**

```dart
extension AppLocationExtensions on AppLocation {
  /// Calculate distance to another location using Haversine formula
  double distanceTo(AppLocation other) {
    const double earthRadius = 6371000; // Earth radius in meters
    
    // Convert degrees to radians
    final double lat1Rad = latitude * (math.pi / 180);
    final double lat2Rad = other.latitude * (math.pi / 180);
    final double deltaLatRad = (other.latitude - latitude) * (math.pi / 180);
    final double deltaLngRad = (other.longitude - longitude) * (math.pi / 180);
    
    // Haversine formula
    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    final double c = 2 * math.asin(math.sqrt(a));
    
    return earthRadius * c; // Distance in meters
  }
  
  /// Calculate distance in kilometers (convenience method)
  double distanceToKm(AppLocation other) {
    return distanceTo(other) / 1000; // Convert meters to kilometers
  }
  
  /// Check if this location is within a certain radius of another
  bool isWithinRadius(AppLocation center, double radiusMeters) {
    return distanceTo(center) <= radiusMeters;
  }
}

// Usage - these feel like methods of AppLocation
final userLocation = AppLocation(latitude: 37.7749, longitude: -122.4194);
final eventLocation = AppLocation(latitude: 37.7849, longitude: -122.4094);

double distance = userLocation.distanceTo(eventLocation);     // 1234.56 meters
double distanceKm = userLocation.distanceToKm(eventLocation); // 1.23 km
bool nearby = userLocation.isWithinRadius(eventLocation, 2000); // true if within 2km
```

### **3. Static Methods (Class-Level Functions)**

```dart
extension AppLocationExtensions on AppLocation {
  /// Create AppLocation from GeoJSON format (static factory method)
  static AppLocation? fromGeoJson(Map<String, dynamic>? json) {
    // Static methods don't have access to 'this' - they work on the class level
    if (json == null || 
        json['coordinates'] == null || 
        (json['coordinates'] as List).length < 2) {
      return null;
    }
    
    try {
      final List<dynamic> coords = json['coordinates'] as List<dynamic>;
      return AppLocation(
        latitude: (coords[1] as num).toDouble(),   // GeoJSON: [lng, lat]
        longitude: (coords[0] as num).toDouble(),
        source: AppLocationSource.unknown,
      );
    } catch (e) {
      print("Error parsing AppLocation from GeoJSON: $json, Error: $e");
      return null;
    }
  }
  
  /// Create a random location within bounds (useful for testing)
  static AppLocation randomLocation({
    double minLat = -90,
    double maxLat = 90,
    double minLng = -180,
    double maxLng = 180,
  }) {
    final random = math.Random();
    return AppLocation(
      latitude: minLat + random.nextDouble() * (maxLat - minLat),
      longitude: minLng + random.nextDouble() * (maxLng - minLng),
      source: AppLocationSource.unknown,
    );
  }
}

// Usage - call on the class name, not an instance
AppLocation? location = AppLocationExtensions.fromGeoJson(geoJsonData);
AppLocation randomLoc = AppLocationExtensions.randomLocation();
```

## 🚀 **Real-World AppLocation Examples**

### **Serialization Extensions**

```dart
extension AppLocationSerialization on AppLocation {
  /// Convert to GeoJSON format for PostGIS storage
  Map<String, dynamic> toGeoJson() {
    if (!isValid) {
      throw ArgumentError('Invalid coordinates: lat=$latitude, lng=$longitude');
    }
    
    return {
      'type': 'Point',
      'coordinates': [longitude, latitude], // GeoJSON format: [lng, lat]
    };
  }
  
  /// Convert to PostGIS POINT format string
  String toPostGisPoint() {
    if (!isValid) {
      throw ArgumentError('Invalid coordinates: lat=$latitude, lng=$longitude');
    }
    
    return 'POINT($longitude $latitude)'; // PostGIS format: POINT(lng lat)
  }
  
  /// Convert to Google Maps URL
  String toGoogleMapsUrl() {
    return 'https://www.google.com/maps?q=$latitude,$longitude';
  }
}

// Usage
final location = AppLocation(latitude: 37.7749, longitude: -122.4194);
Map<String, dynamic> geoJson = location.toGeoJson();
String postGis = location.toPostGisPoint();
String mapsUrl = location.toGoogleMapsUrl();
```

### **Validation Extensions**

```dart
extension AppLocationValidation on AppLocation {
  /// Check if location is on land (simplified - you'd use a real service)
  bool get isOnLand {
    // Simplified check - real implementation would use a geocoding service
    return latitude.abs() < 85; // Rough approximation
  }
  
  /// Check if location is in a specific country (simplified)
  bool isInCountry(String countryCode) {
    // In real app, you'd use a geocoding service
    // This is just an example
    switch (countryCode.toUpperCase()) {
      case 'US':
        return latitude >= 24.396308 && latitude <= 49.384358 &&
               longitude >= -125.0 && longitude <= -66.93457;
      // Add more countries as needed
      default:
        return false;
    }
  }
  
  /// Validate coordinates with custom error messages
  ValidationResult validate() {
    if (latitude < -90 || latitude > 90) {
      return ValidationResult.error('Latitude must be between -90 and 90');
    }
    if (longitude < -180 || longitude > 180) {
      return ValidationResult.error('Longitude must be between -180 and 180');
    }
    return ValidationResult.success();
  }
}

// Helper class for validation results
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  
  ValidationResult.success() : isValid = true, errorMessage = null;
  ValidationResult.error(this.errorMessage) : isValid = false;
}

// Usage
if (location.isOnLand) {
  print('Location is on land');
}

ValidationResult result = location.validate();
if (!result.isValid) {
  print('Invalid location: ${result.errorMessage}');
}
```

## 🎯 **Extension Methods vs Other Approaches**

### **❌ Static Utility Classes (Old Way)**

```dart
class LocationUtils {
  static bool isValid(AppLocation location) { /* ... */ }
  static double distanceBetween(AppLocation a, AppLocation b) { /* ... */ }
  static String formatLocation(AppLocation location) { /* ... */ }
}

// Usage is disconnected and hard to discover
bool valid = LocationUtils.isValid(myLocation);
double distance = LocationUtils.distanceBetween(loc1, loc2);
String formatted = LocationUtils.formatLocation(myLocation);
```

### **✅ Extension Methods (Modern Way)**

```dart
extension AppLocationExtensions on AppLocation {
  bool get isValid { /* ... */ }
  double distanceTo(AppLocation other) { /* ... */ }
  String get formatted { /* ... */ }
}

// Usage is intuitive and discoverable
bool valid = myLocation.isValid;           // Reads naturally
double distance = loc1.distanceTo(loc2);   // Clear relationship
String formatted = myLocation.formatted;   // IDE autocomplete
```

## 🔧 **Best Practices**

### **1. Naming Conventions**

```dart
// ✅ Good: Descriptive extension names
extension AppLocationExtensions on AppLocation { }
extension AppLocationValidation on AppLocation { }
extension AppLocationSerialization on AppLocation { }

// ❌ Avoid: Generic names
extension Utils on AppLocation { }
extension Helpers on AppLocation { }
```

### **2. Logical Grouping**

```dart
// ✅ Group related functionality
extension AppLocationDistance on AppLocation {
  double distanceTo(AppLocation other) { }
  double distanceToKm(AppLocation other) { }
  bool isWithinRadius(AppLocation center, double radius) { }
}

extension AppLocationValidation on AppLocation {
  bool get isValid { }
  bool get isOnLand { }
  ValidationResult validate() { }
}
```

### **3. Documentation**

```dart
extension AppLocationExtensions on AppLocation {
  /// Calculate the great-circle distance between two points on Earth
  /// 
  /// Uses the Haversine formula to calculate the shortest distance
  /// between two points on the surface of a sphere.
  /// 
  /// Returns the distance in meters.
  /// 
  /// Example:
  /// ```dart
  /// final sf = AppLocation(latitude: 37.7749, longitude: -122.4194);
  /// final ny = AppLocation(latitude: 40.7128, longitude: -74.0060);
  /// double distance = sf.distanceTo(ny); // ~4135000 meters
  /// ```
  double distanceTo(AppLocation other) {
    // Implementation...
  }
}
```

## 🎉 **Summary**

Extension methods are a **game-changer** for creating clean, discoverable APIs! They let you:

- ✅ **Add methods to any class** without modifying source code
- ✅ **Create intuitive APIs** that read like natural language
- ✅ **Leverage IDE autocomplete** for better developer experience
- ✅ **Organize code logically** by grouping related functionality
- ✅ **Extend third-party classes** (Freezed models, Flutter widgets, etc.)

In your AppLocation example, extensions transform a simple data class into a powerful, feature-rich location toolkit that feels natural to use! 🌟
